/* ambiance theme for codemirror */

/* Color scheme */

.cm-s-ambiance .cm-keyword {
	color: #cda869;
}

.cm-s-ambiance .cm-atom {
	color: #CF7EA9;
}

.cm-s-ambiance .cm-number {
	color: #78CF8A;
}

.cm-s-ambiance .cm-def {
	color: #aac6e3;
}

.cm-s-ambiance .cm-variable {
	color: #ffb795;
}

.cm-s-ambiance .cm-variable-2 {
	color: #eed1b3;
}

.cm-s-ambiance .cm-variable-3 {
	color: #faded3;
}

.cm-s-ambiance .cm-property {
	color: #eed1b3;
}

.cm-s-ambiance .cm-operator {
	color: #fa8d6a;
}

.cm-s-ambiance .cm-comment {
	color: #555;
	font-style: italic;
}

.cm-s-ambiance .cm-string {
	color: #8f9d6a;
}

.cm-s-ambiance .cm-string-2 {
	color: #9d937c;
}

.cm-s-ambiance .cm-meta {
	color: #D2A8A1;
}

.cm-s-ambiance .cm-qualifier {
	color: yellow;
}

.cm-s-ambiance .cm-builtin {
	color: #9999cc;
}

.cm-s-ambiance .cm-bracket {
	color: #24C2C7;
}

.cm-s-ambiance .cm-tag {
	color: #fee4ff
}

.cm-s-ambiance .cm-attribute {
	color: #9B859D;
}

.cm-s-ambiance .cm-header {
	color: blue;
}

.cm-s-ambiance .cm-quote {
	color: #24C2C7;
}

.cm-s-ambiance .cm-hr {
	color: pink;
}

.cm-s-ambiance .cm-link {
	color: #F4C20B;
}

.cm-s-ambiance .cm-special {
	color: #FF9D00;
}

.cm-s-ambiance .cm-error {
	color: #AF2018;
}

.cm-s-ambiance .CodeMirror-matchingbracket {
	color: #0f0;
}

.cm-s-ambiance .CodeMirror-nonmatchingbracket {
	color: #f22;
}

.cm-s-ambiance .CodeMirror-selected {
	background: rgba(255, 255, 255, 0.15);
}

.cm-s-ambiance.CodeMirror-focused .CodeMirror-selected {
	background: rgba(255, 255, 255, 0.10);
}

/* Editor styling */

.cm-s-ambiance.CodeMirror {
	line-height: 1.40em;
	color: #E6E1DC;
	background-color: #202020;
	-webkit-box-shadow: inset 0 0 10px black;
	-moz-box-shadow: inset 0 0 10px black;
	box-shadow: inset 0 0 10px black;
}

.cm-s-ambiance .CodeMirror-gutters {
	background: #3D3D3D;
	border-right: 1px solid #4D4D4D;
	box-shadow: 0 10px 20px black;
}

.cm-s-ambiance .CodeMirror-linenumber {
	text-shadow: 0px 1px 1px #4d4d4d;
	color: #222;
	padding: 0 5px;
}

.cm-s-ambiance .CodeMirror-lines .CodeMirror-cursor {
	border-left: 1px solid #7991E8;
}

.cm-s-ambiance .CodeMirror-activeline-background {
	background: none repeat scroll 0% 0% rgba(255, 255, 255, 0.031);
}

.cm-s-ambiance.CodeMirror,
.cm-s-ambiance .CodeMirror-gutters {
	background-image: url("data:image/png;base64,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");
}

/* BASICS */

.CodeMirror {
	/* Set height, width, borders, and global font properties here */
	font-family: monospace;
	height: 300px;
}

.CodeMirror-scroll {
	/* Set scrolling behaviour here */
	overflow: auto;
}

/* PADDING */

.CodeMirror-lines {
	padding: 4px 0;
	/* Vertical padding around content */
}

.CodeMirror pre {
	padding: 0 4px;
	/* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler,
.CodeMirror-gutter-filler {
	background-color: white;
	/* The little square between H and V scrollbars */
}

/* GUTTER */

.CodeMirror-gutters {
	border-right: 1px solid #ddd;
	background-color: #f7f7f7;
	white-space: nowrap;
}

.CodeMirror-linenumbers {}

.CodeMirror-linenumber {
	padding: 0 3px 0 5px;
	min-width: 20px;
	text-align: right;
	color: #999;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}

/* CURSOR */

.CodeMirror div.CodeMirror-cursor {
	border-left: 1px solid black;
}

/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
	border-left: 1px solid silver;
}

.CodeMirror.cm-keymap-fat-cursor div.CodeMirror-cursor {
	width: auto;
	border: 0;
	background: #7e7;
}

/* Can style cursor different in overwrite (non-insert) mode */
div.CodeMirror-overwrite div.CodeMirror-cursor {}

.cm-tab {
	display: inline-block;
}

.CodeMirror-ruler {
	border-left: 1px solid #ccc;
	position: absolute;
}

/* DEFAULT THEME */

.cm-s-default .cm-keyword {
	color: #708;
}

.cm-s-default .cm-atom {
	color: #219;
}

.cm-s-default .cm-number {
	color: #164;
}

.cm-s-default .cm-def {
	color: #00f;
}

.cm-s-default .cm-variable,
.cm-s-default .cm-punctuation,
.cm-s-default .cm-property,
.cm-s-default .cm-operator {}

.cm-s-default .cm-variable-2 {
	color: #05a;
}

.cm-s-default .cm-variable-3 {
	color: #085;
}

.cm-s-default .cm-comment {
	color: #a50;
}

.cm-s-default .cm-string {
	color: #a11;
}

.cm-s-default .cm-string-2 {
	color: #f50;
}

.cm-s-default .cm-meta {
	color: #555;
}

.cm-s-default .cm-qualifier {
	color: #555;
}

.cm-s-default .cm-builtin {
	color: #30a;
}

.cm-s-default .cm-bracket {
	color: #997;
}

.cm-s-default .cm-tag {
	color: #170;
}

.cm-s-default .cm-attribute {
	color: #00c;
}

.cm-s-default .cm-header {
	color: blue;
}

.cm-s-default .cm-quote {
	color: #090;
}

.cm-s-default .cm-hr {
	color: #999;
}

.cm-s-default .cm-link {
	color: #00c;
}

.cm-negative {
	color: #d44;
}

.cm-positive {
	color: #292;
}

.cm-header,
.cm-strong {
	font-weight: bold;
}

.cm-em {
	font-style: italic;
}

.cm-link {
	text-decoration: underline;
}

.cm-s-default .cm-error {
	color: #f00;
}

.cm-invalidchar {
	color: #f00;
}

div.CodeMirror span.CodeMirror-matchingbracket {
	color: #0f0;
}

div.CodeMirror span.CodeMirror-nonmatchingbracket {
	color: #f22;
}

.CodeMirror-activeline-background {
	background: #e8f2ff;
}

/* STOP */

/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */

.CodeMirror {
	line-height: 1;
	position: relative;
	overflow: hidden;
	background: white;
	color: black;
}

.CodeMirror-scroll {
	/* 30px is the magic margin used to hide the element's real scrollbars */
	/* See overflow: hidden in .CodeMirror */
	margin-bottom: -30px;
	margin-right: -30px;
	padding-bottom: 30px;
	height: 100%;
	outline: none;
	/* Prevent dragging from highlighting the element */
	position: relative;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}

.CodeMirror-sizer {
	position: relative;
	border-right: 30px solid transparent;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actuall scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar,
.CodeMirror-hscrollbar,
.CodeMirror-scrollbar-filler,
.CodeMirror-gutter-filler {
	position: absolute;
	z-index: 6;
	display: none;
}

.CodeMirror-vscrollbar {
	right: 0;
	top: 0;
	overflow-x: hidden;
	overflow-y: scroll;
}

.CodeMirror-hscrollbar {
	bottom: 0;
	left: 0;
	overflow-y: hidden;
	overflow-x: scroll;
}

.CodeMirror-scrollbar-filler {
	right: 0;
	bottom: 0;
}

.CodeMirror-gutter-filler {
	left: 0;
	bottom: 0;
}

.CodeMirror-gutters {
	position: absolute;
	left: 0;
	top: 0;
	padding-bottom: 30px;
	z-index: 3;
}

.CodeMirror-gutter {
	white-space: normal;
	height: 100%;
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	padding-bottom: 30px;
	margin-bottom: -32px;
	display: inline-block;
	/* Hack to make IE7 behave */
	*zoom: 1;
	*display: inline;
}

.CodeMirror-gutter-elt {
	position: absolute;
	cursor: default;
	z-index: 4;
}

.CodeMirror-lines {
	cursor: text;
}

.CodeMirror pre {
	/* Reset some styles that the rest of the page might have set */
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	border-width: 0;
	background: transparent;
	font-family: inherit;
	font-size: inherit;
	margin: 0;
	white-space: pre;
	word-wrap: normal;
	line-height: inherit;
	color: inherit;
	z-index: 2;
	position: relative;
	overflow: visible;
}

.CodeMirror-wrap pre {
	word-wrap: break-word;
	white-space: pre-wrap;
	word-break: normal;
}

.CodeMirror-linebackground {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 0;
}

.CodeMirror-linewidget {
	position: relative;
	z-index: 2;
	overflow: auto;
}

.CodeMirror-widget {}

.CodeMirror-wrap .CodeMirror-scroll {
	overflow-x: hidden;
}

.CodeMirror-measure {
	position: absolute;
	width: 100%;
	height: 0;
	overflow: hidden;
	visibility: hidden;
}

.CodeMirror-measure pre {
	position: static;
}

.CodeMirror div.CodeMirror-cursor {
	position: absolute;
	border-right: none;
	width: 0;
}

div.CodeMirror-cursors {
	visibility: hidden;
	position: relative;
	z-index: 1;
}

.CodeMirror-focused div.CodeMirror-cursors {
	visibility: visible;
}

.CodeMirror-selected {
	background: #d9d9d9;
}

.CodeMirror-focused .CodeMirror-selected {
	background: #d7d4f0;
}

.CodeMirror-crosshair {
	cursor: crosshair;
}

.cm-searching {
	background: #ffa;
	background: rgba(255, 255, 0, .4);
}

/* IE7 hack to prevent it from returning funny offsetTops on the spans */
.CodeMirror span {
	*vertical-align: text-bottom;
}

/* Used to force a border model for a node */
.cm-force-border {
	padding-right: .1px;
}

@media print {

	/* Hide the cursor when printing */
	.CodeMirror div.CodeMirror-cursors {
		visibility: hidden;
	}
}

/*! gridster.js - v0.1.0 - 2013-06-14 - * http://gridster.net/ - Copyright (c) 2013 ducksboard; Licensed MIT */
.gridster {
	position: relative
}

.gridster>* {
	margin: 0 auto;
	-webkit-transition: height .4s;
	-moz-transition: height .4s;
	-o-transition: height .4s;
	-ms-transition: height .4s;
	transition: height .4s
}

.gridster .gs_w {
	z-index: 2;
	position: absolute
}

.ready .gs_w:not(.preview-holder) {
	-webkit-transition: opacity .3s, left .3s, top .3s;
	-moz-transition: opacity .3s, left .3s, top .3s;
	-o-transition: opacity .3s, left .3s, top .3s;
	transition: opacity .3s, left .3s, top .3s
}

.ready .gs_w:not(.preview-holder) {
	-webkit-transition: opacity .3s, left .3s, top .3s, width .3s, height .3s;
	-moz-transition: opacity .3s, left .3s, top .3s, width .3s, height .3s;
	-o-transition: opacity .3s, left .3s, top .3s, width .3s, height .3s;
	transition: opacity .3s, left .3s, top .3s, width .3s, height .3s
}

.gridster .preview-holder {
	z-index: 1;
	position: absolute;
	background-color: #fff;
	border-color: #fff;
	opacity: .3
}

.gridster .player-revert {
	z-index: 10 !important;
	-webkit-transition: left .3s, top .3s !important;
	-moz-transition: left .3s, top .3s !important;
	-o-transition: left .3s, top .3s !important;
	transition: left .3s, top .3s !important
}

.gridster .dragging {
	z-index: 10 !important;
	-webkit-transition: all 0s !important;
	-moz-transition: all 0s !important;
	-o-transition: all 0s !important;
	transition: all 0s !important
}

/*! normalize.css v2.1.2 | MIT License | git.io/normalize */

/* ==========================================================================
   HTML5 display definitions
   ========================================================================== */

/**
 * Correct `block` display not defined in IE 8/9.
 */

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
	display: block;
}

/**
 * Correct `inline-block` display not defined in IE 8/9.
 */

audio,
canvas,
video {
	display: inline-block;
}

/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */

audio:not([controls]) {
	display: none;
	height: 0;
}

/**
 * Address styling not present in IE 8/9.
 */

[hidden] {
	display: none;
}

/* ==========================================================================
   Base
   ========================================================================== */

/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */

html {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	font-family: sans-serif;
	/* 1 */
	-ms-text-size-adjust: 100%;
	/* 2 */
	-webkit-text-size-adjust: 100%;
	/* 2 */
}

/**
 * Remove default margin.
 */

body {
	margin: 0;
}

/* Hacky hack. Remove scrollbars for 320 width screens */
@media screen and (max-width : 320px) {
	body::-webkit-scrollbar {
		display: none;
	}
}

/* ==========================================================================
   Links
   ========================================================================== */

/**
 * Address `outline` inconsistency between Chrome and other browsers.
 */

a:focus {
	outline: thin dotted;
}

/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */

a:active,
a:hover {
	outline: 0;
}

/* ==========================================================================
   Typography
   ========================================================================== */

/**
 * Address variable `h1` font-size and margin within `section` and `article`
 * contexts in Firefox 4+, Safari 5, and Chrome.
 */

h1 {
	font-size: 2em;
	margin: 0.67em 0;
}

/**
 * Address styling not present in IE 8/9, Safari 5, and Chrome.
 */

abbr[title] {
	border-bottom: 1px dotted;
}

/**
 * Address style set to `bolder` in Firefox 4+, Safari 5, and Chrome.
 */

b,
strong {
	font-weight: bold;
}

/**
 * Address styling not present in Safari 5 and Chrome.
 */

dfn {
	font-style: italic;
}

/**
 * Address differences between Firefox and other browsers.
 */

hr {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	height: 0;
}

/**
 * Address styling not present in IE 8/9.
 */

mark {
	background: #ff0;
	color: #000;
}

/**
 * Correct font family set oddly in Safari 5 and Chrome.
 */

code,
kbd,
pre,
samp {
	font-family: monospace, serif;
	font-size: 1em;
}

/**
 * Improve readability of pre-formatted text in all browsers.
 */

pre {
	white-space: pre-wrap;
}

/**
 * Set consistent quote types.
 */

q {
	quotes: "\201C" "\201D" "\2018" "\2019";
}

/**
 * Address inconsistent and variable font size in all browsers.
 */

small {
	font-size: 80%;
}

/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sup {
	top: -0.5em;
}

sub {
	bottom: -0.25em;
}

/* ==========================================================================
   Embedded content
   ========================================================================== */

/**
 * Remove border when inside `a` element in IE 8/9.
 */

img {
	border: 0;
}

/**
 * Correct overflow displayed oddly in IE 9.
 */

svg:not(:root) {
	overflow: hidden;
}

/* ==========================================================================
   Figures
   ========================================================================== */

/**
 * Address margin not present in IE 8/9 and Safari 5.
 */

figure {
	margin: 0;
}

/* ==========================================================================
   Forms
   ========================================================================== */

/**
 * Define consistent border, margin, and padding.
 */

fieldset {
	border: 1px solid #c0c0c0;
	margin: 0 2px;
	padding: 0.35em 0.625em 0.75em;
}

/**
 * 1. Correct `color` not being inherited in IE 8/9.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */

legend {
	border: 0;
	/* 1 */
	padding: 0;
	/* 2 */
}

/**
 * 1. Correct font family not being inherited in all browsers.
 * 2. Correct font size not being inherited in all browsers.
 * 3. Address margins set differently in Firefox 4+, Safari 5, and Chrome.
 */

button,
input,
select,
textarea {
	font-family: inherit;
	/* 1 */
	font-size: 100%;
	/* 2 */
	margin: 0;
	/* 3 */
}

/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */

button,
input {
	line-height: normal;
}

/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Chrome, Safari 5+, and IE 8+.
 * Correct `select` style inheritance in Firefox 4+ and Opera.
 */

button,
select {
	text-transform: none;
}

/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */

button,
html input[type="button"],
/* 1 */
input[type="reset"],
input[type="submit"] {
	-webkit-appearance: button;
	/* 2 */
	cursor: pointer;
	/* 3 */
}

/**
 * Re-set default cursor for disabled elements.
 */

button[disabled],
html input[disabled] {
	cursor: default;
}

/**
 * 1. Address box sizing set to `content-box` in IE 8/9.
 * 2. Remove excess padding in IE 8/9.
 */

input[type="checkbox"],
input[type="radio"] {
	box-sizing: border-box;
	/* 1 */
	padding: 0;
	/* 2 */
}

/**
 * 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome
 *    (include `-moz` to future-proof).
 */

input[type="search"] {
	-webkit-appearance: textfield;
	/* 1 */
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box;
	/* 2 */
	box-sizing: content-box;
}

/**
 * Remove inner padding and search cancel button in Safari 5 and Chrome
 * on OS X.
 */

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

/**
 * Remove inner padding and border in Firefox 4+.
 */

button::-moz-focus-inner,
input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

/**
 * 1. Remove default vertical scrollbar in IE 8/9.
 * 2. Improve readability and alignment in all browsers.
 */

textarea {
	overflow: auto;
	/* 1 */
	vertical-align: top;
	/* 2 */
}

/* ==========================================================================
   Tables
   ========================================================================== */

/**
 * Remove most spacing between table cells.
 */

table {
	border-collapse: collapse;
	border-spacing: 0;
}

/* ==========
freeboard
============*/

html {
	background-color: #101214;
	height: 100%;
	overflow: hidden;
}

body {
	background-color: #f5f5f5;
	/* #101214; */
	color: #8b8b8b;
	/* #8b8b8b; */
	font-family: "Roboto", sans-serif;
	/* "Helvetica Neue", Helvetica, Arial, sans-serif; */
	font-size: 14px;
	line-height: 20px;
	height: 100%;
	overflow-x: hidden;
	overflow-y: auto;
}

.modal {
	position: absolute;
	background-color: #fff;
	top: 120px;
	width: 900px;
	margin: auto;
	right: 0px;
	left: 0px;
	margin-bottom: 25px;
	border-radius: 10px;
}

.modal.small {
	max-width: 500px;
	width: 100%;
}

.modal header {
	background-color: #1a237e;
	height: 40px;
	margin: 0;
	padding: 0 10px 0 10px;
	color: #fff;
	text-transform: uppercase;
	border-radius: 10px 10px 0px 0px;
}

.modal header .title {
	color: #fff;
}

.modal footer {
	height: 40px;
	margin: 0;
	color: #8b8b8b;
	text-transform: uppercase;
	clear: both;
}

.modal footer {
	text-align: right;
}

.modal footer .text-button {
	line-height: 40px;
	padding-left: 15px;
	padding-right: 15px;
	display: inline-block;
}

.modal section {
	padding: 25px;
	padding-bottom: 55px;
}

.control-group:last-child {
	margin-bottom: 0px;
}

.control-group {
	margin-bottom: 16px;
}

.control-group:before,
.control-group:after {
	display: table;
	line-height: 0;
	content: "";
}

.control-group:after {
	clear: both;
}

.control-label {
	padding-top: 5px;
	text-align: right;
	text-transform: uppercase;
	font-size: 11px;
}

.controls {
	padding-left: 20px;
	margin-left: 180px;
}

.input-suffix {
	display: inline-block;
	height: 22px;
	padding: 4px 10px;
	line-height: 23px;
	vertical-align: middle;
	text-transform: uppercase;
	font-size: 11px;
	min-width: 16px;
}

#plugin-description {
	margin-bottom: 25px;
}

.align-right {
	text-align: right;
}

select,
textarea {
	margin: 0;
	font-size: 100%;
	vertical-align: middle;
}

select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
	display: inline-block;
	height: 20px;
	padding: 4px 6px;
	font-size: 14px;
	line-height: 20px;
	color: #d3d4d4;
	vertical-align: middle;
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	border-radius: -px;
	width: 400px;
}

input,
textarea {
	width: 206px;
}

input.small,
textarea.small {
	width: 133px;
}

textarea {
	height: auto;
}

textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
	background-color: #fff;
	border: 1px solid #d1d3e2;
	border-radius: 0.35rem;
	color: #6e707e;
	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	-moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
	-webkit-transition: border linear 0.2s, box-shadow linear 0.2s;
	-moz-transition: border linear 0.2s, box-shadow linear 0.2s;
	-o-transition: border linear 0.2s, box-shadow linear 0.2s;
	transition: border linear 0.2s, box-shadow linear 0.2s;
}

textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.uneditable-input:focus {
	border-color: rgba(255, 153, 0, 0.8);
	outline: 0;
	outline: thin dotted \9;
	/* IE6-9 */

	-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgb(255, 153, 0);
	-moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgb(255, 153, 0);
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgb(255, 153, 0);
}

select {
	width: 220px;
	background-color: #272727;
	height: 27px;
	font-family: ;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 10px 0;
	font-family: inherit;
	font-weight: bold;
	line-height: 20px;
	color: inherit;
	text-rendering: optimizelegibility;
}

h1,
h2,
h3 {
	line-height: 40px;
}

h1 {
	font-size: 38.5px;
}

h2 {
	font-size: 31.5px;
}

h3 {
	font-size: 24.5px;
}

h4 {
	font-size: 17.5px;
}

h5 {
	font-size: 14px;
}

h6 {
	font-size: 11.9px;
}

table {
	max-width: 100%;
	background-color: transparent;
	border-collapse: collapse;
	border-spacing: 0;
}

.table {
	width: 100%;
	margin-bottom: 20px;
}

.table th,
.table td {
	padding: 8px;
	line-height: 20px;
	text-align: left;
	vertical-align: top;
	border-top: 1px solid #dddddd;
}

.table th {
	font-weight: bold;
}

.table thead th {
	vertical-align: bottom;
}

.table caption+thead tr:first-child th,
.table caption+thead tr:first-child td,
.table colgroup+thead tr:first-child th,
.table colgroup+thead tr:first-child td,
.table thead:first-child tr:first-child th,
.table thead:first-child tr:first-child td {
	border-top: 0;
}

.table tbody+tbody {
	border-top: 2px solid #dddddd;
}

.table .table {
	background-color: #ffffff;
}

.table-condensed th,
.table-condensed td {
	padding: 2px 5px;
}

a:focus {
	outline: thin dotted #333;
	outline: 5px auto -webkit-focus-ring-color;
	outline-offset: -2px;
}

#modal_overlay {
	position: absolute;
	z-index: 100;
	top: 0px;
	left: 0px;
	height: 100%;
	width: 100%;
	background: rgba(0, 0, 0, 0.8);
	overflow-y: auto;
}

a:hover,
a:active {
	outline: 0;
}

a {
	color: #1a237e;
	text-decoration: none;
}

a:hover,
a:focus {
	color: #005580;
	text-decoration: underline;
}

.gridster header {
	background-color: #1a237e;
	height: 30px;
	margin: 0;
	padding: 0 10px 0 10px;
	color: #fff;
	text-transform: uppercase;
}

header h1 {
	font-size: 12px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 0;
	margin: 0;
	line-height: 30px;
}

.gridster section {
	line-height: normal;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

#board-content {
	overflow: visible;
	position: relative;
	padding-top: 10px;
}

#main-header {
	width: 100%;
	position: fixed;
	z-index: 50;
	display: none;
	/* background-color: #1a237e; */
}

#admin-bar {
	background: linear-gradient(135deg, #1a237e 0%, #0d47a1 100%);
	width: 100%;
	height: 64px;
	position: fixed;

	/* -webkit-box-shadow: 0 0 5px #000;
	box-shadow: 0 0 5px #000; */
}

#toggle-header {
	margin: 0 auto;
	right: 20px;
	bottom: 20px;
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: #3b71ca;
	text-align: center;
	cursor: pointer;
	position: fixed;
}

#toggle-header-icon {
	/* margin-top: 4px; */
	margin-top: 12px;
	color: white;
}

.widget {
	padding: 5px 10px 5px 10px;
	height: 100%;
	width: 100%;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.widget.fillsize {
	padding: 0;
}

.pane-tools {
	top: 0px;
	right: 0px;
	position: absolute;
	display: none;
}

.sub-section-tools {
	top: 0px;
	right: 0px;
	position: absolute;
	display: none;
}

.datasource-edit-btn {
	padding: 5px;
	margin-right: 5px;
	cursor: pointer;
}

.datasource-delete-btn {
	padding: 5px;
	margin-left: 5px;
	cursor: pointer;
}

#board-tools {
	width: 50%;
	float: left;
}

.table thead th {
	font-size: 10px;
	text-shadow: none;
}

.table td {
	border-top: solid 1px #3d3d3d;
	color: #d3d4d4;
}

#datasources {
	/* width: 50%;
	float: right; */
	height: 100%;
}

#cam {
	height: 100%;
	background-size: cover;
}

#admin-menu {
	left: 0px;
	right: 0px;
	width: 100%;
	top: 15px;
	margin: 0px auto;
	padding-left: 10px;
	padding-right: 10px;
	position: absolute;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.sub-section h2 {
	font-size: 14px;
	font-weight: normal;
	padding: 0px;
	margin: 0px;
	line-height: normal;
}

.sub-section {
	padding: 0;
	border-bottom: solid 1px #ddd;
	position: relative;
	overflow: hidden;
}

.sub-section-height-1 {
	height: 60px;
}

.sub-section-height-2 {
	height: 120px;
}

.sub-section-height-3 {
	height: 180px;
}

.sub-section-height-4 {
	height: 240px;
}

.sub-section:last-of-type {
	border-bottom: none;
}

#pump-icon {
	padding: 10px;
	padding-bottom: 12px;
	display: none;
}

#admin-menu h1 {}

.section-title {
	min-height: 7px;
}

.small-text {
	color: #d3d4d4;
	font-size: 20px;
	font-weight: 100;
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	overflow: hidden;
	text-overflow: ellipsis;
	letter-spacing: 1px;
}

ul,
ol {
	list-style: none;
}

#column-tools {
	position: relative;
	display: none;
	margin: 0 auto;
	left: 0;
	right: 0;
	top: 64px;
}

#column-tools .left-columns {
	position: absolute;
	left: 0;
}

#column-tools .right-columns {
	position: absolute;
	right: 0;
}

.column-tool {
	cursor: pointer;
}

.column-tool.min.sub,
.column-tool.max.add {
	display: none;
}

.column-icon {
	display: inline-block;
	background-color: white;
	width: 5px;
	height: 100%;
}

.column-icon.right {
	float: right;
}

.column-icon.left {
	float: left;
}

.gridster {
	max-width: 960px;
	margin: 0 auto;
	left: 0;
	right: 0;
	top: 0;
}

.gridster .gs_w {
	border: 1px solid #ddd;
	background-color: #fff;
	border-radius: 10px;
	padding: 0px;
	margin: 0px;
	overflow: hidden;
	z-index: auto;
	-webkit-touch-callout: text;
	-webkit-user-select: text;
	-khtml-user-select: text;
	-moz-user-select: text;
	-ms-user-select: text;
	user-select: text;
}

.gridster .preview-holder {
	border: none !important;
	border-radius: 10px !important;
	background: rgba(0, 0, 0, .2) !important;
}

[class^="icon-"],
[class*=" icon-"] {
	display: inline-block;
	width: 14px;
	height: 14px;
	margin-top: 1px;
	*margin-right: .3em;
	line-height: 14px;
	vertical-align: text-top;
	background-image: url("../img/glyphicons-halflings.png");
	background-position: 14px 14px;
	background-repeat: no-repeat;
}

/* White icons with optional class, or on hover/focus/active states of certain elements */

.icon-white,
.nav-pills>.active>a>[class^="icon-"],
.nav-pills>.active>a>[class*=" icon-"],
.nav-list>.active>a>[class^="icon-"],
.nav-list>.active>a>[class*=" icon-"],
.navbar-inverse .nav>.active>a>[class^="icon-"],
.navbar-inverse .nav>.active>a>[class*=" icon-"],
.dropdown-menu>li>a:hover>[class^="icon-"],
.dropdown-menu>li>a:focus>[class^="icon-"],
.dropdown-menu>li>a:hover>[class*=" icon-"],
.dropdown-menu>li>a:focus>[class*=" icon-"],
.dropdown-menu>.active>a>[class^="icon-"],
.dropdown-menu>.active>a>[class*=" icon-"],
.dropdown-submenu:hover>a>[class^="icon-"],
.dropdown-submenu:focus>a>[class^="icon-"],
.dropdown-submenu:hover>a>[class*=" icon-"],
.dropdown-submenu:focus>a>[class*=" icon-"] {
	background-image: url("../img/glyphicons-halflings-white.png");
}

.icon-glass {
	background-position: 0 0;
}

.icon-music {
	background-position: -24px 0;
}

.icon-search {
	background-position: -48px 0;
}

.icon-envelope {
	background-position: -72px 0;
}

.icon-heart {
	background-position: -96px 0;
}

.icon-star {
	background-position: -120px 0;
}

.icon-star-empty {
	background-position: -144px 0;
}

.icon-user {
	background-position: -168px 0;
}

.icon-film {
	background-position: -192px 0;
}

.icon-th-large {
	background-position: -216px 0;
}

.icon-th {
	background-position: -240px 0;
}

.icon-th-list {
	background-position: -264px 0;
}

.icon-ok {
	background-position: -288px 0;
}

.icon-remove {
	background-position: -312px 0;
}

.icon-zoom-in {
	background-position: -336px 0;
}

.icon-zoom-out {
	background-position: -360px 0;
}

.icon-off {
	background-position: -384px 0;
}

.icon-signal {
	background-position: -408px 0;
}

.icon-cog {
	background-position: -432px 0;
}

.icon-trash {
	background-position: -456px 0;
}

.icon-home {
	background-position: 0 -24px;
}

.icon-file {
	background-position: -24px -24px;
}

.icon-time {
	background-position: -48px -24px;
}

.icon-road {
	background-position: -72px -24px;
}

.icon-download-alt {
	background-position: -96px -24px;
}

.icon-download {
	background-position: -120px -24px;
}

.icon-upload {
	background-position: -144px -24px;
}

.icon-inbox {
	background-position: -168px -24px;
}

.icon-play-circle {
	background-position: -192px -24px;
}

.icon-repeat {
	background-position: -216px -24px;
}

.icon-refresh {
	background-position: -240px -24px;
}

.icon-list-alt {
	background-position: -264px -24px;
}

.icon-lock {
	background-position: -287px -24px;
}

.icon-flag {
	background-position: -312px -24px;
}

.icon-headphones {
	background-position: -336px -24px;
}

.icon-volume-off {
	background-position: -360px -24px;
}

.icon-volume-down {
	background-position: -384px -24px;
}

.icon-volume-up {
	background-position: -408px -24px;
}

.icon-qrcode {
	background-position: -432px -24px;
}

.icon-barcode {
	background-position: -456px -24px;
}

.icon-tag {
	background-position: 0 -48px;
}

.icon-tags {
	background-position: -25px -48px;
}

.icon-book {
	background-position: -48px -48px;
}

.icon-bookmark {
	background-position: -72px -48px;
}

.icon-print {
	background-position: -96px -48px;
}

.icon-camera {
	background-position: -120px -48px;
}

.icon-font {
	background-position: -144px -48px;
}

.icon-bold {
	background-position: -167px -48px;
}

.icon-italic {
	background-position: -192px -48px;
}

.icon-text-height {
	background-position: -216px -48px;
}

.icon-text-width {
	background-position: -240px -48px;
}

.icon-align-left {
	background-position: -264px -48px;
}

.icon-align-center {
	background-position: -288px -48px;
}

.icon-align-right {
	background-position: -312px -48px;
}

.icon-align-justify {
	background-position: -336px -48px;
}

.icon-list {
	background-position: -360px -48px;
}

.icon-indent-left {
	background-position: -384px -48px;
}

.icon-indent-right {
	background-position: -408px -48px;
}

.icon-facetime-video {
	background-position: -432px -48px;
}

.icon-picture {
	background-position: -456px -48px;
}

.icon-pencil {
	background-position: 0 -72px;
}

.icon-map-marker {
	background-position: -24px -72px;
}

.icon-adjust {
	background-position: -48px -72px;
}

.icon-tint {
	background-position: -72px -72px;
}

.icon-edit {
	background-position: -96px -72px;
}

.icon-share {
	background-position: -120px -72px;
}

.icon-check {
	background-position: -144px -72px;
}

.icon-move {
	background-position: -168px -72px;
}

.icon-step-backward {
	background-position: -192px -72px;
}

.icon-fast-backward {
	background-position: -216px -72px;
}

.icon-backward {
	background-position: -240px -72px;
}

.icon-play {
	background-position: -264px -72px;
}

.icon-pause {
	background-position: -288px -72px;
}

.icon-stop {
	background-position: -312px -72px;
}

.icon-forward {
	background-position: -336px -72px;
}

.icon-fast-forward {
	background-position: -360px -72px;
}

.icon-step-forward {
	background-position: -384px -72px;
}

.icon-eject {
	background-position: -408px -72px;
}

.icon-chevron-left {
	background-position: -432px -72px;
}

.icon-chevron-right {
	background-position: -456px -72px;
}

.icon-plus-sign {
	background-position: 0 -96px;
}

.icon-minus-sign {
	background-position: -24px -96px;
}

.icon-remove-sign {
	background-position: -48px -96px;
}

.icon-ok-sign {
	background-position: -72px -96px;
}

.icon-question-sign {
	background-position: -96px -96px;
}

.icon-info-sign {
	background-position: -120px -96px;
}

.icon-screenshot {
	background-position: -144px -96px;
}

.icon-remove-circle {
	background-position: -168px -96px;
}

.icon-ok-circle {
	background-position: -192px -96px;
}

.icon-ban-circle {
	background-position: -216px -96px;
}

.icon-arrow-left {
	background-position: -240px -96px;
}

.icon-arrow-right {
	background-position: -264px -96px;
}

.icon-arrow-up {
	background-position: -289px -96px;
}

.icon-arrow-down {
	background-position: -312px -96px;
}

.icon-share-alt {
	background-position: -336px -96px;
}

.icon-resize-full {
	background-position: -360px -96px;
}

.icon-resize-small {
	background-position: -384px -96px;
}

.icon-plus {
	background-position: -408px -96px;
}

.icon-minus {
	background-position: -433px -96px;
}

.icon-asterisk {
	background-position: -456px -96px;
}

.icon-exclamation-sign {
	background-position: 0 -120px;
}

.icon-gift {
	background-position: -24px -120px;
}

.icon-leaf {
	background-position: -48px -120px;
}

.icon-fire {
	background-position: -72px -120px;
}

.icon-eye-open {
	background-position: -96px -120px;
}

.icon-eye-close {
	background-position: -120px -120px;
}

.icon-warning-sign {
	background-position: -144px -120px;
}

.icon-plane {
	background-position: -168px -120px;
}

.icon-calendar {
	background-position: -192px -120px;
}

.icon-random {
	width: 16px;
	background-position: -216px -120px;
}

.icon-comment {
	background-position: -240px -120px;
}

.icon-magnet {
	background-position: -264px -120px;
}

.icon-chevron-up {
	background-position: -288px -120px;
}

.icon-chevron-down {
	background-position: -313px -119px;
}

.icon-retweet {
	background-position: -336px -120px;
}

.icon-shopping-cart {
	background-position: -360px -120px;
}

.icon-folder-close {
	width: 16px;
	background-position: -384px -120px;
}

.icon-folder-open {
	width: 16px;
	background-position: -408px -120px;
}

.icon-resize-vertical {
	background-position: -432px -119px;
}

.icon-resize-horizontal {
	background-position: -456px -118px;
}

.icon-hdd {
	background-position: 0 -144px;
}

.icon-bullhorn {
	background-position: -24px -144px;
}

.icon-bell {
	background-position: -48px -144px;
}

.icon-certificate {
	background-position: -72px -144px;
}

.icon-thumbs-up {
	background-position: -96px -144px;
}

.icon-thumbs-down {
	background-position: -120px -144px;
}

.icon-hand-right {
	background-position: -144px -144px;
}

.icon-hand-left {
	background-position: -168px -144px;
}

.icon-hand-up {
	background-position: -192px -144px;
}

.icon-hand-down {
	background-position: -216px -144px;
}

.icon-circle-arrow-right {
	background-position: -240px -144px;
}

.icon-circle-arrow-left {
	background-position: -264px -144px;
}

.icon-circle-arrow-up {
	background-position: -288px -144px;
}

.icon-circle-arrow-down {
	background-position: -312px -144px;
}

.icon-globe {
	background-position: -336px -144px;
}

.icon-wrench {
	background-position: -360px -144px;
}

.icon-tasks {
	background-position: -384px -144px;
}

.icon-filter {
	background-position: -408px -144px;
}

.icon-briefcase {
	background-position: -432px -144px;
}

.icon-fullscreen {
	background-position: -456px -144px;
}

.form-table td {
	vertical-align: top;
}

.form-label {
	text-align: right;
	padding-right: 14px;
	height: 42px;
	float: left;
	width: 160px;
	padding-top: 4px;
}

.form-value {
	float: left;
}

.form-row {
	clear: both;
}

td.table-row-operation {}

input.table-row-value {
	width: 150px;
	margin: 0px;
}

.sub-table {
	margin-bottom: 3px;
}

.sub-table td {
	border: none;
	vertical-align: middle;
}

.form-table-value-subtable {
	max-height: 110px;
	overflow-x: hidden;
	overflow-y: auto;
	border-bottom: solid 1px #3d3d3d;
	width: 414px;
}

.datasource-list-container {
	max-height: 110px;
	overflow-x: hidden;
	overflow-y: auto;
	border-bottom: solid 1px #3d3d3d;
}

ul.value-dropdown {
	height: 95px;
	position: absolute;
	padding: 0px;
	margin: 0px;
	background-color: #fff;
	border: 1px solid rgba(255, 153, 0, 0.8);
	border-radius: 10px;
	overflow-x: hidden;
	overflow-y: auto;
	z-index: 3001;
}

ul.value-dropdown li {
	padding: 5px;
	cursor: pointer;
}

ul.value-dropdown li.selected {
	background-color: rgba(255, 153, 0, 0.8);
	color: #000;
	text-shadow: none;
}

ul.value-dropdown li .preview {
	font-style: italic;
	font-size: 10px;
	width: 300px;
	display: inline-block;
	vertical-align: bottom;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	padding-left: 20px;
}

td.form-table-value>input[type="checkbox"] {
	height: 15px;
	margin-top: 7px;
}

.table-row-operation>i {
	cursor: pointer;
}

#main-logo {
	display: block;
	margin-bottom: 20px;
}

#dash-logo {
	display: block;
	margin-left: 10px;
	margin-bottom: 10px;
}

.value-editor-ds {
	color: #1a237e;
	cursor: pointer;
}

ul.board-toolbar {
	padding: 0px;
	margin: 0px;
	text-transform: uppercase;
	font-size: 14px;
}

ul.board-toolbar.vertical {
	display: flex;
}

.board-toolbar li {
	float: left;
	cursor: pointer;
	margin: 0px 0px 0px 5px;
	background-color: rgba(75, 75, 75, 0.0);
	padding: 8px;
	height: 12px;
	color: #000;
}

.board-toolbar.vertical li {
	float: none;
}

.board-toolbar li:hover {
	background-color: transparent;
	/* rgba(75, 75, 75, 1.0); */
	opacity: 70%;
	-webkit-transition: 250ms linear;
	-moz-transition: 250ms linear;
	-o-transition: 250ms linear;
	-ms-transition: 250ms linear;
	transition: 250ms linear;
}

.board-toolbar li i {
	float: left;
	margin: 0;
	padding: 0;
	font-size: 16px;
}

.board-toolbar li label {
	cursor: pointer;
	margin-left: 5px;
	float: left;
	margin-top: -3px;
	margin-bottom: -10px;
	font-size: 12px;
	color: #6e707e;
}

.text-button {
	color: #1a237e;
	cursor: pointer;
	text-transform: uppercase;
}

.datasource-name {
	text-transform: none;
}

a:hover.text-button,
a:focus.text-button {
	color: #1a237e;
	text-decoration: none;
}

.text-button>i {
	margin-right: 10px;
}

.text-button::before,
.text-button::after {
	display: inline-block;
	opacity: 0;
	-webkit-transition: -webkit-transform 0.3s, opacity 0.2s;
	-moz-transition: -moz-transform 0.3s, opacity 0.2s;
	transition: transform 0.3s, opacity 0.2s;
}

.text-button::before {
	margin-right: 5px;
	content: '[';
	-webkit-transform: translateX(10px);
	-moz-transform: translateX(10px);
	transform: translateX(10px);
}

.text-button::after {
	margin-left: 5px;
	content: ']';
	-webkit-transform: translateX(-10px);
	-moz-transform: translateX(-10px);
	transform: translateX(-10px);
}

.text-button:hover::before,
.text-button:hover::after,
.text-button:focus::before,
.text-button:focus::after {
	opacity: 1;
	-webkit-transform: translateX(0px);
	-moz-transform: translateX(0px);
	transform: translateX(0px);
}

.setting-description {
	font-size: 10px;
	text-shadow: none;
	color: #6F6F6F;
	margin-top: 5px;
	margin-bottom: 15px;
	max-width: 414px;
	line-height: 1.5em;
}

.calculated-setting-row {
	clear: both;
	height: 30px;
	margin-bottom: 5px;
}

textarea.calculated-value-input {
	position: absolute;
	width: 400px;
	height: 20px;
	resize: none;
	white-space: nowrap;
	overflow: auto;
	Z-index: 3000;
}

ul.datasource-input-suffix {
	margin-left: 409px;
}

.form-label .board-toolbar li {
	float: right;
}

.styled-select select {
	width: 414px;
	height: 30px;
	-webkit-appearance: none;
	border: 1px solid #d1d3e2;
	border-radius: 0.35rem;
	color: #6e707e;
	cursor: pointer;
	background: url(../img/dropdown-arrow.png) no-repeat right #fff;
}

.title {
	font-family: 'Helvetica-Neue', 'Helvetica', 'Arial', sans-serif;
	vertical-align: baseline;
	-webkit-font-smoothing: antialiased;
	font-style: normal;
	color: #d3d4d4;
}

.title.bordered {
	border: solid 3px #d3d4d4;
	display: inline-block;
	padding: 2px 17px 2px 17px;
	line-height: 39px;
}

h1.title {
	margin-bottom: 10px;
	font-size: 23px;
	letter-spacing: -1px;
}

h2.title {
	font-size: 13px;
	line-height: 20px;
	margin: 0px;
	padding-top: 10px;
	padding-bottom: 10px;
}

.form-value input[type="checkbox"] {
	margin-top: 8px;
}

.table-operation {
	font-size: 11px;
	padding-left: 5px;
	padding-bottom: 5px;
	padding-top: 1px;
}

#add-pane {}

#pane-holding-pen {
	display: none;
}

@media screen and (max-width : 960px) {
	#add-pane {
		padding-top: 10px;
	}

	.text-button {
		font-size: 14px;
		line-height: 22px;
	}

	h1.title {
		margin-bottom: 0px;
		font-size: 13px;
		letter-spacing: 0px;
	}

	.title.bordered {
		padding: 1px 8px;
		line-height: 25px;
		border-width: 2px;
	}

	#admin-menu {
		width: 100%;
	}

	h2.title {
		font-size: 10px;
		padding-top: 5px;
		padding-bottom: 5px;
	}

	#board-tools {
		width: 100%;
	}

	#datasources {
		width: 100%;
		float: none;
		clear: both;
	}

	#board-actions {
		float: left;
	}

	#board-logo {
		float: left;
	}

	.modal header {
		height: 30px;
	}

	.modal {
		width: 100%;
		top: 0px;
		left: 0px;
	}

	.datasource-list-container {
		max-height: 77px;
	}

	.form-label {
		float: none;
		height: auto;
		width: auto;
		text-align: left;
		padding-top: 0px;
	}

	.form-value {
		width: 100%;
		padding-bottom: 10px;
		float: none;
	}

	.control-label {
		width: auto;
	}

	.modal section {
		padding-bottom: 10px;
		padding-top: 10px;
	}

	ul.datasource-input-suffix {
		margin-left: 0px;
		margin-bottom: 30px;
	}

	ul.datasource-input-suffix li {
		margin-left: 0;
	}

	textarea,
	input[type="text"] {
		width: 95%;
	}

	.styled-select select {
		width: 100%;
	}

	.form-table-value-subtable {
		width: 100%;
	}

	.table-operation {
		font-size: 11px;
	}

	textarea.calculated-value-input {
		position: inherit;
		width: 95%;
	}
}

.wrapperloading {
	position: absolute;
	height: 200px;
	width: 200px;
	top: 50%;
	margin-top: -100px;
	left: 50%;
	margin-left: -100px;
	z-index: 2000;
}

.wrapperloading .loading.up {
	position: absolute;
	height: 200px;
	width: 200px;
	border-radius: 150px;
	border: 3px solid #fff;
	border-top-color: #fff;
	border-left-color: #555;
	border-right-color: #555;
	border-bottom-color: #fff;
	-webkit-animation: rotation 3s linear infinite;
	-moz-animation: rotation 3s linear infinite;
	-o-animation: rotation 3s linear infinite;
	animation: rotation 3s linear infinite;
}

.wrapperloading .loading.down {
	position: absolute;
	height: 100px;
	width: 100px;
	top: 50%;
	margin-top: -50px;
	left: 50%;
	margin-left: -50px;
	border-radius: 150px;
	border: 3px solid #fff;
	border-left-color: #fff;
	border-top-color: #555;
	border-bottom-color: #555;
	border-right-color: #fff;
	-webkit-animation: rotation 1s linear infinite;
	-moz-animation: rotation 1s linear infinite;
	-o-animation: rotation 1s linear infinite;
	animation: rotation 1s linear infinite;
}

@-webkit-keyframes rotation {
	0% {
		-webkit-transform: rotate(0deg);
		-moz-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		-o-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	50% {
		-webkit-transform: rotate(180deg);
		-moz-transform: rotate(180deg);
		-ms-transform: rotate(180deg);
		-o-transform: rotate(180deg);
		transform: rotate(180deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
		-moz-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		-o-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@-moz-keyframes rotation {
	0% {
		-webkit-transform: rotate(0deg);
		-moz-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		-o-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	50% {
		-webkit-transform: rotate(180deg);
		-moz-transform: rotate(180deg);
		-ms-transform: rotate(180deg);
		-o-transform: rotate(180deg);
		transform: rotate(180deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
		-moz-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		-o-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@-o-keyframes rotation {
	0% {
		-webkit-transform: rotate(0deg);
		-moz-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		-o-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	50% {
		-webkit-transform: rotate(180deg);
		-moz-transform: rotate(180deg);
		-ms-transform: rotate(180deg);
		-o-transform: rotate(180deg);
		transform: rotate(180deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
		-moz-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		-o-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes rotation {
	0% {
		-webkit-transform: rotate(0deg);
		-moz-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		-o-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	50% {
		-webkit-transform: rotate(180deg);
		-moz-transform: rotate(180deg);
		-ms-transform: rotate(180deg);
		-o-transform: rotate(180deg);
		transform: rotate(180deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
		-moz-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		-o-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
	color: #636363;
}

input:-moz-placeholder,
textarea:-moz-placeholder {
	color: #636363;
}

::-webkit-scrollbar {
	height: 0px;
	width: 4px;
	background: transparent;
	padding-right: 10;
}

::-webkit-scrollbar-thumb {
	background: rgba(255, 255, 255, 0.05);
	-webkit-border-radius: 1ex;
}

::-webkit-scrollbar-corner {
	background: transparent;
}

.validation-error {
	margin-top: 7px;
	margin-bottom: 7px;
	color: #AA7575;
	font-size: 13px;
}

.onoffswitch {
	position: relative;
	width: 70px;
	background-color: transparent;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
}

.onoffswitch-checkbox {
	display: none;
}

.onoffswitch-label {
	display: block;
	overflow: hidden;
	cursor: pointer;
	/* border: 1px solid #3D3D3D; */
	background-color: transparent;
	border-radius: 15px;
}

.onoffswitch-inner {
	width: 200%;
	margin-left: -100%;
	-moz-transition: margin 0.3s ease-in 0s;
	-webkit-transition: margin 0.3s ease-in 0s;
	-o-transition: margin 0.3s ease-in 0s;
	transition: margin 0.3s ease-in 0s;
}

.onoffswitch-inner .on,
.onoffswitch-inner .off {
	float: left;
	width: 50%;
	height: 29px;
	padding: 0;
	line-height: 29px;
	font-size: 17px;
	color: white;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.onoffswitch-inner .on {
	padding-left: 6px;
	background-color: #1a237e;
	color: #fff;
}

.onoffswitch-inner .off {
	padding-right: 6px;
	background-color: #858796;
	color: #fff;
	text-align: right;
}

.onoffswitch-switch {
	width: 21px;
	margin: 4px;
	background: #fff;
	/* border: 1px solid #3D3D3D; */
	border-radius: 50%;
	position: absolute;
	top: 0;
	bottom: 0;
	right: 40px;
	-moz-transition: all 0.3s ease-in 0s;
	-webkit-transition: all 0.3s ease-in 0s;
	-o-transition: all 0.3s ease-in 0s;
	transition: all 0.3s ease-in 0s;
}

.onoffswitch-checkbox:checked+.onoffswitch-label .onoffswitch-inner {
	margin-left: 0;
}

.onoffswitch-checkbox:checked+.onoffswitch-label .onoffswitch-switch {
	right: 0px;
	background-color: #fff;
}

.code-window {
	z-index: 5000;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: #2a2a2a;
}

.code-window-footer {
	height: 64px;
	position: absolute;
	right: 0;
	bottom: 0;
	line-height: 64px;
	padding-right: 20px;
	padding-left: 20px;
}

.code-window-header {
	position: absolute;
	top: 0;
	left: 0;
	padding: 20px;
	line-height: 1.1em;
	font-weight: 200;
}

@media screen and (max-width : 800px) {
	.code-window-header {
		font-size: 11px;
	}
}

.code-mirror-wrapper {
	width: 100%;
	position: absolute;
	bottom: 64px;
	top: 100px;
	left: 0;
}

.CodeMirror {
	width: 100%;
	height: 100% !important;
}

.ui-state-active {
	background: #F90 !important;
}