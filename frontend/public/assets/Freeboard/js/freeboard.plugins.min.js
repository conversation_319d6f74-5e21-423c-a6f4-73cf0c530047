!function () { var a = function (a, b) { function c(a) { e && clearInterval(e), e = setInterval(function () { d.updateNow() }, a) } var d = this, e = null, f = a, g = 0, h = !1; c(1e3 * f.refresh), this.updateNow = function () { if (!(g > 1 && !f.use_thingproxy || g > 2)) { var a = f.url; 2 == g && f.use_thingproxy && (a = ("https:" == location.protocol ? "https:" : "http:") + "//thingproxy.freeboard.io/fetch/" + encodeURI(f.url)); var c = f.body; if (c) try { c = JSON.parse(c) } catch (a) { } $.ajax({ url: a, dataType: 1 == g ? "JSONP" : "JSON", type: f.method || "GET", data: c, beforeSend: function (a) { try { _.each(f.headers, function (b) { var c = b.name, d = b.value; _.isUndefined(c) || _.isUndefined(d) || a.setRequestHeader(c, d) }) } catch (a) { } }, success: function (a) { h = !0, b(a) }, error: function (a, b, c) { h || (g++, d.updateNow()) } }) } }, this.onDispose = function () { clearInterval(e), e = null }, this.onSettingsChanged = function (a) { h = !1, g = 0, f = a, c(1e3 * f.refresh), d.updateNow() } }; freeboard.loadDatasourcePlugin({ type_name: "JSON", settings: [{ name: "url", display_name: "URL", type: "text" }, { name: "use_thingproxy", display_name: "Try thingproxy", description: 'A direct JSON connection will be tried first, if that fails, a JSONP connection will be tried. If that fails, you can use thingproxy, which can solve many connection problems to APIs. <a href="https://github.com/Freeboard/thingproxy" target="_blank">More information</a>.', type: "boolean", default_value: !0 }, { name: "refresh", display_name: "Refresh Every", type: "number", suffix: "seconds", default_value: 5 }, { name: "method", display_name: "Method", type: "option", options: [{ name: "GET", value: "GET" }, { name: "POST", value: "POST" }, { name: "PUT", value: "PUT" }, { name: "DELETE", value: "DELETE" }] }, { name: "body", display_name: "Body", type: "text", description: "The body of the request. Normally only used if method is POST" }, { name: "headers", display_name: "Headers", type: "array", settings: [{ name: "name", display_name: "Name", type: "text" }, { name: "value", display_name: "Value", type: "text" }] }], newInstance: function (b, c, d) { c(new a(b, d)) } }); var b = function (a, b) { function c(a) { f && clearInterval(f), f = setInterval(function () { e.updateNow() }, a) } function d(a) { return a.replace(/\w\S*/g, function (a) { return a.charAt(0).toUpperCase() + a.substr(1).toLowerCase() }) } var e = this, f = null, g = a; c(1e3 * g.refresh), this.updateNow = function () { $.ajax({ url: "http://api.openweathermap.org/data/2.5/weather?APPID=" + g.api_key + "&q=" + encodeURIComponent(g.location) + "&units=" + g.units, dataType: "JSONP", success: function (a) { var c = { place_name: a.name, sunrise: new Date(1e3 * a.sys.sunrise).toLocaleTimeString(), sunset: new Date(1e3 * a.sys.sunset).toLocaleTimeString(), conditions: d(a.weather[0].description), current_temp: a.main.temp, high_temp: a.main.temp_max, low_temp: a.main.temp_min, pressure: a.main.pressure, humidity: a.main.humidity, wind_speed: a.wind.speed, wind_direction: a.wind.deg }; b(c) }, error: function (a, b, c) { } }) }, this.onDispose = function () { clearInterval(f), f = null }, this.onSettingsChanged = function (a) { g = a, e.updateNow(), c(1e3 * g.refresh) } }; freeboard.loadDatasourcePlugin({ type_name: "openweathermap", display_name: "Open Weather Map API", settings: [{ name: "api_key", display_name: "API Key", type: "text", description: "Your personal API Key from Open Weather Map" }, { name: "location", display_name: "Location", type: "text", description: "Example: London, UK" }, { name: "units", display_name: "Units", type: "option", default: "imperial", options: [{ name: "Imperial", value: "imperial" }, { name: "Metric", value: "metric" }] }, { name: "refresh", display_name: "Refresh Every", type: "number", suffix: "seconds", default_value: 5 }], newInstance: function (a, c, d) { c(new b(a, d)) } }); var c = function (a, b) { function c(a) { b(a) } var d = this, e = a; this.updateNow = function () { dweetio.get_latest_dweet_for(e.thing_id, function (a, b) { a || c(b[0].content) }) }, this.onDispose = function () { }, this.onSettingsChanged = function (a) { dweetio.stop_listening_for(e.thing_id), e = a, dweetio.listen_for(e.thing_id, function (a) { c(a.content) }) }, d.onSettingsChanged(a) }; freeboard.loadDatasourcePlugin({ type_name: "dweet_io", display_name: "Dweet.io", external_scripts: ["http://dweet.io/client/dweet.io.min.js"], settings: [{ name: "thing_id", display_name: "Thing Name", description: "Example: salty-dog-1", type: "text" }], newInstance: function (a, b, d) { b(new c(a, d)) } }); var d = function (a, b) { function c() { h.length > 0 ? (i < h.length && (b(h[i]), i++), i >= h.length && g.loop && (i = 0), i < h.length && (e = setTimeout(c, 1e3 * g.refresh))) : b({}) } function d() { h = [], i = 0, e && (clearTimeout(e), e = null) } var e, f = this, g = a, h = [], i = 0; this.updateNow = function () { d(), $.ajax({ url: g.datafile, dataType: g.is_jsonp ? "JSONP" : "JSON", success: function (a) { h = _.isArray(a) ? a : [], i = 0, c() }, error: function (a, b, c) { } }) }, this.onDispose = function () { d() }, this.onSettingsChanged = function (a) { g = a, f.updateNow() } }; freeboard.loadDatasourcePlugin({ type_name: "playback", display_name: "Playback", settings: [{ name: "datafile", display_name: "Data File URL", type: "text", description: "A link to a JSON array of data." }, { name: "is_jsonp", display_name: "Is JSONP", type: "boolean" }, { name: "loop", display_name: "Loop", type: "boolean", description: "Rewind and loop when finished" }, { name: "refresh", display_name: "Refresh Every", type: "number", suffix: "seconds", default_value: 5 }], newInstance: function (a, b, c) { b(new d(a, c)) } }); var e = function (a, b) { function c() { e && (clearTimeout(e), e = null) } function d() { c(), e = setInterval(f.updateNow, 1e3 * g.refresh) } var e, f = this, g = a; this.updateNow = function () { var a = new Date, c = { numeric_value: a.getTime(), full_string_value: a.toLocaleString(), date_string_value: a.toLocaleDateString(), time_string_value: a.toLocaleTimeString(), date_object: a }; b(c) }, this.onDispose = function () { c() }, this.onSettingsChanged = function (a) { g = a, d() }, d() }; freeboard.loadDatasourcePlugin({ type_name: "clock", display_name: "Clock", settings: [{ name: "refresh", display_name: "Refresh Every", type: "number", suffix: "seconds", default_value: 1 }], newInstance: function (a, b, c) { b(new e(a, c)) } }), freeboard.loadDatasourcePlugin({ type_name: "meshblu", display_name: "Octoblu", description: "app.octoblu.com", external_scripts: ["http://meshblu.octoblu.com/js/meshblu.js"], settings: [{ name: "uuid", display_name: "UUID", type: "text", default_value: "device uuid", description: "your device UUID", required: !0 }, { name: "token", display_name: "Token", type: "text", default_value: "device token", description: "your device TOKEN", required: !0 }, { name: "server", display_name: "Server", type: "text", default_value: "meshblu.octoblu.com", description: "your server", required: !0 }, { name: "port", display_name: "Port", type: "number", default_value: 80, description: "server port", required: !0 }], newInstance: function (a, b, c) { b(new f(a, c)) } }); var f = function (a, b) { function c() { var a = skynet.createConnection({ uuid: e.uuid, token: e.token, server: e.server, port: e.port }); a.on("ready", function (c) { a.on("message", function (a) { b(a) }) }) } var d = this, e = a; d.onSettingsChanged = function (a) { e = a }, d.updateNow = function () { c() }, d.onDispose = function () { } } }(), function () { function a(a, b, c) { var d = $(b).text(); if (d != a) if ($.isNumeric(a) && $.isNumeric(d)) { var e = a.toString().split("."), f = 0; e.length > 1 && (f = e[1].length), e = d.toString().split("."); var g = 0; e.length > 1 && (g = e[1].length), jQuery({ transitionValue: Number(d), precisionValue: g }).animate({ transitionValue: Number(a), precisionValue: f }, { duration: c, step: function () { $(b).text(this.transitionValue.toFixed(this.precisionValue)) }, done: function () { $(b).text(a) } }) } else $(b).text(a) } function b(a, b) { for (var c = $("<div class='sparkline-legend'></div>"), d = 0; d < b.length; d++) { var f = e[d % e.length], g = b[d]; c.append("<div class='sparkline-legend-value'><span style='color:" + f + "'>&#9679;</span>" + g + "</div>") } a.empty().append(c), freeboard.addStyle(".sparkline-legend", "margin:5px;"), freeboard.addStyle(".sparkline-legend-value", "color:white; font:10px arial,san serif; float:left; overflow:hidden; width:50%;"), freeboard.addStyle(".sparkline-legend-value span", "font-weight:bold; padding-right:5px;") } function c(a, b, c) { var f = $(a).data().values, g = $(a).data().valueMin, h = $(a).data().valueMax; f || (f = [], g = void 0, h = void 0); var i = function (a, b) { f[b] || (f[b] = []), f[b].length >= d && f[b].shift(), f[b].push(Number(a)), (void 0 === g || a < g) && (g = a), (void 0 === h || a > h) && (h = a) }; _.isArray(b) ? _.each(b, i) : i(b, 0), $(a).data().values = f, $(a).data().valueMin = g, $(a).data().valueMax = h; var j = '<span style="color: {{color}}">&#9679;</span> {{y}}', k = !1; _.each(f, function (b, d) { $(a).sparkline(b, { type: "line", composite: k, height: "100%", width: "100%", fillColor: !1, lineColor: e[d % e.length], lineWidth: 2, spotRadius: 3, spotColor: !1, minSpotColor: "#78AB49", maxSpotColor: "#78AB49", highlightSpotColor: "#9D3926", highlightLineColor: "#9D3926", chartRangeMin: g, chartRangeMax: h, tooltipFormat: c && c[d] ? j + " (" + c[d] + ")" : j }), k = !0 }) } var d = 100, e = ["#FF9900", "#FFFFFF", "#B3B4B4", "#6B6B6B", "#28DE28", "#13F7F9", "#E6EE18", "#C41204", "#CA3CB8", "#0B1CFB"], f = freeboard.getStyleString("values"); freeboard.addStyle(".widget-big-text", f + "font-size:75px;"), freeboard.addStyle(".tw-display", "width: 100%; height:100%; display:table; table-layout:fixed;"), freeboard.addStyle(".tw-tr", "display:table-row;"), freeboard.addStyle(".tw-tg", "display:table-row-group;"), freeboard.addStyle(".tw-tc", "display:table-caption;"), freeboard.addStyle(".tw-td", "display:table-cell;"), freeboard.addStyle(".tw-value", f + "overflow: hidden;display: inline-block;text-overflow: ellipsis;"), freeboard.addStyle(".tw-unit", "display: inline-block;padding-left: 10px;padding-bottom: 1.1em;vertical-align: bottom;"), freeboard.addStyle(".tw-value-wrapper", "position: relative;vertical-align: middle;height:100%;"), freeboard.addStyle(".tw-sparkline", "height:20px;"); var g = function (b) { function d() { _.isUndefined(e.units) || "" == e.units ? h.css("max-width", "100%") : h.css("max-width", f.innerWidth() - i.outerWidth(!0) + "px") } var e = b, f = $('<div class="tw-display"></div>'), g = $('<h2 class="section-title tw-title tw-td"></h2>'), h = $('<div class="tw-value"></div>'), i = $('<div class="tw-unit"></div>'), j = $('<div class="tw-sparkline tw-td"></div>'); this.render = function (a) { $(a).empty(), $(f).append($('<div class="tw-tr"></div>').append(g)).append($('<div class="tw-tr"></div>').append($('<div class="tw-value-wrapper tw-td"></div>').append(h).append(i))).append($('<div class="tw-tr"></div>').append(j)), $(a).append(f), d() }, this.onSettingsChanged = function (a) { e = a; var b = !_.isUndefined(a.title) && "" != a.title, c = !_.isUndefined(a.units) && "" != a.units; a.sparkline ? j.attr("style", null) : (delete j.data().values, j.empty(), j.hide()), b ? (g.html(_.isUndefined(a.title) ? "" : a.title), g.attr("style", null)) : (g.empty(), g.hide()), c ? (i.html(_.isUndefined(a.units) ? "" : a.units), i.attr("style", null)) : (i.empty(), i.hide()); var f = 30; "big" == a.size && (f = 75, a.sparkline && (f = 60)), h.css({ "font-size": f + "px" }), d() }, this.onSizeChanged = function () { d() }, this.onCalculatedValueChanged = function (b, d) { "value" == b && (e.animate ? a(d, h, 500) : h.text(d), e.sparkline && c(j, d)) }, this.onDispose = function () { }, this.getHeight = function () { return "big" == e.size || e.sparkline ? 2 : 1 }, this.onSettingsChanged(b) }; freeboard.loadWidgetPlugin({ type_name: "text_widget", display_name: "Text", external_scripts: ["/assets/Freeboard/plugins/thirdparty/jquery.sparkline.min.js"], settings: [{ name: "title", display_name: "Title", type: "text" }, { name: "size", display_name: "Size", type: "option", options: [{ name: "Regular", value: "regular" }, { name: "Big", value: "big" }] }, { name: "value", display_name: "Value", type: "calculated" }, { name: "sparkline", display_name: "Include Sparkline", type: "boolean" }, { name: "animate", display_name: "Animate Value Changes", type: "boolean", default_value: !0 }, { name: "units", display_name: "Units", type: "text" }], newInstance: function (a, b) { b(new g(a)) } }); var h = 0; freeboard.addStyle(".gauge-widget-wrapper", "width: 100%;text-align: center;"), freeboard.addStyle(".gauge-widget", "width:200px;height:160px;display:inline-block;"); var i = function (a) { function b() { g && (f.empty(), c = new JustGage({ id: d, value: _.isUndefined(i.min_value) ? 0 : i.min_value, min: _.isUndefined(i.min_value) ? 0 : i.min_value, max: _.isUndefined(i.max_value) ? 0 : i.max_value, label: i.units, showInnerShadow: !1, valueFontColor: "#d3d4d4" })) } var c, d = "gauge-" + h++, e = $('<h2 class="section-title"></h2>'), f = $('<div class="gauge-widget" id="' + d + '"></div>'), g = !1, i = a; this.render = function (a) { g = !0, $(a).append(e).append($('<div class="gauge-widget-wrapper"></div>').append(f)), b() }, this.onSettingsChanged = function (a) { a.min_value != i.min_value || a.max_value != i.max_value || a.units != i.units ? (i = a, b()) : i = a, e.html(a.title) }, this.onCalculatedValueChanged = function (a, b) { _.isUndefined(c) || c.refresh(Number(b)) }, this.onDispose = function () { }, this.getHeight = function () { return 3 }, this.onSettingsChanged(a) }; freeboard.loadWidgetPlugin({ type_name: "gauge", display_name: "Gauge", external_scripts: ["/assets/Freeboard/plugins/thirdparty/raphael.2.1.0.min.js", "/assets/Freeboard/plugins/thirdparty/justgage.1.0.1.js"], settings: [{ name: "title", display_name: "Title", type: "text" }, { name: "value", display_name: "Value", type: "calculated" }, { name: "units", display_name: "Units", type: "text" }, { name: "min_value", display_name: "Minimum", type: "text", default_value: 0 }, { name: "max_value", display_name: "Maximum", type: "text", default_value: 100 }], newInstance: function (a, b) { b(new i(a)) } }), freeboard.addStyle(".sparkline", "width:100%;height: 75px;"); var j = function (a) { var d = $('<h2 class="section-title"></h2>'), e = $('<div class="sparkline"></div>'), f = $("<div></div>"), g = a; this.render = function (a) { $(a).append(d).append(e).append(f) }, this.onSettingsChanged = function (a) { g = a, d.html(_.isUndefined(a.title) ? "" : a.title), a.include_legend && b(f, a.legend.split(",")) }, this.onCalculatedValueChanged = function (a, b) { g.legend ? c(e, b, g.legend.split(",")) : c(e, b) }, this.onDispose = function () { }, this.getHeight = function () { var a = 0; if (g.include_legend && g.legend) { var b = g.legend.split(",").length; b > 4 ? a = .5 * Math.floor((b - 1) / 4) : b && (a = .5) } return 2 + a }, this.onSettingsChanged(a) }; freeboard.loadWidgetPlugin({ type_name: "sparkline", display_name: "Sparkline", external_scripts: ["plugins/thirdparty/jquery.sparkline.min.js"], settings: [{ name: "title", display_name: "Title", type: "text" }, { name: "value", display_name: "Value", type: "calculated", multi_input: "true" }, { name: "include_legend", display_name: "Include Legend", type: "boolean" }, { name: "legend", display_name: "Legend", type: "text", description: "Comma-separated for multiple sparklines" }], newInstance: function (a, b) { b(new j(a)) } }), freeboard.addStyle("div.pointer-value", "position:absolute;height:95px;margin: auto;top: 0px;bottom: 0px;width: 100%;text-align:center;"); var k = function (a) { function b(a) { if (!a || a.length < 2) return []; var b = []; b.push(["m", a[0], a[1]]); for (var c = 2; c < a.length; c += 2)b.push(["l", a[c], a[c + 1]]); return b.push(["z"]), b } var c, d, e, f, g = 3, h = 0, i = $('<div class="widget-big-text"></div>'), j = $("<div></div>"); this.render = function (a) { e = $(a).width(), f = $(a).height(); var h = Math.min(e, f) / 2 - 2 * g; c = Raphael($(a).get()[0], e, f); var k = c.circle(e / 2, f / 2, h); k.attr("stroke", "#FF9900"), k.attr("stroke-width", g), d = c.path(b([e / 2, f / 2 - h + g, 15, 20, -30, 0])), d.attr("stroke-width", 0), d.attr("fill", "#fff"), $(a).append($('<div class="pointer-value"></div>').append(i).append(j)) }, this.onSettingsChanged = function (a) { j.html(a.units) }, this.onCalculatedValueChanged = function (a, b) { if ("direction" == a) { if (!_.isUndefined(d)) { d.animate({ transform: "r" + b + "," + e / 2 + "," + f / 2 }, 250, "bounce") } h = b } else "value_text" == a && i.html(b) }, this.onDispose = function () { }, this.getHeight = function () { return 4 }, this.onSettingsChanged(a) }; freeboard.loadWidgetPlugin({ type_name: "pointer", display_name: "Pointer", external_scripts: ["plugins/thirdparty/raphael.2.1.0.min.js"], settings: [{ name: "direction", display_name: "Direction", type: "calculated", description: "In degrees" }, { name: "value_text", display_name: "Value Text", type: "calculated" }, { name: "units", display_name: "Units", type: "text" }], newInstance: function (a, b) { b(new k(a)) } }); var l = function (a) { function b() { e && (clearInterval(e), e = null) } function c() { if (d && f) { var a = f + (-1 == f.indexOf("?") ? "?" : "&") + Date.now(); $(d).css({ "background-image": "url(" + a + ")" }) } } var d, e, f; this.render = function (a) { $(a).css({ width: "100%", height: "100%", "background-size": "cover", "background-position": "center" }), d = a }, this.onSettingsChanged = function (a) { b(), a.refresh && a.refresh > 0 && (e = setInterval(c, 1e3 * Number(a.refresh))) }, this.onCalculatedValueChanged = function (a, b) { "src" == a && (f = b), c() }, this.onDispose = function () { b() }, this.getHeight = function () { return 4 }, this.onSettingsChanged(a) }; freeboard.loadWidgetPlugin({ type_name: "picture", display_name: "Picture", fill_size: !0, settings: [{ name: "src", display_name: "Image URL", type: "calculated" }, { type: "number", display_name: "Refresh every", name: "refresh", suffix: "seconds", description: "Leave blank if the image doesn't need to be refreshed" }], newInstance: function (a, b) { b(new l(a)) } }), freeboard.addStyle(".indicator-light", "border-radius:50%;width:22px;height:22px;border:2px solid #3d3d3d;margin-top:5px;float:left;background-color:#222;margin-right:10px;"), freeboard.addStyle(".indicator-light.on", "background-color:#FFC773;box-shadow: 0px 0px 15px #FF9900;border-color:#FDF1DF;"), freeboard.addStyle(".indicator-text", "margin-top:10px;"); var m = function (a) { function b() { g.toggleClass("on", i), i ? f.text(_.isUndefined(c) ? _.isUndefined(h.on_text) ? "" : h.on_text : c) : f.text(_.isUndefined(d) ? _.isUndefined(h.off_text) ? "" : h.off_text : d) } var c, d, e = $('<h2 class="section-title"></h2>'), f = $('<div class="indicator-text"></div>'), g = $('<div class="indicator-light"></div>'), h = a, i = !1; this.render = function (a) { $(a).append(e).append(g).append(f) }, this.onSettingsChanged = function (a) { h = a, e.html(_.isUndefined(a.title) ? "" : a.title), b() }, this.onCalculatedValueChanged = function (a, e) { "value" == a && (i = Boolean(e)), "on_text" == a && (c = e), "off_text" == a && (d = e), b() }, this.onDispose = function () { }, this.getHeight = function () { return 1 }, this.onSettingsChanged(a) }; freeboard.loadWidgetPlugin({ type_name: "indicator", display_name: "Indicator Light", settings: [{ name: "title", display_name: "Title", type: "text" }, { name: "value", display_name: "Value", type: "calculated" }, { name: "on_text", display_name: "On Text", type: "calculated" }, { name: "off_text", display_name: "Off Text", type: "calculated" }], newInstance: function (a, b) { b(new m(a)) } }), freeboard.addStyle(".gm-style-cc a", "text-shadow:none;"); var n = function (a) { function b() { if (c && d && f.lat && f.lon) { var a = new google.maps.LatLng(f.lat, f.lon); d.setPosition(a), c.panTo(a) } } var c, d, e = a, f = {}; this.render = function (a) { function e() { var e = { zoom: 13, center: new google.maps.LatLng(37.235, -115.811111), disableDefaultUI: !0, draggable: !1, styles: [{ featureType: "water", elementType: "geometry", stylers: [{ color: "#2a2a2a" }] }, { featureType: "landscape", elementType: "geometry", stylers: [{ color: "#000000" }, { lightness: 20 }] }, { featureType: "road.highway", elementType: "geometry.fill", stylers: [{ color: "#000000" }, { lightness: 17 }] }, { featureType: "road.highway", elementType: "geometry.stroke", stylers: [{ color: "#000000" }, { lightness: 29 }, { weight: .2 }] }, { featureType: "road.arterial", elementType: "geometry", stylers: [{ color: "#000000" }, { lightness: 18 }] }, { featureType: "road.local", elementType: "geometry", stylers: [{ color: "#000000" }, { lightness: 16 }] }, { featureType: "poi", elementType: "geometry", stylers: [{ color: "#000000" }, { lightness: 21 }] }, { elementType: "labels.text.stroke", stylers: [{ visibility: "on" }, { color: "#000000" }, { lightness: 16 }] }, { elementType: "labels.text.fill", stylers: [{ saturation: 36 }, { color: "#000000" }, { lightness: 40 }] }, { elementType: "labels.icon", stylers: [{ visibility: "off" }] }, { featureType: "transit", elementType: "geometry", stylers: [{ color: "#000000" }, { lightness: 19 }] }, { featureType: "administrative", elementType: "geometry.fill", stylers: [{ color: "#000000" }, { lightness: 20 }] }, { featureType: "administrative", elementType: "geometry.stroke", stylers: [{ color: "#000000" }, { lightness: 17 }, { weight: 1.2 }] }] }; c = new google.maps.Map(a, e), google.maps.event.addDomListener(a, "mouseenter", function (a) { a.cancelBubble = !0, c.hover || (c.hover = !0, c.setOptions({ zoomControl: !0 })) }), google.maps.event.addDomListener(a, "mouseleave", function (a) { c.hover && (c.setOptions({ zoomControl: !1 }), c.hover = !1) }), d = new google.maps.Marker({ map: c }), b() } window.google && window.google.maps ? e() : (window.gmap_initialize = e, head.js("https://maps.googleapis.com/maps/api/js?v=3.exp&sensor=false&callback=gmap_initialize")) }, this.onSettingsChanged = function (a) { e = a }, this.onCalculatedValueChanged = function (a, c) { "lat" == a ? f.lat = c : "lon" == a && (f.lon = c), b() }, this.onDispose = function () { }, this.getHeight = function () { return 4 }, this.onSettingsChanged(a) }; freeboard.loadWidgetPlugin({ type_name: "google_map", display_name: "Google Map", fill_size: !0, settings: [{ name: "lat", display_name: "Latitude", type: "calculated" }, { name: "lon", display_name: "Longitude", type: "calculated" }], newInstance: function (a, b) { b(new n(a)) } }), freeboard.addStyle(".html-widget", "white-space:normal;width:100%;height:100%"); var o = function (a) { var b = $('<div class="html-widget"></div>'), c = a; this.render = function (a) { $(a).append(b) }, this.onSettingsChanged = function (a) { c = a }, this.onCalculatedValueChanged = function (a, c) { "html" == a && b.html(c) }, this.onDispose = function () { }, this.getHeight = function () { return Number(c.height) }, this.onSettingsChanged(a) }; freeboard.loadWidgetPlugin({ type_name: "html", display_name: "HTML", fill_size: !0, settings: [{ name: "html", display_name: "HTML", type: "calculated", description: "Can be literal HTML, or javascript that outputs HTML." }, { name: "height", display_name: "Height Blocks", type: "number", default_value: 4, description: "A height block is around 60 pixels" }], newInstance: function (a, b) { b(new o(a)) } }) }();