// ┌────────────────────────────────────────────────────────────────────┐ \\
// │ iot-switch-plugin                                                  │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ https://beeblock.vn                                                │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ Licensed under the MIT license.                                    │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ Freeboard widget plugin for Highcharts.                            │ \\
// └────────────────────────────────────────────────────────────────────┘ \\
(function()
{
    //
    // DECLARATIONS
    //
    var LOADING_INDICATOR_DELAY = 1000;
    var SWITCH_ID = 0;
    //

    freeboard.loadWidgetPlugin({
        type_name: "beeSwitch",
        display_name: "Bee Switch",
        description : "Interactive on-off switch",
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text"
            },
            {
                name: "icon_switch",
                display_name: "Icon",
                type: "option",
                default: "fas fa-lightbulb",
				options: widget_icon
            },
            {
                name: "topic",
                display_name: "Topic",
                type: "calculated",
            },
            {
                name: "value",
                display_name: "Value",
                type: "calculated"
            },
            {
                name: "on_text",
                display_name: "Payload On",
                type: "text",
                default_value: 'On'
            },
            {
                name: "off_text",
                display_name: "Payload Off",
                type: "text",
                default_value: 'Off'
            },
            {
                name: "show_value",
                display_name: "Show value",
                type: "option",
                default: "hide",
				options: [
					{
						value: "hide",
						name: "Hide",
					},
					{
						value: "show",
						name: "Show",
					}
				]
            }
        ],
        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new beeSwitchWidget(settings));
        }
    });

    freeboard.addStyle ('.floating-box',"display: inline-block; vertical-align: top; width: 78px; background-color: transparent; margin-top: 10px; margin-right: 5px;");
    freeboard.addStyle ('.onoffswitch-title',"font-size: 17px; line-height: 29px; width: 65%; height: 29px; padding-left: 0px;");
    freeboard.addStyle ('.icon-title', "font-size: 20px; margin-top: 15px; margin-right: 15px;")
    freeboard.addStyle ('.round' ,"border-radius: 50%;");

    var beeSwitchWidget = function (settings) {
        var self = this;
        var thisWidgetId = "onoffswitch-" + SWITCH_ID++;
        var currentSettings = settings;
        var box1 =  $('<div class="floating-box" style="position: absolute; right: 5px;"></div>');
        var box2 =  $('<div class="floating-box onoffswitch-title" style=" color: black;">' + settings.title + '</div>');
        var iconElement = $('<i class="' + settings.icon_switch + '"></i>').addClass('icon-title');
        var onOffSwitch = $('<div class="onoffswitch"><label class="onoffswitch-label" for="'+ thisWidgetId +'"><div class="onoffswitch-inner"><span class="on"></span><span class="off"></span></div><div class="onoffswitch-switch round"></div></label></div>');

        //onOffSwitch.find("span.on").text("True");

        onOffSwitch.prependTo(box1);

        var isOn = false;
        var onText;
        var offText;
        var topic;
        var show_value = "hide";

        function updateState() {
            $('#'+thisWidgetId).prop('checked', isOn);
            if (isOn) {
                iconElement.attr('style', 'color: #F1C40F')
            } else {
                iconElement.removeAttr('style')
            }
            if (show_value == "show") {
                onOffSwitch.find("span.on").text(onText);
                onOffSwitch.find("span.off").text(offText);
//                onOffSwitch.show();
            } else {
//                onOffSwitch.hide();
                onOffSwitch.find("span.on").text("");
                onOffSwitch.find("span.off").text("");
            }
        }

        this.render = function (element) {
            $(element).append(box1).append(iconElement).append(box2);
            var input = $('<input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="'+ thisWidgetId +'">').prependTo(onOffSwitch).change(function() {
                isOn =!isOn;
                payload = (isOn) ? currentSettings.on_text: currentSettings.off_text;
                mqttClient.send(topic, payload);
            });
        }

        this.onSettingsChanged = function (newSettings) {
            currentSettings = newSettings;
            box2.html((_.isUndefined(newSettings.title) ? "" : newSettings.title));
            iconElement.removeAttr('class').addClass(newSettings.icon_switch).addClass('icon-title');
            onText = newSettings.on_text;
            offText = newSettings.off_text;
            show_value = newSettings.show_value;
            updateState();
        }

        this.onCalculatedValueChanged = function (settingName, newValue) {
            if (settingName == "value" && newValue != null) {
                if (newValue == currentSettings.on_text) {
                    isOn = true;
                }
                else {
                    isOn = false;
                }
            }
            if (settingName == "topic") topic = newValue;

            updateState();
        }

        this.onDispose = function () {
        }

        this.getHeight = function () {
            return 1;
        }

        this.onSettingsChanged(settings);
    };

}());
