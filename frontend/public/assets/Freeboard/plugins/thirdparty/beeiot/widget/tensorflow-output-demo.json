{"version": 1, "allow_edit": true, "plugins": [], "panes": [{"title": "TensorFlow Camera", "width": 1, "row": {"3": 1, "4": 1}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "tensorflow_camera", "settings": {"title": "AI Camera Detection", "model_type": "cocossd", "camera_facing": "environment", "confidence_threshold": 0.5, "prediction_interval": 1000, "show_predictions": true, "show_bounding_boxes": true, "max_predictions": 3, "output_datasource": "ai_camera_results"}}]}, {"title": "Detection Results", "width": 1, "row": {"3": 1, "4": 1}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "tensorflow_display", "settings": {"title": "AI Detection Results", "data_source": "datasources[\"ai_camera_results\"]", "display_mode": "list", "max_items": 5, "show_confidence": true, "show_timestamp": true, "auto_refresh": 0}}]}, {"title": "Top Detection", "width": 1, "row": {"3": 2, "4": 2}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "text_widget", "settings": {"title": "Current Object", "size": "big", "value": "datasources[\"ai_camera_results\"] && datasources[\"ai_camera_results\"][\"predictions\"] && datasources[\"ai_camera_results\"][\"predictions\"][0] ? (datasources[\"ai_camera_results\"][\"predictions\"][0][\"class\"] || datasources[\"ai_camera_results\"][\"predictions\"][0][\"className\"]) : \"No detection\"", "animate": true}}]}, {"title": "Confidence Level", "width": 1, "row": {"3": 2, "4": 2}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "gauge", "settings": {"title": "Detection Confidence", "value": "datasources[\"ai_camera_results\"] && datasources[\"ai_camera_results\"][\"predictions\"] && datasources[\"ai_camera_results\"][\"predictions\"][0] ? (datasources[\"ai_camera_results\"][\"predictions\"][0][\"score\"] || datasources[\"ai_camera_results\"][\"predictions\"][0][\"probability\"]) * 100 : 0", "units": "%", "min_value": 0, "max_value": 100}}]}, {"title": "Detection Count", "width": 1, "row": {"3": 3, "4": 3}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "text_widget", "settings": {"title": "Objects Detected", "size": "regular", "value": "datasources[\"ai_camera_results\"] && datasources[\"ai_camera_results\"][\"predictions\"] ? datasources[\"ai_camera_results\"][\"predictions\"].length + \" objects\" : \"0 objects\"", "animate": false}}]}, {"title": "Model Info", "width": 1, "row": {"3": 3, "4": 3}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "html", "settings": {"title": "AI Model Status", "html": "var data = datasources[\"ai_camera_results\"]; if (data) { return \"<p><strong>Model:</strong> \" + (data.model_type || 'Unknown') + \"</p><p><strong>Last Update:</strong> \" + (data.timestamp ? new Date(data.timestamp).toLocaleTimeString() : 'Never') + \"</p><p><strong>Threshold:</strong> \" + ((data.confidence_threshold || 0.5) * 100) + \"%</p>\"; } else { return \"<p>No data available</p>\"; }", "height": 3}}]}, {"title": "Raw Data Debug", "width": 1, "row": {"3": 4, "4": 4}, "col": {"3": 1, "4": 1}, "col_width": 2, "widgets": [{"type": "html", "settings": {"title": "Raw JSON Output", "html": "var data = datasources[\"ai_camera_results\"]; return data ? \"<pre style='font-size: 12px; max-height: 200px; overflow-y: auto;'>\" + JSON.stringify(data, null, 2) + \"</pre>\" : \"<p>No data</p>\";", "height": 4}}]}], "datasources": [{"name": "ai_camera_results", "type": "JSON", "settings": {"url": "", "use_thingproxy": false, "refresh": 0, "method": "GET"}}], "columns": 3}