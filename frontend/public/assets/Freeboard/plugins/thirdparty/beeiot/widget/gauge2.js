(function () {
    var gauge2ID = 0;
    freeboard.loadWidgetPlugin({
        type_name: "beeGauge2",
        display_name: "Bee Gauge 2",
        "external_scripts": [
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/lib/gauge-coffee.js"
        ],
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text"
            },
            {
                name: "value",
                display_name: "Value",
                type: "calculated",
            },
            {
                name: "units",
                display_name: "Units",
                default_value: "",
                type: "text"
            },
            {
                name: "min_value",
                display_name: "Minimum",
                type: "text",
                default_value: 0
            },
            {
                name: "max_value",
                display_name: "Maximum",
                type: "text",
                default_value: 100
            }
        ],
        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new beeGaugeWidget(settings));
        }
    });

    freeboard.addStyle('.bee-gauge-widget-wrapper', "width: 100%; text-align: center;");
    freeboard.addStyle('.bee-gauge-widget', "transform: translateX(-10px);");
    freeboard.addStyle('.bee-text-value', 'position: relative; text-align: center; bottom: 80px; font-size: 24px; font-weight: bold; color: black;')

    var beeGaugeWidget = function (settings) {
        var self = this;

        var thisGaugeID = "gauge2-" + gauge2ID++;
        var titleElement = $('<h2 class="section-title"></h2>');
        var gaugeElement = $('<canvas class="bee-gauge-widget" id="' + thisGaugeID + '"></canvas>');
        var valueElement = $('<div class="bee-text-value"></div>');
        var gaugeObject;
        var rendered = false;

        var currentSettings = settings;

        function createGauge() {
            if (!rendered) {
                return;
            }

            gaugeElement.empty();
            max = (_.isUndefined(currentSettings.max_value) ? 0 : currentSettings.max_value);
            var opts = {
                angle: 0.35, // The span of the gauge arc
                lineWidth: 0.1, // The line thickness
                radiusScale: 1, // Relative radius
                pointer: {
                    length: 0.6, // // Relative to gauge radius
                    strokeWidth: 0.035, // The thickness
                    color: '#000000' // Fill color
                },
                limitMax: false,     // If false, max value increases automatically if value > maxValue
                limitMin: false,     // If true, the min value of the gauge will be fixed
                colorStart: '#3b71ca',   // Colors
                colorStop: '#ff0000',    // just experiment with them
                strokeColor: '#EEEEEE',  // to see which ones work best for you
                generateGradient: true,
                highDpiSupport: true,     // High resolution support
            };
            var target = document.getElementById(thisGaugeID);
            gaugeObject = new Donut(target).setOptions(opts); // create sexy gauge!
            gaugeObject.maxValue = max; // set max gauge value
            gaugeObject.setMinValue((_.isUndefined(currentSettings.min_value) ? 0 : currentSettings.min_value));  // Prefer setter over gauge.minValue = 0
            gaugeObject.animationSpeed = 32; // set animation speed (32 is default value)
            gaugeObject.set((_.isUndefined(currentSettings.min_value) ? 0 : currentSettings.min_value)); // set actual value
        }

        this.render = function (element) {
            rendered = true;
            $(element).append(titleElement).append($('<div class="bee-gauge-widget-wrapper"></div>').append(gaugeElement).append(valueElement));
            createGauge();
        }

        this.onSettingsChanged = function (newSettings) {
            if (newSettings.min_value != currentSettings.min_value || newSettings.max_value != currentSettings.max_value || newSettings.units != currentSettings.units) {
                currentSettings = newSettings;
                createGauge();
            }
            else {
                currentSettings = newSettings;
            }

            titleElement.html(newSettings.title);
        }

        this.onCalculatedValueChanged = function (settingName, newValue) {

            if (!_.isUndefined(gaugeObject)) {
                if (settingName == "value" && newValue != null) {
                    gaugeObject.set(Number(newValue)); // set actual value
                    add_unit = "";
                    if (currentSettings.units != "" && !_.isUndefined(currentSettings.units)) add_unit = currentSettings.units;

                    valueElement.html(newValue + add_unit);
                }
            }
        }

        this.onDispose = function () {
        }

        this.getHeight = function () {
            return 3;
        }

        this.onSettingsChanged(settings);
    };

}());