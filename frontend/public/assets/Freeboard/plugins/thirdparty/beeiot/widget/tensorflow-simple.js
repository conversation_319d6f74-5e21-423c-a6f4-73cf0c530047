// Simple TensorFlow Display Widget for Testing
(function() {
    console.log("🧪 Loading Simple TensorFlow Widget...");
    
    freeboard.loadWidgetPlugin({
        type_name: "tensorflow_simple",
        display_name: "TensorFlow Simple Test",
        description: "Simple test widget for TensorFlow integration",
        fill_size: false,
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text",
                default_value: "TensorFlow Test"
            },
            {
                name: "data_source",
                display_name: "Data Source",
                type: "calculated",
                description: "Data source for testing"
            }
        ],
        
        newInstance: function(settings, newInstanceCallback) {
            newInstanceCallback(new tensorflowSimpleWidget(settings));
        }
    });

    var tensorflowSimpleWidget = function(settings) {
        var currentSettings = settings;
        var widgetElement;
        var currentData = null;

        function createWidgetHTML() {
            var html = '<div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px;">';
            
            if (currentSettings.title) {
                html += '<h3 style="margin: 0 0 10px 0; color: #333;">' + currentSettings.title + '</h3>';
            }
            
            html += '<div id="content" style="font-family: Arial, sans-serif;">No data</div>';
            html += '</div>';
            
            return html;
        }

        function updateDisplay() {
            if (!widgetElement) return;
            
            var contentElement = widgetElement.find('#content');
            
            if (currentData) {
                var html = '<strong>Data received:</strong><br>';
                html += '<pre style="background: #f5f5f5; padding: 10px; border-radius: 3px; font-size: 12px;">';
                html += JSON.stringify(currentData, null, 2);
                html += '</pre>';
                contentElement.html(html);
            } else {
                contentElement.html('No data available');
            }
        }

        this.render = function(containerElement) {
            console.log("🎨 Rendering Simple TensorFlow Widget");
            widgetElement = $(containerElement);
            widgetElement.html(createWidgetHTML());
            updateDisplay();
        };

        this.getHeight = function() {
            return 2;
        };

        this.onSettingsChanged = function(newSettings) {
            console.log("⚙️ Settings changed:", newSettings);
            currentSettings = newSettings;
            if (widgetElement) {
                widgetElement.html(createWidgetHTML());
                updateDisplay();
            }
        };

        this.onCalculatedValueChanged = function(settingName, newValue) {
            console.log("📊 Data changed:", settingName, newValue);
            if (settingName === 'data_source') {
                currentData = newValue;
                updateDisplay();
            }
        };

        this.onDispose = function() {
            console.log("🗑️ Disposing Simple TensorFlow Widget");
        };
    };
    
    console.log("✅ Simple TensorFlow Widget loaded successfully");
}());
