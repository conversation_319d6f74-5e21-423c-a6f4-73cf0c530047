// TensorFlow.js Camera Widget for Freeboard
(function () {
    freeboard.loadWidgetPlugin({
        type_name: "tensorflow_camera",
        display_name: "TensorFlow.js Camera",
        description: "Display camera feed with real-time AI predictions",
        fill_size: true,
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text",
            },
            {
                name: "camera_facing",
                display_name: "Camera Facing",
                type: "option",
                options: [
                    {
                        name: "Back Camera",
                        value: "environment",
                    },
                    {
                        name: "Front Camera",
                        value: "user",
                    },
                ],
                default_value: "environment",
            },
            {
                name: "model_type",
                display_name: "Model Type",
                type: "option",
                options: [
                    {
                        name: "MobileNet (Image Classification)",
                        value: "mobilenet",
                    },
                    {
                        name: "COCO-SSD (Object Detection)",
                        value: "cocossd",
                    },
                    {
                        name: "PoseNet (Pose Detection)",
                        value: "posenet",
                    },
                    {
                        name: "Custom Model",
                        value: "custom",
                    },
                    {
                        name: "Teachable Machine",
                        value: "teachable_machine",
                    },
                ],
                default_value: "mobilenet",
            },
            {
                name: "model_url",
                display_name: "Custom Model URL",
                type: "text",
                description: "URL to custom TensorFlow.js model (only for Custom Model type)",
            },
            {
                name: "prediction_interval",
                display_name: "Prediction Interval (ms)",
                type: "number",
                default_value: 1000,
            },
            {
                name: "show_predictions",
                display_name: "Show Predictions",
                type: "boolean",
                default_value: true,
            },
            {
                name: "show_bounding_boxes",
                display_name: "Show Bounding Boxes",
                type: "boolean",
                default_value: true,
                description: "Draw bounding boxes and pose keypoints on video",
            },
            {
                name: "max_predictions",
                display_name: "Max Predictions to Show",
                type: "number",
                default_value: 3,
            },
            {
                name: "confidence_threshold",
                display_name: "Confidence Threshold",
                type: "number",
                default_value: 0.5,
                description: "Minimum confidence score (0.0 - 1.0)",
            },
            {
                name: "output_datasource",
                display_name: "Output Data Source",
                type: "text",
                description: "Data source name to send predictions to other widgets",
            },
        ],
        external_scripts: [
            "https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.21.0/dist/tf.min.js",
            "https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.1.1/dist/mobilenet.min.js",
            "https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2.2.2/dist/coco-ssd.min.js",
            "https://cdn.jsdelivr.net/npm/@tensorflow-models/posenet@2.2.2/dist/posenet.min.js",
            "https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8.6/dist/teachablemachine-image.min.js",
        ],

        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new tensorflowCameraWidget(settings));
        },
    });

    var tensorflowCameraWidget = function (settings) {
        var self = this;
        var currentSettings = settings;
        var widgetElement;
        var videoElement;
        var canvasElement;
        var overlayCanvas;
        var overlayContext;
        var predictionElement;
        var model = null;
        var predictionTimer = null;
        var isModelLoaded = false;
        var stream = null;

        function createWidgetHTML() {
            var html = '<div style="width: 100%; height: 100%; position: relative; background: #000;">';

            if (currentSettings.title) {
                html +=
                    '<div style="position: absolute; top: 10px; left: 10px; color: white; font-weight: bold; z-index: 10; background: rgba(0,0,0,0.7); padding: 5px 10px; border-radius: 5px;">' +
                    currentSettings.title +
                    "</div>";
            }

            html +=
                '<video id="camera-video" autoplay playsinline muted style="width: 100%; height: 100%; object-fit: cover;"></video>';

            // Add canvas overlay for bounding boxes
            html +=
                '<canvas id="overlay-canvas" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 5;"></canvas>';

            if (currentSettings.show_predictions) {
                html +=
                    '<div id="predictions" style="position: absolute; bottom: 10px; left: 10px; right: 10px; color: white; background: rgba(0,0,0,0.8); padding: 10px; border-radius: 5px; font-size: 14px; max-height: 150px; overflow-y: auto;"></div>';
            }

            html +=
                '<div id="status" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; background: rgba(0,0,0,0.8); padding: 10px; border-radius: 5px; display: none;">Loading...</div>';
            html += "</div>";

            return html;
        }

        function initializeCamera() {
            videoElement = widgetElement.find("#camera-video")[0];
            predictionElement = widgetElement.find("#predictions")[0];
            overlayCanvas = widgetElement.find("#overlay-canvas")[0];
            var statusElement = widgetElement.find("#status")[0];

            // Initialize overlay canvas context
            if (overlayCanvas) {
                overlayContext = overlayCanvas.getContext("2d");
                // Set canvas size to match video
                overlayCanvas.width = 640;
                overlayCanvas.height = 480;
            }

            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                statusElement.style.display = "block";
                statusElement.textContent = "Requesting camera access...";

                navigator.mediaDevices
                    .getUserMedia({
                        video: {
                            facingMode: currentSettings.camera_facing,
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                        },
                    })
                    .then(function (mediaStream) {
                        stream = mediaStream;
                        videoElement.srcObject = stream;
                        statusElement.style.display = "none";

                        videoElement.onloadedmetadata = function () {
                            loadModel();
                        };
                    })
                    .catch(function (error) {
                        console.error("Camera error:", error);
                        statusElement.textContent = "Camera access denied or not available";
                    });
            } else {
                statusElement.style.display = "block";
                statusElement.textContent = "Camera not supported in this browser";
            }
        }

        function loadModel() {
            var statusElement = widgetElement.find("#status")[0];
            statusElement.style.display = "block";
            statusElement.textContent = "Loading AI model...";

            var modelType = currentSettings.model_type || "mobilenet";

            switch (modelType) {
                case "mobilenet":
                    statusElement.textContent = "Loading MobileNet model...";
                    mobilenet
                        .load()
                        .then(function (loadedModel) {
                            model = loadedModel;
                            isModelLoaded = true;
                            statusElement.style.display = "none";
                            startPredictions();
                        })
                        .catch(function (error) {
                            console.error("MobileNet loading error:", error);
                            statusElement.textContent = "Error loading MobileNet model";
                        });
                    break;

                case "cocossd":
                    statusElement.textContent = "Loading COCO-SSD model...";
                    cocoSsd
                        .load()
                        .then(function (loadedModel) {
                            model = loadedModel;
                            isModelLoaded = true;
                            statusElement.style.display = "none";
                            startPredictions();
                        })
                        .catch(function (error) {
                            console.error("COCO-SSD loading error:", error);
                            statusElement.textContent = "Error loading COCO-SSD model";
                        });
                    break;

                case "posenet":
                    statusElement.textContent = "Loading PoseNet model...";
                    posenet
                        .load()
                        .then(function (loadedModel) {
                            model = loadedModel;
                            isModelLoaded = true;
                            statusElement.style.display = "none";
                            startPredictions();
                        })
                        .catch(function (error) {
                            console.error("PoseNet loading error:", error);
                            statusElement.textContent = "Error loading PoseNet model";
                        });
                    break;

                case "teachable_machine":
                    if (currentSettings.model_url && currentSettings.model_url.trim() !== "") {
                        // Check if tmImage library is loaded
                        if (typeof tmImage === "undefined") {
                            statusElement.textContent = "Teachable Machine library not loaded";
                            console.error(
                                "tmImage is not defined. Make sure @teachablemachine/image library is loaded."
                            );
                            return;
                        }

                        statusElement.textContent = "Loading Teachable Machine model...";
                        var modelUrl = currentSettings.model_url.trim();
                        if (!modelUrl.endsWith("/")) {
                            modelUrl += "/";
                        }
                        tmImage
                            .load(modelUrl + "model.json", modelUrl + "metadata.json")
                            .then(function (loadedModel) {
                                model = loadedModel;
                                isModelLoaded = true;
                                statusElement.style.display = "none";
                                startPredictions();
                            })
                            .catch(function (error) {
                                console.error("Teachable Machine loading error:", error);
                                statusElement.textContent = "Error loading Teachable Machine model: " + error.message;
                            });
                    } else {
                        statusElement.textContent = "Please provide a Teachable Machine model URL";
                    }
                    break;

                case "custom":
                    if (currentSettings.model_url && currentSettings.model_url.trim() !== "") {
                        statusElement.textContent = "Loading custom model...";
                        tf.loadLayersModel(currentSettings.model_url)
                            .then(function (loadedModel) {
                                model = loadedModel;
                                isModelLoaded = true;
                                statusElement.style.display = "none";
                                startPredictions();
                            })
                            .catch(function (error) {
                                console.error("Custom model loading error:", error);
                                statusElement.textContent = "Error loading custom model";
                            });
                    } else {
                        statusElement.textContent = "Please provide a custom model URL";
                    }
                    break;

                default:
                    statusElement.textContent = "Unknown model type";
            }
        }

        function clearOverlay() {
            if (overlayContext && overlayCanvas) {
                overlayContext.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
            }
        }

        function drawBoundingBox(x, y, width, height, label, confidence, color) {
            if (!overlayContext || !overlayCanvas) return;

            // Calculate scale factors
            var scaleX = overlayCanvas.width / videoElement.videoWidth;
            var scaleY = overlayCanvas.height / videoElement.videoHeight;

            // Scale coordinates
            var scaledX = x * scaleX;
            var scaledY = y * scaleY;
            var scaledWidth = width * scaleX;
            var scaledHeight = height * scaleY;

            // Set drawing style
            overlayContext.strokeStyle = color || "#00ff00";
            overlayContext.lineWidth = 3;
            overlayContext.fillStyle = color || "#00ff00";
            overlayContext.font = "16px Arial";

            // Draw bounding box
            overlayContext.strokeRect(scaledX, scaledY, scaledWidth, scaledHeight);

            // Draw label background
            var labelText = label + " (" + confidence + "%)";
            var textMetrics = overlayContext.measureText(labelText);
            var textWidth = textMetrics.width;
            var textHeight = 20;

            overlayContext.fillRect(scaledX, scaledY - textHeight, textWidth + 10, textHeight);

            // Draw label text
            overlayContext.fillStyle = "#000000";
            overlayContext.fillText(labelText, scaledX + 5, scaledY - 5);
        }

        function drawPoseKeypoints(pose, color) {
            if (!overlayContext || !overlayCanvas || !pose.keypoints) return;

            var scaleX = overlayCanvas.width / videoElement.videoWidth;
            var scaleY = overlayCanvas.height / videoElement.videoHeight;
            var confidenceThreshold = currentSettings.confidence_threshold || 0.5;

            // Draw keypoints
            overlayContext.fillStyle = color || "#ff0000";
            pose.keypoints.forEach(function (keypoint) {
                if (keypoint.score >= confidenceThreshold) {
                    var x = keypoint.position.x * scaleX;
                    var y = keypoint.position.y * scaleY;

                    overlayContext.beginPath();
                    overlayContext.arc(x, y, 5, 0, 2 * Math.PI);
                    overlayContext.fill();
                }
            });

            // Draw skeleton connections
            var adjacentKeyPoints = [
                [5, 6],
                [5, 7],
                [6, 8],
                [7, 9],
                [8, 10],
                [5, 11],
                [6, 12],
                [11, 12],
                [11, 13],
                [12, 14],
                [13, 15],
                [14, 16],
            ];

            overlayContext.strokeStyle = color || "#ff0000";
            overlayContext.lineWidth = 2;

            adjacentKeyPoints.forEach(function (pair) {
                var kp1 = pose.keypoints[pair[0]];
                var kp2 = pose.keypoints[pair[1]];

                if (kp1.score >= confidenceThreshold && kp2.score >= confidenceThreshold) {
                    overlayContext.beginPath();
                    overlayContext.moveTo(kp1.position.x * scaleX, kp1.position.y * scaleY);
                    overlayContext.lineTo(kp2.position.x * scaleX, kp2.position.y * scaleY);
                    overlayContext.stroke();
                }
            });
        }

        function startPredictions() {
            if (predictionTimer) {
                clearInterval(predictionTimer);
            }

            predictionTimer = setInterval(function () {
                if (isModelLoaded && videoElement && videoElement.readyState === 4) {
                    makePrediction();
                }
            }, currentSettings.prediction_interval || 1000);
        }

        function makePrediction() {
            if (!model || !videoElement) return;

            var modelType = currentSettings.model_type || "mobilenet";
            var confidenceThreshold = currentSettings.confidence_threshold || 0.5;

            // Clear previous overlay drawings
            clearOverlay();

            switch (modelType) {
                case "mobilenet":
                    model
                        .classify(videoElement)
                        .then(function (predictions) {
                            var filteredPredictions = predictions.filter((p) => p.probability >= confidenceThreshold);
                            if (predictionElement && filteredPredictions.length > 0) {
                                var maxPredictions = Math.min(
                                    filteredPredictions.length,
                                    currentSettings.max_predictions || 3
                                );
                                var html = "<strong>Image Classification:</strong><br>";

                                for (var i = 0; i < maxPredictions; i++) {
                                    var confidence = (filteredPredictions[i].probability * 100).toFixed(1);
                                    html += `${filteredPredictions[i].className}: ${confidence}%<br>`;
                                }

                                predictionElement.innerHTML = html;
                                sendToDataSource("mobilenet", filteredPredictions.slice(0, maxPredictions));
                            }
                        })
                        .catch(function (error) {
                            console.error("MobileNet prediction error:", error);
                        });
                    break;

                case "cocossd":
                    model
                        .detect(videoElement)
                        .then(function (predictions) {
                            var filteredPredictions = predictions.filter(function (p) {
                                return p.score >= confidenceThreshold;
                            });

                            // Draw bounding boxes for all filtered predictions if enabled
                            if (currentSettings.show_bounding_boxes !== false) {
                                var colors = ["#00ff00", "#ff0000", "#0000ff", "#ffff00", "#ff00ff", "#00ffff"];
                                for (var i = 0; i < filteredPredictions.length; i++) {
                                    var prediction = filteredPredictions[i];
                                    var bbox = prediction.bbox;
                                    var confidence = (prediction.score * 100).toFixed(1);
                                    var color = colors[i % colors.length];

                                    drawBoundingBox(
                                        bbox[0], // x
                                        bbox[1], // y
                                        bbox[2], // width
                                        bbox[3], // height
                                        prediction.class,
                                        confidence,
                                        color
                                    );
                                }
                            }

                            if (predictionElement) {
                                var maxPredictions = Math.min(
                                    filteredPredictions.length,
                                    currentSettings.max_predictions || 3
                                );
                                var html = "<strong>Object Detection:</strong><br>";

                                for (var i = 0; i < maxPredictions; i++) {
                                    var confidence = (filteredPredictions[i].score * 100).toFixed(1);
                                    var bbox = filteredPredictions[i].bbox;
                                    html +=
                                        filteredPredictions[i].class +
                                        ": " +
                                        confidence +
                                        "% [" +
                                        bbox[0].toFixed(0) +
                                        ", " +
                                        bbox[1].toFixed(0) +
                                        "]<br>";
                                }

                                predictionElement.innerHTML = html || "<strong>No objects detected</strong>";
                                sendToDataSource("cocossd", filteredPredictions.slice(0, maxPredictions));
                            }
                        })
                        .catch(function (error) {
                            console.error("COCO-SSD prediction error:", error);
                        });
                    break;

                case "posenet":
                    model
                        .estimateSinglePose(videoElement)
                        .then(function (pose) {
                            // Draw pose skeleton if score is above threshold and bounding boxes are enabled
                            if (pose.score >= confidenceThreshold && currentSettings.show_bounding_boxes !== false) {
                                // Draw the pose keypoints and skeleton
                                drawPoseKeypoints(pose, "#ff0000");
                            }

                            if (predictionElement && pose.score >= confidenceThreshold) {
                                var html = "<strong>Pose Detection:</strong><br>";
                                html += "Overall Score: " + (pose.score * 100).toFixed(1) + "%<br>";

                                var visibleKeypoints = pose.keypoints.filter(function (kp) {
                                    return kp.score >= confidenceThreshold;
                                });
                                html += "Visible Keypoints: " + visibleKeypoints.length + "/17<br>";

                                // Add key body parts with confidence
                                var keyBodyParts = [
                                    "nose",
                                    "leftEye",
                                    "rightEye",
                                    "leftShoulder",
                                    "rightShoulder",
                                    "leftWrist",
                                    "rightWrist",
                                    "leftHip",
                                    "rightHip",
                                ];
                                html += "<br><strong>Key Parts:</strong><br>";
                                keyBodyParts.forEach(function (part) {
                                    var keypoint = pose.keypoints.find(function (kp) {
                                        return kp.part === part;
                                    });
                                    if (keypoint) {
                                        var confidence = (keypoint.score * 100).toFixed(1);
                                        var color = keypoint.score >= confidenceThreshold ? "green" : "gray";
                                        html +=
                                            '<span style="color: ' +
                                            color +
                                            '">' +
                                            part +
                                            ": " +
                                            confidence +
                                            "%</span><br>";
                                    }
                                });

                                predictionElement.innerHTML = html;
                                sendToDataSource("posenet", { pose: pose, visibleKeypoints: visibleKeypoints.length });
                            } else {
                                predictionElement.innerHTML = "<strong>No pose detected</strong>";
                            }
                        })
                        .catch(function (error) {
                            console.error("PoseNet prediction error:", error);
                        });
                    break;

                case "teachable_machine":
                    model
                        .predict(videoElement)
                        .then(function (predictions) {
                            var filteredPredictions = predictions.filter(function (p) {
                                return p.probability >= confidenceThreshold;
                            });
                            if (predictionElement) {
                                var maxPredictions = Math.min(predictions.length, currentSettings.max_predictions || 3);
                                var html = "<strong>Teachable Machine:</strong><br>";

                                // Sort by probability
                                var sortedPredictions = predictions.slice().sort(function (a, b) {
                                    return b.probability - a.probability;
                                });

                                for (var i = 0; i < maxPredictions; i++) {
                                    var confidence = (sortedPredictions[i].probability * 100).toFixed(1);
                                    var isHighConfidence = sortedPredictions[i].probability >= confidenceThreshold;
                                    var style = isHighConfidence
                                        ? "color: #4CAF50; font-weight: bold;"
                                        : "color: #666;";
                                    html +=
                                        '<span style="' +
                                        style +
                                        '">' +
                                        sortedPredictions[i].className +
                                        ": " +
                                        confidence +
                                        "%</span><br>";
                                }

                                predictionElement.innerHTML = html;
                                sendToDataSource("teachable_machine", {
                                    all_predictions: predictions,
                                    high_confidence_predictions: filteredPredictions,
                                    top_prediction: sortedPredictions[0],
                                });
                            }
                        })
                        .catch(function (error) {
                            console.error("Teachable Machine prediction error:", error);
                        });
                    break;

                case "custom":
                    // Custom model prediction
                    const tensor = tf.browser
                        .fromPixels(videoElement)
                        .resizeNearestNeighbor([224, 224])
                        .toFloat()
                        .div(255.0)
                        .expandDims();

                    const prediction = model.predict(tensor);
                    const results = Array.from(prediction.dataSync());

                    if (predictionElement) {
                        predictionElement.innerHTML =
                            "<strong>Custom Model Output:</strong><br>" +
                            results
                                .slice(0, 5)
                                .map((val, idx) => `Output ${idx}: ${val.toFixed(4)}`)
                                .join("<br>");
                    }

                    sendToDataSource("custom", results.slice(0, 10));

                    tensor.dispose();
                    prediction.dispose();
                    break;
            }
        }

        function sendToDataSource(modelType, data) {
            if (currentSettings.output_datasource && currentSettings.output_datasource.trim() !== "") {
                var outputData = {
                    timestamp: new Date().toISOString(),
                    model_type: modelType,
                    data: data,
                };

                // Send data to Freeboard datasource
                if (typeof freeboard !== "undefined" && freeboard.setDatasourceValue) {
                    freeboard.setDatasourceValue(currentSettings.output_datasource, outputData);
                }
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach((track) => track.stop());
                stream = null;
            }

            if (predictionTimer) {
                clearInterval(predictionTimer);
                predictionTimer = null;
            }
        }

        this.render = function (containerElement) {
            widgetElement = $(containerElement);
            widgetElement.html(createWidgetHTML());
            initializeCamera();
        };

        this.getHeight = function () {
            return 4; // Default height in grid units
        };

        this.onSettingsChanged = function (newSettings) {
            stopCamera();
            currentSettings = newSettings;
            isModelLoaded = false;
            model = null;

            // Re-render with new settings
            widgetElement.html(createWidgetHTML());
            initializeCamera();
        };

        this.onCalculatedValueChanged = function (settingName, newValue) {
            // Handle dynamic value changes if needed
        };

        this.onDispose = function () {
            stopCamera();
        };
    };
})();
