// Teachable Machine Widget for Freeboard
(function () {
    console.log("🎓 Loading Teachable Machine Widget...");

    freeboard.loadWidgetPlugin({
        type_name: "teachable_machine",
        display_name: "Teachable Machine",
        description: "AI Camera using Google Teachable Machine models",
        fill_size: false,
        external_scripts: [
            "https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.21.0/dist/tf.min.js",
            "https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8.6/dist/teachablemachine-image.min.js",
        ],
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text",
                default_value: "Teachable Machine AI",
            },
            {
                name: "model_url",
                display_name: "Teachable Machine Model URL",
                type: "text",
                description:
                    "URL from Teachable Machine (e.g., https://teachablemachine.withgoogle.com/models/YOUR_MODEL_ID/)",
                default_value: "https://teachablemachine.withgoogle.com/models/86y3FI4B8/",
            },
            {
                name: "camera_facing",
                display_name: "Camera Facing",
                type: "option",
                options: [
                    {
                        name: "Back Camera",
                        value: "environment",
                    },
                    {
                        name: "Front Camera",
                        value: "user",
                    },
                ],
                default_value: "environment",
            },
            {
                name: "prediction_interval",
                display_name: "Prediction Interval (ms)",
                type: "number",
                default_value: 1000,
            },
            {
                name: "confidence_threshold",
                display_name: "Confidence Threshold",
                type: "number",
                default_value: 0.7,
                description: "Minimum confidence score (0.0 - 1.0)",
            },
            {
                name: "show_predictions",
                display_name: "Show Predictions",
                type: "boolean",
                default_value: true,
            },
            {
                name: "output_datasource",
                display_name: "Output Data Source",
                type: "text",
                description: "Data source name to send predictions to other widgets",
            },
        ],

        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new teachableMachineWidget(settings));
        },
    });

    var teachableMachineWidget = function (settings) {
        var currentSettings = settings;
        var widgetElement;
        var model = null;
        var isModelLoaded = false;
        var videoElement = null;
        var predictionElement = null;
        var predictionTimer = null;
        var modelMetadata = null;

        function createWidgetHTML() {
            var html = '<div style="position: relative; text-align: center; font-family: Arial, sans-serif;">';

            if (currentSettings.title) {
                html += '<h3 style="margin: 0 0 10px 0; color: #333;">' + currentSettings.title + "</h3>";
            }

            html += '<div style="position: relative; display: inline-block;">';
            html +=
                '<video id="video" width="320" height="240" autoplay muted style="border: 2px solid #ddd; border-radius: 8px;"></video>';
            html +=
                '<div id="status" style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px;">Initializing...</div>';
            html += "</div>";

            if (currentSettings.show_predictions) {
                html +=
                    '<div id="predictions" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border-radius: 5px; min-height: 60px;"></div>';
            }

            html += "</div>";
            return html;
        }

        function initializeCamera() {
            videoElement = widgetElement.find("#video")[0];

            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                var constraints = {
                    video: {
                        facingMode: currentSettings.camera_facing || "environment",
                        width: { ideal: 320 },
                        height: { ideal: 240 },
                    },
                };

                navigator.mediaDevices
                    .getUserMedia(constraints)
                    .then(function (stream) {
                        videoElement.srcObject = stream;
                        videoElement.play();
                        updateStatus("Camera ready", "success");
                        loadModel();
                    })
                    .catch(function (error) {
                        console.error("Camera error:", error);
                        updateStatus("Camera access denied", "error");
                    });
            } else {
                updateStatus("Camera not supported", "error");
            }
        }

        function loadModel() {
            if (!currentSettings.model_url || currentSettings.model_url.trim() === "") {
                updateStatus("No model URL provided", "error");
                return;
            }

            // Check if tmImage library is loaded, with retry mechanism
            if (typeof tmImage === "undefined") {
                updateStatus("Loading Teachable Machine library...", "loading");
                console.warn("tmImage not immediately available, attempting to load...");

                // Try to load the library manually
                var script = document.createElement("script");
                script.src =
                    "https://cdn.jsdelivr.net/npm/@teachablemachine/image@0.8.6/dist/teachablemachine-image.min.js";
                script.onload = function () {
                    console.log("✅ Teachable Machine library loaded manually");
                    setTimeout(loadModel, 1000); // Retry after 1 second
                };
                script.onerror = function () {
                    updateStatus("Failed to load Teachable Machine library", "error");
                    console.error("❌ Failed to load @teachablemachine/image library");
                };
                document.head.appendChild(script);
                return;
            }

            updateStatus("Loading AI model...", "loading");

            // Extract model URL - handle both full URL and just the model ID
            var modelUrl = currentSettings.model_url.trim();
            if (!modelUrl.endsWith("/")) {
                modelUrl += "/";
            }

            // Load model metadata first
            fetch(modelUrl + "metadata.json")
                .then(function (response) {
                    return response.json();
                })
                .then(function (metadata) {
                    modelMetadata = metadata;
                    console.log("Model metadata:", metadata);

                    // Load the actual model
                    return tmImage.load(modelUrl + "model.json", modelUrl + "metadata.json");
                })
                .then(function (loadedModel) {
                    model = loadedModel;
                    isModelLoaded = true;
                    updateStatus(
                        "Model loaded: " + (modelMetadata.labels ? modelMetadata.labels.length + " classes" : "Ready"),
                        "success"
                    );
                    startPredictions();
                })
                .catch(function (error) {
                    console.error("Model loading error:", error);
                    updateStatus("Error loading model", "error");
                });
        }

        function updateStatus(message, type) {
            if (!widgetElement) return;

            var statusElement = widgetElement.find("#status");
            var color = "#fff";
            var bgColor = "rgba(0,0,0,0.8)";

            switch (type) {
                case "success":
                    bgColor = "rgba(76, 175, 80, 0.9)";
                    break;
                case "error":
                    bgColor = "rgba(244, 67, 54, 0.9)";
                    break;
                case "loading":
                    bgColor = "rgba(255, 152, 0, 0.9)";
                    break;
            }

            statusElement.html(message).css({
                color: color,
                background: bgColor,
            });
        }

        function startPredictions() {
            if (predictionTimer) {
                clearInterval(predictionTimer);
            }

            var interval = currentSettings.prediction_interval || 1000;
            predictionTimer = setInterval(makePrediction, interval);
        }

        function makePrediction() {
            if (!model || !videoElement || !isModelLoaded) return;

            model
                .predict(videoElement)
                .then(function (predictions) {
                    if (currentSettings.show_predictions) {
                        displayPredictions(predictions);
                    }

                    // Send to data source
                    sendToDataSource(predictions);
                })
                .catch(function (error) {
                    console.error("Prediction error:", error);
                });
        }

        function displayPredictions(predictions) {
            if (!widgetElement) return;

            var predictionElement = widgetElement.find("#predictions");
            if (!predictionElement.length) return;

            var confidenceThreshold = currentSettings.confidence_threshold || 0.7;
            var html = "<strong>AI Predictions:</strong><br>";

            // Sort predictions by probability
            var sortedPredictions = predictions.slice().sort(function (a, b) {
                return b.probability - a.probability;
            });

            var hasHighConfidence = false;

            for (var i = 0; i < sortedPredictions.length; i++) {
                var prediction = sortedPredictions[i];
                var confidence = (prediction.probability * 100).toFixed(1);
                var isHighConfidence = prediction.probability >= confidenceThreshold;

                if (isHighConfidence) {
                    hasHighConfidence = true;
                }

                var style = isHighConfidence ? "color: #4CAF50; font-weight: bold;" : "color: #666;";

                html += '<div style="margin: 5px 0; ' + style + '">';
                html += prediction.className + ": " + confidence + "%";

                // Add confidence bar
                var barWidth = Math.min(prediction.probability * 100, 100);
                var barColor = isHighConfidence ? "#4CAF50" : "#ddd";
                html +=
                    '<div style="background: #f0f0f0; height: 8px; border-radius: 4px; margin-top: 2px; overflow: hidden;">';
                html +=
                    '<div style="background: ' +
                    barColor +
                    "; height: 100%; width: " +
                    barWidth +
                    '%; transition: width 0.3s ease;"></div>';
                html += "</div>";
                html += "</div>";
            }

            if (!hasHighConfidence) {
                html +=
                    '<div style="color: #FF9800; font-style: italic; margin-top: 10px;">No high-confidence predictions</div>';
            }

            predictionElement.html(html);
        }

        function sendToDataSource(predictions) {
            if (currentSettings.output_datasource && currentSettings.output_datasource.trim() !== "") {
                var confidenceThreshold = currentSettings.confidence_threshold || 0.7;
                var highConfidencePredictions = predictions.filter(function (p) {
                    return p.probability >= confidenceThreshold;
                });

                var outputData = {
                    timestamp: new Date().toISOString(),
                    model_type: "teachable_machine",
                    model_url: currentSettings.model_url,
                    confidence_threshold: confidenceThreshold,
                    all_predictions: predictions,
                    high_confidence_predictions: highConfidencePredictions,
                    top_prediction:
                        predictions.length > 0
                            ? predictions.reduce(function (max, p) {
                                  return p.probability > max.probability ? p : max;
                              })
                            : null,
                };

                // Send data to Freeboard datasource
                if (typeof freeboard !== "undefined" && freeboard.setDatasourceValue) {
                    freeboard.setDatasourceValue(currentSettings.output_datasource, outputData);
                }
            }
        }

        this.render = function (containerElement) {
            console.log("🎨 Rendering Teachable Machine Widget");
            widgetElement = $(containerElement);
            widgetElement.html(createWidgetHTML());
            predictionElement = widgetElement.find("#predictions");
            initializeCamera();
        };

        this.getHeight = function () {
            return 4;
        };

        this.onSettingsChanged = function (newSettings) {
            console.log("⚙️ Teachable Machine settings changed:", newSettings);
            currentSettings = newSettings;
            isModelLoaded = false;
            model = null;

            if (predictionTimer) {
                clearInterval(predictionTimer);
            }

            if (widgetElement) {
                widgetElement.html(createWidgetHTML());
                predictionElement = widgetElement.find("#predictions");
                initializeCamera();
            }
        };

        this.onDispose = function () {
            console.log("🗑️ Disposing Teachable Machine Widget");
            if (predictionTimer) {
                clearInterval(predictionTimer);
            }
            if (videoElement && videoElement.srcObject) {
                var tracks = videoElement.srcObject.getTracks();
                tracks.forEach(function (track) {
                    track.stop();
                });
            }
        };
    };

    console.log("✅ Teachable Machine Widget loaded successfully");
})();
