import os
import re
import json

path = r"/Users/<USER>/Library/CloudStorage/OneDrive-Personal/Business/7.xJob/3.Project/beeblock/BeeBot_IDE/assets/Freeboard/plugins/thirdparty/beeiot/widget/lib/"
f = open(os.path.join(path, "mdi-font.txt"), "r")
f.seek(0)
font = f.read()
f.close()

font_list = re.findall("\..*::", font)

data = []
for font_mdi in font_list:
    data.append({
        "value": "mdi " + font_mdi[1: -2],
        "name": font_mdi[1: -2].replace('mdi-', "mdi: "),
        "icon": "mdi " + font_mdi[1: -2]
    })

# Serializing json
json_object = json.dumps({"data": data}, indent=4)

# Writing to sample.json
with open(os.path.join(path, "mdi-font.json"), "w") as outfile:
    outfile.write(json_object)
