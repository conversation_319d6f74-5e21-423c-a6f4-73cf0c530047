// Debug script to check TensorFlow and Teachable Machine libraries
(function() {
    console.log("🔍 Checking TensorFlow.js and Teachable Machine libraries...");
    
    // Wait for libraries to load
    setTimeout(function() {
        console.log("📚 Library Status Check:");
        
        // Check TensorFlow.js
        if (typeof tf !== 'undefined') {
            console.log("✅ TensorFlow.js loaded successfully");
            console.log("   Version:", tf.version.tfjs);
            console.log("   Backend:", tf.getBackend());
        } else {
            console.error("❌ TensorFlow.js NOT loaded");
        }
        
        // Check TensorFlow Models
        if (typeof mobilenet !== 'undefined') {
            console.log("✅ MobileNet model loaded");
        } else {
            console.error("❌ MobileNet model NOT loaded");
        }
        
        if (typeof cocoSsd !== 'undefined') {
            console.log("✅ COCO-SSD model loaded");
        } else {
            console.error("❌ COCO-SSD model NOT loaded");
        }
        
        if (typeof posenet !== 'undefined') {
            console.log("✅ PoseNet model loaded");
        } else {
            console.error("❌ PoseNet model NOT loaded");
        }
        
        // Check Teachable Machine
        if (typeof tmImage !== 'undefined') {
            console.log("✅ Teachable Machine Image library loaded");
            console.log("   Available methods:", Object.keys(tmImage));
        } else {
            console.error("❌ Teachable Machine Image library NOT loaded");
            console.error("   This will cause 'tmImage is not defined' errors");
        }
        
        // Check global objects
        console.log("🌐 Global Objects Available:");
        console.log("   window.tf:", typeof window.tf);
        console.log("   window.tmImage:", typeof window.tmImage);
        console.log("   window.mobilenet:", typeof window.mobilenet);
        console.log("   window.cocoSsd:", typeof window.cocoSsd);
        console.log("   window.posenet:", typeof window.posenet);
        
        // Test basic TensorFlow functionality
        if (typeof tf !== 'undefined') {
            try {
                var testTensor = tf.tensor([1, 2, 3, 4]);
                console.log("✅ TensorFlow basic operations working");
                testTensor.dispose();
            } catch (error) {
                console.error("❌ TensorFlow basic operations failed:", error);
            }
        }
        
        // Test Teachable Machine functionality
        if (typeof tmImage !== 'undefined') {
            try {
                console.log("✅ Teachable Machine methods available:");
                console.log("   tmImage.load:", typeof tmImage.load);
                console.log("   tmImage.version:", tmImage.version || 'unknown');
            } catch (error) {
                console.error("❌ Teachable Machine test failed:", error);
            }
        }
        
        console.log("🔍 Library check complete!");
        
    }, 3000); // Wait 3 seconds for all libraries to load
    
})();
