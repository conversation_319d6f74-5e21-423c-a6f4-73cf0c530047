// TensorFlow.js Results Display Widget for Freeboard
(function () {
    freeboard.loadWidgetPlugin({
        type_name: "tensorflow_display",
        display_name: "TensorFlow.js Results",
        description: "Display AI prediction results from TensorFlow models",
        fill_size: false,
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text",
                default_value: "AI Predictions",
            },
            {
                name: "data_source",
                display_name: "Data Source",
                type: "calculated",
                description: 'Data source containing TensorFlow predictions (e.g., datasources["tensorflow_results"])',
            },
            {
                name: "display_mode",
                display_name: "Display Mode",
                type: "option",
                options: [
                    {
                        name: "List View",
                        value: "list",
                    },
                    {
                        name: "Chart View",
                        value: "chart",
                    },
                    {
                        name: "Table View",
                        value: "table",
                    },
                ],
                default_value: "list",
            },
            {
                name: "max_items",
                display_name: "Max Items to Show",
                type: "number",
                default_value: 5,
            },
            {
                name: "show_confidence",
                display_name: "Show Confidence Scores",
                type: "boolean",
                default_value: true,
            },
            {
                name: "show_timestamp",
                display_name: "Show Timestamp",
                type: "boolean",
                default_value: true,
            },
            {
                name: "auto_refresh",
                display_name: "Auto Refresh (seconds)",
                type: "number",
                default_value: 0,
                description: "0 = disabled",
            },
        ],

        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new tensorflowDisplayWidget(settings));
        },
    });

    var tensorflowDisplayWidget = function (settings) {
        var currentSettings = settings;
        var widgetElement;
        var currentData = null;
        var refreshTimer = null;

        function createWidgetHTML() {
            var html = '<div style="padding: 10px; font-family: Arial, sans-serif;">';

            if (currentSettings.title) {
                html +=
                    '<div style="font-weight: bold; font-size: 16px; margin-bottom: 10px; color: #333;">' +
                    currentSettings.title +
                    "</div>";
            }

            html += '<div id="content-area" style="min-height: 100px;"></div>';
            html += "</div>";

            return html;
        }

        function formatTimestamp(timestamp) {
            if (!timestamp) return "";
            var date = new Date(timestamp);
            return date.toLocaleTimeString();
        }

        function renderListView(data) {
            var html = "";

            if (currentSettings.show_timestamp && data.timestamp) {
                html +=
                    '<div style="font-size: 12px; color: #666; margin-bottom: 8px;">Last Update: ' +
                    formatTimestamp(data.timestamp) +
                    "</div>";
            }

            if (data.model_type) {
                html +=
                    '<div style="font-size: 14px; color: #444; margin-bottom: 8px;">Model: ' +
                    data.model_type.toUpperCase() +
                    "</div>";
            }

            if (data.data && Array.isArray(data.data)) {
                var maxItems = Math.min(data.data.length, currentSettings.max_items || 5);

                for (var i = 0; i < maxItems; i++) {
                    var item = data.data[i];
                    html += '<div style="margin-bottom: 5px; padding: 5px; background: #f5f5f5; border-radius: 3px;">';

                    if (data.model_type === "mobilenet" && item.className) {
                        html += '<span style="font-weight: bold;">' + item.className + "</span>";
                        if (currentSettings.show_confidence && item.probability) {
                            html += ' <span style="color: #666;">(' + (item.probability * 100).toFixed(1) + "%)</span>";
                        }
                    } else if (data.model_type === "cocossd" && item.class) {
                        html += '<span style="font-weight: bold;">' + item.class + "</span>";
                        if (currentSettings.show_confidence && item.score) {
                            html += ' <span style="color: #666;">(' + (item.score * 100).toFixed(1) + "%)</span>";
                        }
                        if (item.bbox) {
                            html +=
                                "<br><small>Position: [" +
                                item.bbox
                                    .map(function (v) {
                                        return v.toFixed(0);
                                    })
                                    .join(", ") +
                                "]</small>";
                        }
                    } else if (data.model_type === "custom") {
                        html +=
                            "<span>Output " +
                            i +
                            ": " +
                            (typeof item === "number" ? item.toFixed(4) : item) +
                            "</span>";
                    }

                    html += "</div>";
                }
            } else if (data.model_type === "posenet" && data.data) {
                html += '<div style="margin-bottom: 5px; padding: 5px; background: #f5f5f5; border-radius: 3px;">';
                html += '<span style="font-weight: bold;">Pose Detected</span>';
                if (currentSettings.show_confidence && data.data.pose && data.data.pose.score) {
                    html += ' <span style="color: #666;">(' + (data.data.pose.score * 100).toFixed(1) + "%)</span>";
                }
                if (data.data.visibleKeypoints) {
                    html += "<br><small>Visible Keypoints: " + data.data.visibleKeypoints + "/17</small>";
                }
                html += "</div>";
            }

            return html || '<div style="color: #999; font-style: italic;">No data available</div>';
        }

        function renderTableView(data) {
            var html = '<table style="width: 100%; border-collapse: collapse; font-size: 14px;">';

            if (currentSettings.show_timestamp && data.timestamp) {
                html +=
                    '<tr><td colspan="2" style="padding: 5px; border-bottom: 1px solid #ddd; font-size: 12px; color: #666;">Last Update: ' +
                    formatTimestamp(data.timestamp) +
                    "</td></tr>";
            }

            if (data.data && Array.isArray(data.data)) {
                html += '<tr style="background: #f0f0f0;"><th style="padding: 8px; border: 1px solid #ddd;">Item</th>';
                if (currentSettings.show_confidence) {
                    html += '<th style="padding: 8px; border: 1px solid #ddd;">Confidence</th>';
                }
                html += "</tr>";

                var maxItems = Math.min(data.data.length, currentSettings.max_items || 5);

                for (var i = 0; i < maxItems; i++) {
                    var item = data.data[i];
                    html += "<tr>";

                    if (data.model_type === "mobilenet" && item.className) {
                        html += '<td style="padding: 8px; border: 1px solid #ddd;">' + item.className + "</td>";
                        if (currentSettings.show_confidence) {
                            html +=
                                '<td style="padding: 8px; border: 1px solid #ddd;">' +
                                (item.probability * 100).toFixed(1) +
                                "%</td>";
                        }
                    } else if (data.model_type === "cocossd" && item.class) {
                        html += '<td style="padding: 8px; border: 1px solid #ddd;">' + item.class + "</td>";
                        if (currentSettings.show_confidence) {
                            html +=
                                '<td style="padding: 8px; border: 1px solid #ddd;">' +
                                (item.score * 100).toFixed(1) +
                                "%</td>";
                        }
                    } else if (data.model_type === "custom") {
                        html += '<td style="padding: 8px; border: 1px solid #ddd;">Output ' + i + "</td>";
                        if (currentSettings.show_confidence) {
                            html +=
                                '<td style="padding: 8px; border: 1px solid #ddd;">' +
                                (typeof item === "number" ? item.toFixed(4) : item) +
                                "</td>";
                        }
                    }

                    html += "</tr>";
                }
            }

            html += "</table>";
            return html;
        }

        function renderChartView(data) {
            var html = '<div style="position: relative;">';

            if (currentSettings.show_timestamp && data.timestamp) {
                html +=
                    '<div style="font-size: 12px; color: #666; margin-bottom: 8px;">Last Update: ' +
                    formatTimestamp(data.timestamp) +
                    "</div>";
            }

            if (data.data && Array.isArray(data.data)) {
                var maxItems = Math.min(data.data.length, currentSettings.max_items || 5);

                for (var i = 0; i < maxItems; i++) {
                    var item = data.data[i];
                    var confidence = 0;
                    var label = "";

                    if (data.model_type === "mobilenet" && item.className) {
                        label = item.className;
                        confidence = item.probability || 0;
                    } else if (data.model_type === "cocossd" && item.class) {
                        label = item.class;
                        confidence = item.score || 0;
                    } else if (data.model_type === "custom") {
                        label = "Output " + i;
                        confidence = Math.abs(typeof item === "number" ? item : 0);
                    }

                    var percentage = confidence * 100;
                    var barWidth = Math.min(percentage, 100);

                    html += '<div style="margin-bottom: 8px;">';
                    html += '<div style="font-size: 12px; margin-bottom: 2px;">' + label;
                    if (currentSettings.show_confidence) {
                        html += " (" + percentage.toFixed(1) + "%)";
                    }
                    html += "</div>";
                    html += '<div style="background: #e0e0e0; height: 20px; border-radius: 10px; overflow: hidden;">';
                    html +=
                        '<div style="background: linear-gradient(90deg, #4CAF50, #8BC34A); height: 100%; width: ' +
                        barWidth +
                        '%; transition: width 0.3s ease;"></div>';
                    html += "</div>";
                    html += "</div>";
                }
            }

            html += "</div>";
            return html;
        }

        function updateDisplay() {
            if (!widgetElement || !currentData) return;

            var contentArea = widgetElement.find("#content-area");
            var displayMode = currentSettings.display_mode || "list";
            var html = "";

            switch (displayMode) {
                case "chart":
                    html = renderChartView(currentData);
                    break;
                case "table":
                    html = renderTableView(currentData);
                    break;
                case "list":
                default:
                    html = renderListView(currentData);
                    break;
            }

            contentArea.html(html);
        }

        function startAutoRefresh() {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }

            var interval = currentSettings.auto_refresh || 0;
            if (interval > 0) {
                refreshTimer = setInterval(function () {
                    updateDisplay();
                }, interval * 1000);
            }
        }

        this.render = function (containerElement) {
            widgetElement = $(containerElement);
            widgetElement.html(createWidgetHTML());
            updateDisplay();
            startAutoRefresh();
        };

        this.getHeight = function () {
            return 3; // Default height in grid units
        };

        this.onSettingsChanged = function (newSettings) {
            currentSettings = newSettings;
            widgetElement.html(createWidgetHTML());
            updateDisplay();
            startAutoRefresh();
        };

        this.onCalculatedValueChanged = function (settingName, newValue) {
            if (settingName === "data_source") {
                currentData = newValue;
                updateDisplay();
            }
        };

        this.onDispose = function () {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }
        };
    };
})();
