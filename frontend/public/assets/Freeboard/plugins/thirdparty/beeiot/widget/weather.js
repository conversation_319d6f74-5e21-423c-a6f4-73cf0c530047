(function () {
    //
    // DECLARATIONS
    //
    var LOADING_INDICATOR_DELAY = 1000;

    //

    freeboard.loadWidgetPlugin({
        type_name: "beeWeather",
        display_name: "Bee Weather",
        description: "<PERSON>em thời tiết (https://weatherwidget.io/)",
        external_scripts: [
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/lib/weather.js"
        ],
        settings: [
            {
                name: "title",
                display_name: "Device Name",
                type: "text"
            },
            {
                name: "location",
                display_name: "Weather URL",
                type: "calculated",
            },
            {
                name: "refresh",
                display_name: "Refresh every",
                type: "number",
                suffix: "seconds",
                description: "Leave blank if the image doesn't need to be refreshed"
            }
        ],
        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new beeButtonWidget(settings));
        }
    });

    var beeButtonWidget = function (settings) {
        var currentSettings = settings;
        var self = this;
        var titleElement = $('<h2 class="section-title" style="text-align: center; color: black;"></h2>');
        var weatherElement = $('<div style="height: 100%; width: 100%;"></div>');
        var timer;

        function updateWeather(newValue) {
            weatherElement.html(newValue);
        }

        function stopTimer() {
            if (timer) {
                clearInterval(timer);
                timer = null;
            }
        }

        this.render = function (element) {
            $(element).append(titleElement).append(weatherElement);
        }

        this.onSettingsChanged = function (newSettings) {
            currentSettings = newSettings;
            titleElement.html((_.isUndefined(newSettings.title) ? "" : newSettings.title));

            stopTimer();
            if (newSettings.refresh && newSettings.refresh > 0) {
                timer = setInterval(updateWeather, Number(newSettings.refresh) * 1000);
            }
        }

        this.onCalculatedValueChanged = function (settingName, newValue) {
            if (settingName == "location" && newValue != null) {
                weatherElement.html(newValue);
            }
        }

        this.onDispose = function () {
            stopTimer();
        }

        this.getHeight = function () {
            return 5;
        }

        this.onSettingsChanged(settings);
    };

}());
