# TensorFlow.js Widgets for Freeboard

Bộ widget TensorFlow.js tích hợp AI/ML vào dashboard IoT Freeboard của bạn.

## 🚀 Tính năng chính

### 1. **TensorFlow Camera Widget**

-   **Mô tả**: Hiển thị camera với AI predictions thời gian thực
-   **Hỗ trợ models**: MobileNet, COCO-SSD, PoseNet, Custom Models
-   **Tính năng**:
    -   Chọn camera trước/sau
    -   Điều chỉnh confidence threshold
    -   Xuất dữ liệu sang data source khác
    -   Hiển thị predictions trực tiếp trên video

### 2. **TensorFlow Display Widget**

-   **M<PERSON> tả**: Hiển thị kết quả AI predictions từ các widget khác
-   **Chế độ hiển thị**: List, Chart, Table
-   **Tính năng**:
    -   Auto-refresh
    -   Hiển thị confidence scores
    -   Timestamp tracking
    -   Responsive design

### 3. **TensorFlow Processor Widget**

-   **<PERSON><PERSON> tả**: Xử lý dữ liệu sensor bằng TensorFlow models
-   **Preprocessing**: Normalize, Standardize, Min-Max scaling
-   **Tính năng**:
    -   Custom input shapes
    -   Real-time processing
    -   Output labeling
    -   Data pipeline integration

### 4. **Teachable Machine Widget**

-   **Mô tả**: Widget chuyên dụng cho Google Teachable Machine models
-   **Hỗ trợ**: Image Classification models từ teachablemachine.withgoogle.com
-   **Tính năng**:
    -   Tự động load metadata và labels
    -   Giao diện tối ưu cho classification
    -   Confidence threshold filtering
    -   Real-time predictions với visual feedback

## 📦 Cài đặt

### Cách 1: Load từng widget riêng lẻ

```html
<!-- Trong Freeboard settings -->
<script src="/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-camera.js"></script>
<script src="/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-display.js"></script>
<script src="/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-processor.js"></script>
```

### Cách 2: Load bundle (Khuyến nghị)

```html
<script src="/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-widgets.js"></script>
```

## 🎯 Hướng dẫn sử dụng

### Camera Widget Setup

1. **Thêm widget**: Chọn "TensorFlow.js Camera" từ widget list
2. **Cấu hình cơ bản**:
    - Title: Tên hiển thị
    - Camera Facing: Front/Back camera
    - Model Type: Chọn loại AI model
3. **Cấu hình nâng cao**:
    - Prediction Interval: Tần suất dự đoán (ms)
    - Confidence Threshold: Ngưỡng tin cậy (0.0-1.0)
    - Output Data Source: Tên data source để xuất dữ liệu

### Display Widget Setup

1. **Thêm widget**: Chọn "TensorFlow.js Results"
2. **Kết nối data source**: Chọn data source từ Camera widget
3. **Chọn display mode**:
    - **List**: Hiển thị danh sách đơn giản
    - **Chart**: Biểu đồ thanh với confidence
    - **Table**: Bảng có cấu trúc

### Processor Widget Setup

1. **Thêm widget**: Chọn "TensorFlow.js Data Processor"
2. **Cấu hình model**:
    - Model URL: Link đến TensorFlow.js model
    - Input Shape: Định dạng input (JSON array)
3. **Cấu hình preprocessing**:
    - None: Không xử lý
    - Normalize: Chuẩn hóa 0-1
    - Standardize: Z-score normalization
    - Min-Max: Scale về [-1, 1]

## 🔗 Data Flow Examples

### Example 1: Camera → Display

```
[Camera Widget] → [Data Source: "ai_predictions"] → [Display Widget]
```

### Example 2: Sensor → Processor → Display

```
[Sensor Data] → [Processor Widget] → [Data Source: "processed_data"] → [Display Widget]
```

### Example 3: Complete AI Pipeline

```
[Camera] → [Data: "camera_ai"] → [Display 1]
    ↓
[Processor] → [Data: "processed_ai"] → [Display 2]
```

## 📊 Data Formats

### Camera Widget Output

```json
{
    "timestamp": "2024-01-01T12:00:00.000Z",
    "model_type": "mobilenet",
    "data": [
        {
            "className": "Egyptian cat",
            "probability": 0.8234
        },
        {
            "className": "tabby, tabby cat",
            "probability": 0.1234
        }
    ]
}
```

### Processor Widget Output

```json
{
    "timestamp": "2024-01-01T12:00:00.000Z",
    "input": [1.2, 3.4, 5.6, 7.8],
    "output": [0.8234, 0.1766],
    "model_url": "https://example.com/model.json",
    "preprocessing": "normalize"
}
```

## 🎨 Customization

### CSS Styling

Widgets sử dụng inline styles nhưng có thể override:

```css
/* Camera widget overlay */
.tensorflow-camera .predictions {
    background: rgba(0, 0, 0, 0.9) !important;
    color: #00ff00 !important;
}

/* Display widget charts */
.tensorflow-display .chart-bar {
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4) !important;
}
```

### Custom Models

#### Supported Formats

-   TensorFlow.js LayersModel (.json + .bin)
-   TensorFlow SavedModel (converted)
-   Custom architectures

#### Model Requirements

-   Input: Image tensor [batch, height, width, channels]
-   Output: Predictions array or classification probabilities

## 🔧 Troubleshooting

### Common Issues

1. **Camera không hoạt động**

    - Kiểm tra HTTPS (required for camera access)
    - Cho phép camera permission
    - Thử đổi camera facing mode

2. **Model loading failed**

    - Kiểm tra model URL accessibility
    - Verify CORS headers
    - Check model format compatibility

3. **No predictions showing**
    - Kiểm tra confidence threshold
    - Verify model input format
    - Check browser console for errors

### Performance Tips

1. **Giảm prediction interval** cho real-time performance
2. **Sử dụng smaller models** (MobileNet vs ResNet)
3. **Optimize preprocessing** pipeline
4. **Limit max predictions** displayed

## 🌟 Advanced Features

### Custom Model Integration

```javascript
// Example custom model setup
{
  "model_type": "custom",
  "model_url": "https://your-domain.com/model.json",
  "input_shape": "[1, 224, 224, 3]",
  "preprocessing": "normalize"
}
```

### Multi-Model Pipeline

```javascript
// Chain multiple models
Camera → Object Detection → Classification → Action
```

### Real-time Analytics

```javascript
// Combine with time-series data
Sensor Data + AI Predictions → Trend Analysis
```

## 📈 Performance Metrics

-   **Model Loading**: 2-10 seconds (depending on model size)
-   **Inference Speed**: 50-200ms per prediction
-   **Memory Usage**: 100-500MB (varies by model)
-   **Browser Support**: Chrome 60+, Firefox 55+, Safari 11+

## 🤝 Contributing

Để đóng góp vào dự án:

1. Fork repository
2. Tạo feature branch
3. Implement changes
4. Test thoroughly
5. Submit pull request

## 📄 License

Copyright 2025 BeE STEM Solutions. All rights reserved.

---

**Phát triển bởi**: BeE STEM Solutions
**Version**: 1.0.0  
**Last Updated**: 2025-07-17
