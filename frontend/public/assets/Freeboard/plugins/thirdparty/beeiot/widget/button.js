// ┌────────────────────────────────────────────────────────────────────┐ \\
// │ freeboard-actuator-plugin                                          │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ http://blog.onlinux.fr/?tag=freeboard                              │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ Licensed under the MIT license.                                    │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ Freeboard widget plugin.                                           │ \\
// └────────────────────────────────────────────────────────────────────┘ \\
(function () {
    //
    // DECLARATIONS
    //
    var LOADING_INDICATOR_DELAY = 1000;

    //

    freeboard.loadWidgetPlugin({
        type_name: "beeButton",
        display_name: "Bee Button",
        description: "Xem trạng thái và điều khiển thiết bị",
        settings: [
            {
                name: "title",
                display_name: "Device Name",
                type: "text"
            },
            {
                name: "icon_switch",
                display_name: "Icon",
                type: "option",
                default: "fas fa-lightbulb",
				options: widget_icon
            },
            {
                name: "topic",
                display_name: "Topic",
                type: "calculated",
            },
            {
                name: "value",
                display_name: "Value",
                type: "calculated"
            },
            {
                name: "on_text",
                display_name: "Payload On",
                type: "text"
            },
            {
                name: "off_text",
                display_name: "Payload Off",
                type: "text"
            },
            {
                name: "show_value",
                display_name: "Show value",
                type: "option",
                default: false,
				options: [
					{
						value: "hide",
						name: "Hide",
					},
					{
						value: "show",
						name: "Show",
					}
				]
            }
        ],
        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new beeButtonWidget(settings));
        }
    });

    freeboard.addStyle('.indicator-light.interactive:hover', "box-shadow: 0px 0px 15px #FF9900; cursor: pointer;");
    freeboard.addStyle('.button-widget-wrapper', "width: 100%; height: 130px; text-align: center;");

    var beeButtonWidget = function (settings) {
        var currentSettings = settings;
        var self = this;
        var titleElement = $('<h2 class="section-title" style="text-align: center; color: black;"></h2>');
        var stateElement = $('<div class="indicator-text" style="text-align: center; transform: translateY(-5px);"></div>');
        var iconElement = $('<i class="' + settings.icon_switch + ' fa-7x" style="margin-top: 10px;"></i>')
        var isOn = false;
        var onText;
        var offText;
        var topic;

        function updateState() {
            if (isOn) {
                stateElement.text((_.isUndefined(onText) ? (_.isUndefined(currentSettings.on_text) ? "" : currentSettings.on_text) : onText));
                iconElement.attr('style', 'margin-top: 20px; color: #F1C40F;')
            }
            else {
                stateElement.text((_.isUndefined(offText) ? (_.isUndefined(currentSettings.off_text) ? "" : currentSettings.off_text) : offText));
                iconElement.attr('style', 'margin-top: 20px;')
            }
            if (currentSettings.show_value == "show") stateElement.show();
            else stateElement.hide();
        }

        this.onClick = function(e) {
            e.preventDefault()

            var new_val = !isOn;
            payload = (new_val) ? currentSettings.on_text : currentSettings.off_text;
            this.onCalculatedValueChanged('value', payload);
            mqttClient.send(topic, payload);
        }

        this.render = function (element) {
            $(element).append($('<div class="button-widget-wrapper"></div>').append(iconElement)).append(stateElement).append(titleElement);//.append(indicatorElement)
            $(iconElement).click(this.onClick.bind(this));
        }

        this.onSettingsChanged = function (newSettings) {
            currentSettings = newSettings;
            titleElement.html((_.isUndefined(newSettings.title) ? "" : newSettings.title));
            iconElement.removeAttr('class').addClass(newSettings.icon_switch).addClass('fa-7x')
            updateState();
        }

        this.onCalculatedValueChanged = function (settingName, newValue) {
            if (settingName == "value" && newValue != null) {
                if (newValue == currentSettings.on_text) {
                    isOn = true;
                }
                else {
                    isOn = false;
                }
            }
            if (settingName == "topic") topic = newValue;
            updateState();
        }

        this.onDispose = function () {
        }

        this.getHeight = function () {
            return 3;
        }

        this.onSettingsChanged(settings);
    };

}());
