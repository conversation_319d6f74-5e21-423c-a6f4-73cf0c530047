{"version": 1, "allow_edit": true, "plugins": [], "panes": [{"title": "Teachable Machine AI Demo", "width": 1, "row": {"3": 1, "4": 1}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "teachable_machine", "settings": {"title": "BeE Board vs Cuộn chì Detection", "model_url": "https://teachablemachine.withgoogle.com/models/86y3FI4B8/", "camera_facing": "environment", "prediction_interval": 1500, "confidence_threshold": 0.7, "show_predictions": true, "output_datasource": "teachable_machine_results"}}]}, {"title": "AI Results Display", "width": 1, "row": {"3": 1, "4": 1}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "tensorflow_display", "settings": {"title": "Detection Results", "data_source": "datasources[\"teachable_machine_results\"]", "display_mode": "chart", "max_items": 2, "show_confidence": true, "show_timestamp": true, "auto_refresh": 0}}]}, {"title": "Top Prediction", "width": 1, "row": {"3": 2, "4": 2}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "text_widget", "settings": {"title": "Current Detection", "size": "big", "value": "datasources[\"teachable_machine_results\"][\"top_prediction\"][\"className\"]", "animate": true}}]}, {"title": "Confidence Score", "width": 1, "row": {"3": 2, "4": 2}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "gauge", "settings": {"title": "Confidence Level", "value": "datasources[\"teachable_machine_results\"][\"top_prediction\"][\"probability\"] * 100", "units": "%", "min_value": 0, "max_value": 100}}]}, {"title": "Detection History", "width": 1, "row": {"3": 3, "4": 3}, "col": {"3": 1, "4": 1}, "col_width": 2, "widgets": [{"type": "sparkline", "settings": {"title": "BeE Board Detection Confidence", "value": "datasources[\"teachable_machine_results\"][\"all_predictions\"][1][\"probability\"] * 100", "include_legend": false, "legend": "BeE Board %"}}]}], "datasources": [{"name": "teachable_machine_results", "type": "JSON", "settings": {"url": "", "use_thingproxy": false, "refresh": 0, "method": "GET"}}], "columns": 3}