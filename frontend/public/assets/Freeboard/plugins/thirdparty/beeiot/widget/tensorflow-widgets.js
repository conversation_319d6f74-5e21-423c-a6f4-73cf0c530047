// TensorFlow.js Widgets Bundle for Freeboard
// This file loads all TensorFlow.js related widgets

(function() {
    // Widget loading configuration
    var tensorflowWidgets = [
        {
            name: "TensorFlow Camera",
            file: "tensorflow-camera.js",
            description: "Real-time AI camera with multiple model support"
        },
        {
            name: "TensorFlow Display",
            file: "tensorflow-display.js", 
            description: "Display AI prediction results in various formats"
        },
        {
            name: "TensorFlow Processor",
            file: "tensorflow-processor.js",
            description: "Process sensor data using TensorFlow models"
        }
    ];

    // Load widgets dynamically
    function loadTensorFlowWidgets() {
        var basePath = "/assets/Freeboard/plugins/thirdparty/beeiot/widget/";
        
        tensorflowWidgets.forEach(function(widget) {
            var script = document.createElement('script');
            script.src = basePath + widget.file;
            script.async = false; // Ensure sequential loading
            script.onload = function() {
                console.log("✅ Loaded TensorFlow Widget:", widget.name);
            };
            script.onerror = function() {
                console.error("❌ Failed to load TensorFlow Widget:", widget.name);
            };
            document.head.appendChild(script);
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadTensorFlowWidgets);
    } else {
        loadTensorFlowWidgets();
    }

    // Export widget information for debugging
    window.TensorFlowWidgets = {
        widgets: tensorflowWidgets,
        version: "1.0.0",
        description: "TensorFlow.js integration for Freeboard IoT dashboard"
    };

    console.log("🧠 TensorFlow.js Widgets Bundle initialized");
})();
