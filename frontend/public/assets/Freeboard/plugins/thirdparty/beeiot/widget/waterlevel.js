(function () {
    var waterlevelID = 0;
    freeboard.loadWidgetPlugin({
        type_name: "beeWaterLevel",
        display_name: "Bee Water Level",
        "external_scripts": [
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/lib/waterlevel.js"
        ],
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text"
            },
            {
                name: "value",
                display_name: "Value",
                type: "calculated",
            }
        ],
        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new beeGaugeWidget(settings));
        }
    });

    freeboard.addStyle('.bee-waterlevel-widget-wrapper', "width: 100%; text-align: center;");
    freeboard.addStyle('.bee-waterlevel-widget', "width: 200px; height: 160px; display: inline-block;");

    var beeGaugeWidget = function (settings) {
        var self = this;

        var thisGaugeID = "waterlevel-" + waterlevelID++;
        var titleElement = $('<h2 class="section-title"></h2>');
        var gaugeElement = $('<div class="waterlevel-widget" id="' + thisGaugeID + '" style="text-align: center;"></div>');

        var waterObject;
        var rendered = false;

        var currentSettings = settings;

        function createGauge() {
            if (!rendered) {
                return;
            }

            gaugeElement.empty();

            waterObject = new FluidMeter();
            waterObject.init({
                targetContainer: document.getElementById(thisGaugeID),
                fillPercentage: 0, // 15%
                options: {
                    fontSize: "30px",
                    fontFamily: "HelveticaNeue-UltraLight",
                    fontFillStyle: "white",
                    drawShadow: true,
                    drawText: true,
                    drawPercentageSign: true,
                    drawBubbles: false,
                    size: 160,
                    borderWidth: 2,
                    backgroundColor: "#e2e2e2",
                    foregroundColor: "#fafafa",
                    foregroundFluidLayer: {
                        fillStyle: "#0000CD",
                        angularSpeed: 100,
                        maxAmplitude: 9,
                        frequency: 30,
                        horizontalSpeed: -150
                    },
                    backgroundFluidLayer: {
                        fillStyle: "pink",
                        angularSpeed: 100,
                        maxAmplitude: 9,
                        frequency: 30,
                        horizontalSpeed: 150
                    }
                }
            });
        }

        this.render = function (element) {
            rendered = true;
            $(element).append(titleElement).append($('<div class="waterlevel-widget-wrapper"></div>').append(gaugeElement));
            createGauge();
        }

        this.onSettingsChanged = function (newSettings) {
            if (newSettings.min_value != currentSettings.min_value || newSettings.max_value != currentSettings.max_value || newSettings.units != currentSettings.units) {
                currentSettings = newSettings;
                createGauge();
            }
            else {
                currentSettings = newSettings;
            }
            titleElement.html(newSettings.title);
        }

        this.onCalculatedValueChanged = function (settingName, newValue) {
            if (!_.isUndefined(waterObject)) {
                if (settingName == "value" && newValue != null) {
                    waterObject.setPercentage(Number(newValue));
                }
            }
        }

        this.onDispose = function () {
        }

        this.getHeight = function () {
            return 3;
        }

        this.onSettingsChanged(settings);
    };

}());