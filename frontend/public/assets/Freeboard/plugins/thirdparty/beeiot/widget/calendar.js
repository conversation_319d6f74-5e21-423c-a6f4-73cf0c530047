(function(){

    calendarID = 0;

    freeboard.loadWidgetPlugin({
        "type_name": "beeCalendar",
        "display_name": "Bee Calendar",
        "external_scripts": [
            "https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js",
        ],
        "fill_size": true,
        "settings": [
            {
                "name": "height",
                "display_name": "Height Blocks",
                "type": "number",
                "default_value": 4,
                "description": "A height block is around 60 pixels"
            }
        ],
        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new htmlWidget(settings));
        }
    });

    freeboard.addStyle('.calendar-widget', "white-space: normal; width: 100%; height:100%");

    var htmlWidget = function (settings) {
        var self = this;
        var thisCalendarID = 'calendar-' + calendarID;
        var calendarElement = $('<div class="calendar-widget" id="' + thisCalendarID + '"></div>');
        var currentSettings = settings;

        this.render = function (element) {
            $(element).append(calendarElement);

            document.addEventListener('DOMContentLoaded', function() {

                var calendarEl = document.getElementById(thisCalendarID);
                var calendar = new FullCalendar.Calendar(calendarEl, {
                    initialView: 'dayGridMonth'
                });

                calendar.render();
            });
        }

        this.onSettingsChanged = function (newSettings) {
            currentSettings = newSettings;
        }

        this.onCalculatedValueChanged = function (settingName, newValue) {
//            if (settingName == "html") {
//                htmlElement.html(newValue);
//            }
        }

        this.onDispose = function () {
        }

        this.getHeight = function () {
            return Number(currentSettings.height);
        }

        this.onSettingsChanged(settings);
    };


}());