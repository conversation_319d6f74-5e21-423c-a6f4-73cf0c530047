// ┌────────────────────────────────────────────────────────────────────┐ \\
// │ freeboard-slider-plugin                                            │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ http://blog.onlinux.fr/?tag=freeboard                              │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ Licensed under the MIT license.                                    │ \\
// ├────────────────────────────────────────────────────────────────────┤ \\
// │ Freeboard widget plugin.                                           │ \\
// └────────────────────────────────────────────────────────────────────┘ \\
(function()
{
    //
    // DECLARATIONS
    //
    var LOADING_INDICATOR_DELAY = 1000;
    var SLIDER_ID = 0;
        
    freeboard.addStyle ('.slider',"background-color: #858796; border-radius: 8px !important; margin: 10px;");
    freeboard.addStyle ('.slider-label','margin-left: 10px; margin-top: 10px; text-transform: capitalize;');
    freeboard.addStyle ('.myui-slider-handle', "width: 1.5em !important; height: 1.5em !important; border-radius: 50%; top: -.4em !important; margin-left:-1.0em !important;");
    freeboard.addStyle ('.ui-slider-range', 'background: #F90;');
    freeboard.addStyle ('.icon-title-slider', "font-size: 20px; margin-top: 8px; margin-right: 15px; display: inline-block;")
	// ## A Widget Plugin
	//
	// -------------------
	// ### Widget Definition
	//
	// -------------------
	// **freeboard.loadWidgetPlugin(definition)** tells freeboard that we are giving it a widget plugin. It expects an object with the following:
	freeboard.loadWidgetPlugin({
		// Same stuff here as with datasource plugin.
		"type_name"   : "beeSlider",
		"display_name": "Bee Linear Slider",
                "description" : "Interactive Slider Plugin",
		// **external_scripts** : Any external scripts that should be loaded before the plugin instance is created.
		"external_scripts": [
            "https://code.jquery.com/ui/1.12.0/jquery-ui.js",
            "https://code.jquery.com/ui/1.12.0/themes/base/jquery-ui.css"
		],
		// **fill_size** : If this is set to true, the widget will fill be allowed to fill the entire space given it, otherwise it will contain an automatic padding of around 10 pixels around it.
		"fill_size" : true,
		"settings"    : [
			{
				name            : "title",
				display_name    : "Device Name",
				type            : "text"
			},
			{
                name            : "icon_switch",
                display_name    : "Icon",
                type            : "option",
                default         : "fas fa-lightbulb",
				options         : widget_icon
            },
			{
				name            : "topic",
				display_name    : "Topic",
				type            : "calculated"
			},
			{
                name            : "value",
                display_name    : "Value",
                type            : "calculated"
            },
			{
				name            : "min",
				display_name    : "MINIMUM",
				type            : "calculated",
				default_value   : "0"
			},
			{
				name            : "max",
				display_name    : "MAXIMUM",
				type            : "calculated",
				default_value   : "100"
			}
		],
		// Same as with datasource plugin, but there is no updateCallback parameter in this case.
		newInstance   : function(settings, newInstanceCallback)
		{
			newInstanceCallback(new beeSlider(settings));
		}
	});


	// ### Widget Implementation
	//
	// -------------------
	// Here we implement the actual widget plugin. We pass in the settings;
	var beeSlider = function(settings)
	{
		var self = this;
		var currentSettings = settings;

        var thisWidgetId = "slider-" + SLIDER_ID++;
        var thisWidgetContainer = $('<div class="slider-widget slider-label" id="__' + thisWidgetId + '"></div>');

        var titleElement = $('<h2 class="section-title slider-label"></h2>');
        var valueElement = $('<div id="value-' + thisWidgetId + '" style="display: inline-block; margin-top: 7px; font-weight:bold; color: black;" ></div>');
        var sliderRowElement = $('<div style="display: flex"></div>');
        var sliderElement = $('<div class="slider" id="' + thisWidgetId + '" style="width: 200px;"></div>');
        var iconElement = $('<i class="' + settings.icon_switch + '"></i>').addClass('icon-title-slider');
        var theSlider = '#' + thisWidgetId;

        titleElement.html( (_.isUndefined(currentSettings.title) ? "": currentSettings.title) );
        var min = (_.isUndefined(currentSettings.min) ? 0: currentSettings.min);
        var max = (_.isUndefined(currentSettings.max) ? 100: currentSettings.max);
        var value = min; // = (_.isUndefined(currentSettings.value) ? 50: currentSettings.value);
        var topic;
                
		// Here we create an element to hold the text we're going to display. We're going to set the value displayed in it below.
		// **render(containerElement)** (required) : A public function we must implement that will be called when freeboard wants us to render the contents of our widget. The container element is the DIV that will surround the widget.
		self.render = function(containerElement)
		{
            $(containerElement).append(thisWidgetContainer);
            titleElement.appendTo(thisWidgetContainer);
//            $(titleElement).append(valueElement);
            sliderRowElement.append(iconElement);
            sliderRowElement.append(sliderElement);
            sliderRowElement.append(valueElement);
            sliderRowElement.appendTo(thisWidgetContainer);

            $(theSlider).slider({
                classes: {
                    "ui-slider-range": "ui-corner-all",
                    "ui-slider-handle" : "myui-slider-handle"
                },
                value: value,
                min: min,
                max: max,
                orientation: "horizontal",
                range: "min",
                animate: "slow",
                slide: function(event, ui) {
                    $("#value-" + thisWidgetId).html(ui.value);
                },
                stop: function(event, ui) {
                    mqttClient.send(topic, ui.value.toString());
                }
            }).removeClass("ui-widget-content");

            $(valueElement).html(value);
            $(theSlider).slider("value", value);

		}
        // **getHeight()** (required) : A public function we must implement that will be called when freeboard wants to know how big we expect to be when we render, and returns a height. This function will be called any time a user updates their settings (including the first time they create the widget).
		//
		// Note here that the height is not in pixels, but in blocks. A block in freeboard is currently defined as a rectangle that is fixed at 300 pixels wide and around 45 pixels multiplied by the value you return here.
		//
		// Blocks of different sizes may be supported in the future.
		self.getHeight = function()
		{
			if(currentSettings.size == "big")
			{
				return 2;
			}
			else
			{
				return 1;
			}
		}

		// **onSettingsChanged(newSettings)** (required) : A public function we must implement that will be called when a user makes a change to the settings.
		self.onSettingsChanged = function(newSettings)
		{
			// Normally we'd update our text element with the value we defined in the user settings above (the_text), but there is a special case for settings that are of type **"calculated"** -- see below.
			currentSettings = newSettings;
            titleElement.html( (_.isUndefined(newSettings.title) ? "": newSettings.title) );
            iconElement.removeAttr('class').addClass(newSettings.icon_switch).addClass('icon-title-slider');
		}

		// **onCalculatedValueChanged(settingName, newValue)** (required) : A public function we must implement that will be called when a calculated value changes. Since calculated values can change at any time (like when a datasource is updated) we handle them in a special callback function here.
		self.onCalculatedValueChanged = function(settingName, newValue)
		{
            if (settingName == "topic") topic = newValue;

            if (settingName == "value" && newValue != null) {
                $(valueElement).html(newValue);
                $(theSlider).slider("value", newValue);
            }

			// Remember we defined "the_text" up above in our settings.
			if (settingName == "max")
			{
                if (newValue > min ) {
                    max = newValue;
                    $( theSlider ).slider( "option", "max", newValue);
                } else {
                    currentSettings.max = max; // Keep it unchanged
                    freeboard.showDialog($("<div align='center'> Max value cannot be lower than Min value!</div>"),"Warning!","OK",null,function(){});
                }
			}
			if (settingName == "min")
			{
                if (newValue < max ) {
                    min = newValue;
                    $(theSlider).slider("option","min", newValue);
                } else {
                    currentSettings.min= min;// Keep it unchanged
                    freeboard.showDialog($("<div align='center'> Min value cannot be greater than Max value!</div>"),"Warning!","OK",null,function(){});
                }
			}
		}

		// **onDispose()** (required) : Same as with datasource plugins.
		self.onDispose = function()
		{
		}
	}
}());
