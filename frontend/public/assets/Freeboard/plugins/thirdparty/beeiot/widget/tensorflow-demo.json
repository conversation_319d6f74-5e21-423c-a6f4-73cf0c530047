{"version": 1, "allow_edit": true, "plugins": ["/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-widgets.js"], "panes": [{"title": "AI Camera Dashboard", "width": 1, "row": {"3": 1, "4": 1}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "tensorflow_camera", "settings": {"title": "AI Camera Feed", "camera_facing": "environment", "model_type": "mobilenet", "prediction_interval": 2000, "show_predictions": true, "max_predictions": 3, "confidence_threshold": 0.3, "output_datasource": "camera_predictions"}}]}, {"title": "AI Results", "width": 1, "row": {"3": 1, "4": 1}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "tensorflow_display", "settings": {"title": "Image Classification Results", "data_source": "datasources[\"camera_predictions\"]", "display_mode": "chart", "max_items": 5, "show_confidence": true, "show_timestamp": true, "auto_refresh": 0}}]}, {"title": "Object Detection", "width": 1, "row": {"3": 2, "4": 2}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "tensorflow_camera", "settings": {"title": "Object Detection Camera", "camera_facing": "environment", "model_type": "cocossd", "prediction_interval": 1500, "show_predictions": true, "max_predictions": 5, "confidence_threshold": 0.5, "output_datasource": "object_detection"}}]}, {"title": "Detection Results", "width": 1, "row": {"3": 2, "4": 2}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "tensorflow_display", "settings": {"title": "Detected Objects", "data_source": "datasources[\"object_detection\"]", "display_mode": "list", "max_items": 8, "show_confidence": true, "show_timestamp": true, "auto_refresh": 0}}]}, {"title": "Sensor Data Processing", "width": 1, "row": {"3": 3, "4": 3}, "col": {"3": 1, "4": 1}, "col_width": 1, "widgets": [{"type": "tensorflow_processor", "settings": {"title": "Sensor AI Processor", "input_data": "datasources[\"sensor_data\"]", "model_url": "https://example.com/sensor-model.json", "input_shape": "[1, 10]", "preprocessing": "normalize", "output_datasource": "ai_processed_data", "processing_interval": 5000, "show_raw_output": true, "output_labels": "Normal,Warning,Critical"}}]}, {"title": "AI Analysis Results", "width": 1, "row": {"3": 3, "4": 3}, "col": {"3": 2, "4": 2}, "col_width": 1, "widgets": [{"type": "tensorflow_display", "settings": {"title": "AI Analysis", "data_source": "datasources[\"ai_processed_data\"]", "display_mode": "table", "max_items": 3, "show_confidence": true, "show_timestamp": true, "auto_refresh": 5}}]}], "datasources": [{"name": "camera_predictions", "type": "JSON", "settings": {"url": "", "use_thingproxy": false, "refresh": 0, "method": "GET"}}, {"name": "object_detection", "type": "JSON", "settings": {"url": "", "use_thingproxy": false, "refresh": 0, "method": "GET"}}, {"name": "sensor_data", "type": "JSON", "settings": {"url": "https://api.example.com/sensor-data", "use_thingproxy": false, "refresh": 10, "method": "GET", "headers": {"Content-Type": "application/json"}}}, {"name": "ai_processed_data", "type": "JSON", "settings": {"url": "", "use_thingproxy": false, "refresh": 0, "method": "GET"}}], "columns": 3}