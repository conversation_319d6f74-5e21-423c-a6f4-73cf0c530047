(function()
{
    //
    // DECLARATIONS
    //
    var ROUND_SLIDER_ID = 0;

    freeboard.addStyle('.round-slider-container', 'justify-content: center; width: 100%; height: 100%;');

	// ## A Widget Plugin
	//
	// -------------------
	// ### Widget Definition
	//
	// -------------------
	// **freeboard.loadWidgetPlugin(definition)** tells freeboard that we are giving it a widget plugin. It expects an object with the following:
	freeboard.loadWidgetPlugin({
		// Same stuff here as with datasource plugin.
		"type_name"   : "beeRoundSlider",
		"display_name": "Bee Round Slider",
		"description" : "Interactive Round Slider",
		// **external_scripts** : Any external scripts that should be loaded before the plugin instance is created.
		"external_scripts": [
//            "https://code.jquery.com/jquery-3.2.1.js",
            "https://cdn.jsdelivr.net/npm/round-slider@1.6.1/dist/roundslider.min.css",
            "https://cdn.jsdelivr.net/npm/round-slider@1.6.1/dist/roundslider.min.js"
		],
		// **fill_size** : If this is set to true, the widget will fill be allowed to fill the entire space given it, otherwise it will contain an automatic padding of around 10 pixels around it.
		"fill_size" : true,
		"settings"    : [
			{
				name            : "title",
				display_name    : "Device Name",
				type            : "text"
			},
			{
                name: "icon_switch",
                display_name: "Icon",
                type: "option",
                default: "fas fa-lightbulb",
				options: widget_icon
            },
			{
				name            : "topic",
				display_name    : "Topic",
				type            : "calculated"
			},
			{
                name            : "value",
                display_name    : "Value",
                type            : "calculated"
            },
			{
				name            : "min",
				display_name    : "MINIMUM",
				type            : "calculated",
				default_value   : "0"
			},
			{
				name            : "max",
				display_name    : "MAXIMUM",
				type            : "calculated",
				default_value   : "100"
			}
		],
		// Same as with datasource plugin, but there is no updateCallback parameter in this case.
		newInstance: function(settings, newInstanceCallback)
		{
			newInstanceCallback(new beeRoundSlider(settings));
		}
	});

	// ### Widget Implementation
	//
	// -------------------
	// Here we implement the actual widget plugin. We pass in the settings;
	var beeRoundSlider = function(settings)
	{
		var self = this;
		var currentSettings = settings;

        var thisWidgetId = "round-slider-" + ROUND_SLIDER_ID++;
        var thisWidgetContainer = $('<div class="round-slider-container" style="text-align: center;" id="__' + thisWidgetId + '"></div>');

        var titleElement = $('<h2 class="" style="z-index: 1; position: relative; top: 65px; color: black;"></h2>');
//        var valueElement = $('<div id="round-value-' + thisWidgetId + '" style="display:inline-block; padding-left: 10px; font-weight:bold; color: #d3d4d4" ></div>');
        var sliderElement = $('<div id="' + thisWidgetId + '" style="margin: 25px auto; transform: translateY(-100px);"></div>');
        var iconElement = $('<i class="' + settings.icon_switch + ' fa-5x" style="position: relative; z-index: 1; top: 65px;"></i>')
        var theSlider = '#' + thisWidgetId;

        freeboard.addStyle(theSlider + ' .rs-range-color', 'background-color: #3b71ca;');
        freeboard.addStyle(theSlider + ' .rs-path-color', 'background-color: #C2E9F7;');
        freeboard.addStyle(theSlider + ' .rs-handle', 'background-color: #3b71ca; padding: 7px; border: 2px solid #3b71ca;');
        freeboard.addStyle(theSlider + ' .rs-handle .rs-focus', 'border-color: #3b71ca;');
        freeboard.addStyle(theSlider + ' .rs-handle-dot:after', 'border-color: #fff; background-color: #fff');
        freeboard.addStyle(theSlider + ' .rs-handle: after', 'border-color: #3b71ca; background-color: #3b71ca;');
        freeboard.addStyle(theSlider + ' .rs-border', 'border-color: transparent;');

        titleElement.html( (_.isUndefined(currentSettings.title) ? "": currentSettings.title) );
        var min = (_.isUndefined(currentSettings.min) ? 0: currentSettings.min);
        var max = (_.isUndefined(currentSettings.max) ? 100: currentSettings.max);
        var value = min; // = (_.isUndefined(currentSettings.value) ? 50: currentSettings.value);
        var topic;

        function getColor(fillPercent) {
            // mix colors
            // green rgb(169,215,11) #a9d70b
            // yellow rgb(249,200,2) #f9c802
            // red rgb(255,0,0) #ff0000

            fillPercent = 2 * fillPercent;
            var R = fillPercent * 241 + (1 - fillPercent) * 169;
            var G = fillPercent * 196 + (1 - fillPercent) * 169;
            var B = fillPercent * 15 + (1 - fillPercent) * 0;

            return "rgb(" + Math.round(R) + "," + Math.round(G) + "," + Math.round(B) + ")"
        }

        function change_icon_color(value) {
            var fillVal = 160 * (value - min)/(max - min);

            fillVal = fillVal > 160 ? 160 : fillVal;
            fillVal = fillVal < 0 ? 0 : fillVal;

            var fillColor = getColor(fillVal / 160);

            iconElement.attr('style', 'color: ' + fillColor + '; position: relative; z-index: 1; top: 65px;')
        }

		// Here we create an element to hold the text we're going to display. We're going to set the value displayed in it below.
		// **render(containerElement)** (required) : A public function we must implement that will be called when freeboard wants us to render the contents of our widget. The container element is the DIV that will surround the widget.
		self.render = function(containerElement)
		{
            $(containerElement).append(thisWidgetContainer.append(iconElement).append(titleElement));
//            titleElement.appendTo(thisWidgetContainer);
//            $(titleElement).append(valueElement);
            sliderElement.appendTo(thisWidgetContainer);

            $("#"+thisWidgetId).roundSlider({
                radius: 80,
                width: 8,
                min: min,
                max: max,
                handleSize: "+16",
                handleShape: "dot",
                sliderType: "min-range",
                circleShape: "pie",
                startAngle: "315",
                showTooltip: false,
                rangeColor: "#3b71ca",
                value: value,

                change: function(args) {
                    mqttClient.send(topic, args.value.toString());
                    change_icon_color(args.value)
                }

            });

		}
        // **getHeight()** (required) : A public function we must implement that will be called when freeboard wants to know how big we expect to be when we render, and returns a height. This function will be called any time a user updates their settings (including the first time they create the widget).
		//
		// Note here that the height is not in pixels, but in blocks. A block in freeboard is currently defined as a rectangle that is fixed at 300 pixels wide and around 45 pixels multiplied by the value you return here.
		//
		// Blocks of different sizes may be supported in the future.
		self.getHeight = function()
		{
			return 3;
		}

		// **onSettingsChanged(newSettings)** (required) : A public function we must implement that will be called when a user makes a change to the settings.
		self.onSettingsChanged = function(newSettings)
		{
			// Normally we'd update our text element with the value we defined in the user settings above (the_text), but there is a special case for settings that are of type **"calculated"** -- see below.
			currentSettings = newSettings;
			iconElement.removeAttr('class').addClass(newSettings.icon_switch).addClass('fa-5x')
            titleElement.html( (_.isUndefined(newSettings.title) ? "": newSettings.title) );
//            $(titleElement).append(valueElement);
		}

		// **onCalculatedValueChanged(settingName, newValue)** (required) : A public function we must implement that will be called when a calculated value changes. Since calculated values can change at any time (like when a datasource is updated) we handle them in a special callback function here.
		self.onCalculatedValueChanged = function(settingName, newValue)
		{
            if (settingName == "topic") topic = newValue;

            if (settingName == "value" && newValue != null) {
//                $(valueElement).html(newValue);
                $("#"+thisWidgetId).roundSlider("option", "value", newValue);
                change_icon_color(newValue);
            }

			// Remember we defined "the_text" up above in our settings.
			if (settingName == "max")
			{
                if (newValue > min ) {
                    max = newValue;
                    $("#"+thisWidgetId).roundSlider("option", "max", newValue);
                } else {
                    currentSettings.max = max; // Keep it unchanged
                    freeboard.showDialog($("<div align='center'> Max value cannot be lower than Min value!</div>"),"Warning!","OK",null,function(){});
                }
			}
			if (settingName == "min")
			{
                if (newValue < max ) {
                    min = newValue;
                    $("#"+thisWidgetId).roundSlider("option", "min", newValue);
                } else {
                    currentSettings.min= min;// Keep it unchanged
                    freeboard.showDialog($("<div align='center'> Min value cannot be greater than Max value!</div>"),"Warning!","OK",null,function(){});
                }
			}
		}

		// **onDispose()** (required) : Same as with datasource plugins.
		self.onDispose = function()
		{
		}
	}
}());
