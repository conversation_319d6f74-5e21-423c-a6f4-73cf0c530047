// TensorFlow.js Data Processor Widget for Freeboard
(function () {
    freeboard.loadWidgetPlugin({
        type_name: "tensorflow_processor",
        display_name: "TensorFlow.js Data Processor",
        description: "Process sensor data using TensorFlow.js models",
        fill_size: false,
        settings: [
            {
                name: "title",
                display_name: "Title",
                type: "text",
                default_value: "AI Data Processor",
            },
            {
                name: "input_data",
                display_name: "Input Data Source",
                type: "calculated",
                description: "Data source containing input values for the model",
            },
            {
                name: "model_url",
                display_name: "Model URL",
                type: "text",
                description: "URL to TensorFlow.js model for data processing",
            },
            {
                name: "input_shape",
                display_name: "Input Shape",
                type: "text",
                default_value: "[1, 10]",
                description: "Expected input shape as JSON array, e.g., [1, 10]",
            },
            {
                name: "preprocessing",
                display_name: "Preprocessing",
                type: "option",
                options: [
                    {
                        name: "None",
                        value: "none",
                    },
                    {
                        name: "Normalize (0-1)",
                        value: "normalize",
                    },
                    {
                        name: "Standardize (z-score)",
                        value: "standardize",
                    },
                    {
                        name: "Min-Max Scale",
                        value: "minmax",
                    },
                ],
                default_value: "none",
            },
            {
                name: "output_datasource",
                display_name: "Output Data Source",
                type: "text",
                description: "Data source name to send predictions to other widgets",
            },
            {
                name: "processing_interval",
                display_name: "Processing Interval (ms)",
                type: "number",
                default_value: 5000,
                description: "How often to process new data (0 = on data change only)",
            },
            {
                name: "show_raw_output",
                display_name: "Show Raw Output",
                type: "boolean",
                default_value: false,
            },
            {
                name: "output_labels",
                display_name: "Output Labels",
                type: "text",
                description: "Comma-separated labels for outputs, e.g., 'Normal,Anomaly'",
            },
        ],
        external_scripts: ["https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest"],

        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new tensorflowProcessorWidget(settings));
        },
    });

    var tensorflowProcessorWidget = function (settings) {
        var currentSettings = settings;
        var widgetElement;
        var model = null;
        var isModelLoaded = false;
        var currentInputData = null;
        var processingTimer = null;
        var lastProcessedData = null;

        function createWidgetHTML() {
            var html =
                '<div style="padding: 10px; font-family: Arial, sans-serif; border: 1px solid #ddd; border-radius: 5px;">';

            if (currentSettings.title) {
                html +=
                    '<div style="font-weight: bold; font-size: 16px; margin-bottom: 10px; color: #333;">' +
                    currentSettings.title +
                    "</div>";
            }

            html +=
                '<div id="status" style="padding: 8px; background: #f0f0f0; border-radius: 3px; margin-bottom: 10px; font-size: 14px;">Initializing...</div>';
            html += '<div id="input-display" style="margin-bottom: 10px; font-size: 12px; color: #666;"></div>';
            html += '<div id="output-display" style="font-size: 14px;"></div>';
            html += "</div>";

            return html;
        }

        function loadModel() {
            if (!currentSettings.model_url || currentSettings.model_url.trim() === "") {
                updateStatus("No model URL provided", "error");
                return;
            }

            updateStatus("Loading model...", "loading");

            tf.loadLayersModel(currentSettings.model_url)
                .then(function (loadedModel) {
                    model = loadedModel;
                    isModelLoaded = true;
                    updateStatus("Model loaded successfully", "success");
                    startProcessing();
                })
                .catch(function (error) {
                    console.error("Model loading error:", error);
                    updateStatus("Error loading model: " + error.message, "error");
                });
        }

        function updateStatus(message, type) {
            if (!widgetElement) return;

            var statusElement = widgetElement.find("#status");
            var color = "#333";

            switch (type) {
                case "success":
                    color = "#4CAF50";
                    break;
                case "error":
                    color = "#f44336";
                    break;
                case "loading":
                    color = "#FF9800";
                    break;
                case "processing":
                    color = "#2196F3";
                    break;
            }

            statusElement.html(message).css("color", color);
        }

        function preprocessData(data) {
            if (!Array.isArray(data)) {
                return data;
            }

            var preprocessing = currentSettings.preprocessing || "none";

            switch (preprocessing) {
                case "normalize":
                    var max = Math.max.apply(Math, data);
                    var min = Math.min.apply(Math, data);
                    return data.map(function (val) {
                        return (val - min) / (max - min);
                    });

                case "standardize":
                    var mean =
                        data.reduce(function (a, b) {
                            return a + b;
                        }, 0) / data.length;
                    var variance =
                        data.reduce(function (a, b) {
                            return a + Math.pow(b - mean, 2);
                        }, 0) / data.length;
                    var stdDev = Math.sqrt(variance);
                    return data.map(function (val) {
                        return (val - mean) / stdDev;
                    });

                case "minmax":
                    var max = Math.max.apply(Math, data);
                    var min = Math.min.apply(Math, data);
                    return data.map(function (val) {
                        return (2 * (val - min)) / (max - min) - 1;
                    });

                default:
                    return data;
            }
        }

        function processData() {
            if (!isModelLoaded || !model || !currentInputData) {
                return;
            }

            try {
                updateStatus("Processing data...", "processing");

                // Prepare input data
                var inputArray = Array.isArray(currentInputData) ? currentInputData : [currentInputData];
                var processedInput = preprocessData(inputArray);

                // Parse input shape
                var inputShape;
                try {
                    inputShape = JSON.parse(currentSettings.input_shape || "[1, 10]");
                } catch (e) {
                    inputShape = [1, processedInput.length];
                }

                // Create tensor
                var inputTensor = tf.tensor(processedInput, inputShape);

                // Make prediction
                var prediction = model.predict(inputTensor);
                var results = Array.from(prediction.dataSync());

                // Display input data
                updateInputDisplay(processedInput);

                // Display output
                updateOutputDisplay(results);

                // Send to data source
                sendToDataSource(results, processedInput);

                updateStatus("Processing complete", "success");

                // Cleanup
                inputTensor.dispose();
                prediction.dispose();
            } catch (error) {
                console.error("Processing error:", error);
                updateStatus("Processing error: " + error.message, "error");
            }
        }

        function updateInputDisplay(data) {
            if (!widgetElement) return;

            var inputElement = widgetElement.find("#input-display");
            var html =
                "<strong>Input:</strong> [" +
                data
                    .slice(0, 10)
                    .map(function (val) {
                        return typeof val === "number" ? val.toFixed(3) : val;
                    })
                    .join(", ");

            if (data.length > 10) {
                html += ", ... (" + data.length + " values)";
            }
            html += "]";

            inputElement.html(html);
        }

        function updateOutputDisplay(results) {
            if (!widgetElement) return;

            var outputElement = widgetElement.find("#output-display");
            var html = "<strong>Output:</strong><br>";

            if (currentSettings.show_raw_output) {
                html +=
                    '<div style="font-family: monospace; background: #f5f5f5; padding: 5px; border-radius: 3px; margin-top: 5px;">';
                html +=
                    "[" +
                    results
                        .map(function (val) {
                            return val.toFixed(4);
                        })
                        .join(", ") +
                    "]";
                html += "</div>";
            }

            // Apply labels if provided
            var labels = currentSettings.output_labels ? currentSettings.output_labels.split(",") : null;

            if (labels && labels.length > 0) {
                html += '<div style="margin-top: 10px;">';
                for (var i = 0; i < Math.min(results.length, labels.length); i++) {
                    var confidence = (results[i] * 100).toFixed(1);
                    var barWidth = Math.min(Math.abs(results[i]) * 100, 100);

                    html += '<div style="margin-bottom: 8px;">';
                    html +=
                        '<div style="font-size: 12px; margin-bottom: 2px;">' +
                        labels[i].trim() +
                        " (" +
                        confidence +
                        "%)</div>";
                    html += '<div style="background: #e0e0e0; height: 15px; border-radius: 7px; overflow: hidden;">';
                    html +=
                        '<div style="background: linear-gradient(90deg, #2196F3, #03DAC6); height: 100%; width: ' +
                        barWidth +
                        '%; transition: width 0.3s ease;"></div>';
                    html += "</div>";
                    html += "</div>";
                }
                html += "</div>";
            }

            outputElement.html(html);
        }

        function sendToDataSource(results, inputData) {
            if (currentSettings.output_datasource && currentSettings.output_datasource.trim() !== "") {
                var outputData = {
                    timestamp: new Date().toISOString(),
                    input: inputData,
                    output: results,
                    model_url: currentSettings.model_url,
                    preprocessing: currentSettings.preprocessing,
                };

                // Send data to Freeboard datasource
                if (typeof freeboard !== "undefined" && freeboard.setDatasourceValue) {
                    freeboard.setDatasourceValue(currentSettings.output_datasource, outputData);
                }
            }
        }

        function startProcessing() {
            if (processingTimer) {
                clearInterval(processingTimer);
            }

            var interval = currentSettings.processing_interval || 0;
            if (interval > 0) {
                processingTimer = setInterval(function () {
                    if (currentInputData !== lastProcessedData) {
                        processData();
                        lastProcessedData = currentInputData;
                    }
                }, interval);
            }
        }

        this.render = function (containerElement) {
            widgetElement = $(containerElement);
            widgetElement.html(createWidgetHTML());
            loadModel();
        };

        this.getHeight = function () {
            return 3; // Default height in grid units
        };

        this.onSettingsChanged = function (newSettings) {
            currentSettings = newSettings;
            isModelLoaded = false;
            model = null;

            if (processingTimer) {
                clearInterval(processingTimer);
            }

            widgetElement.html(createWidgetHTML());
            loadModel();
        };

        this.onCalculatedValueChanged = function (settingName, newValue) {
            if (settingName === "input_data") {
                currentInputData = newValue;

                // Process immediately if no interval is set
                if (!currentSettings.processing_interval || currentSettings.processing_interval === 0) {
                    processData();
                }
            }
        };

        this.onDispose = function () {
            if (processingTimer) {
                clearInterval(processingTimer);
            }
            if (model) {
                model.dispose();
            }
        };
    };
})();
