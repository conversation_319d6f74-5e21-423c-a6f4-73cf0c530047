(function () {
    freeboard.loadWidgetPlugin({
        type_name: "beePointer",
        display_name: "Bee Pointer",
        "external_scripts": [
            "/assets/Freeboard/plugins/thirdparty/raphael.2.1.0.min.js"
        ],
        settings: [
            {
                name: "value",
                display_name: "Value",
                type: "calculated",
            },
            {
                name: "units",
                display_name: "Units",
                type: "text"
            }
        ],
        newInstance: function (settings, newInstanceCallback) {
            newInstanceCallback(new beePointerWidget(settings));
        }
    });

    freeboard.addStyle('div.bee-pointer-value', "position: absolute; height: 95px; margin: auto; top: 0px; bottom: 0px; width: 100%; text-align:center;");

    var beePointerWidget = function (settings) {
        var self = this;
        var paper;
        var strokeWidth = 3;
        var triangle;
        var width, height;
        var currentValue = 0;
        var valueDiv = $('<div class="widget-big-text" style=" color: black;"></div>');
        var unitsDiv = $('<div style="font-size: 20px; color: black;"></div>');
        var topic;

        function polygonPath(points) {
            if (!points || points.length < 2)
                return [];
            var path = []; //will use path object type
            path.push(['m', points[0], points[1]]);
            for (var i = 2; i < points.length; i += 2) {
                path.push(['l', points[i], points[i + 1]]);
            }
            path.push(['z']);
            return path;
        }

        this.render = function (element) {
            width = $(element).width();
            height = '240';//$(element).height();

            var radius = Math.min(width, height) / 2 - strokeWidth * 2;

            paper = Raphael($(element).get()[0], width, height);
            var circle = paper.circle(width / 2, height / 2, radius);
            circle.attr("stroke", "#FF9900");
            circle.attr("stroke-width", strokeWidth);

            triangle = paper.path(polygonPath([width / 2, (height / 2) - radius + strokeWidth, 15, 20, -30, 0]));
            triangle.attr("stroke-width", 0);
            triangle.attr("fill", "#F1C40F");

            $(element).append($('<div class="bee-pointer-value" style="transform: translateX(-10px);"></div>').append(valueDiv).append(unitsDiv));
        }

        this.onSettingsChanged = function (newSettings) {
            unitsDiv.html(newSettings.units);
        }

        this.onCalculatedValueChanged = function (settingName, newValue) {
            if (settingName == "value" && newValue != null) {
                if (!_.isUndefined(triangle)) {
                    var direction = "r";

                    var oppositeCurrent = currentValue + 180;

                    if (oppositeCurrent < newValue) {
                        //direction = "l";
                    }

                    triangle.animate({ transform: "r" + newValue + "," + (width / 2) + "," + (height / 2) }, 250, "bounce");
                }

                currentValue = newValue;
                valueDiv.html(newValue);
            }
            //            if (settingName == "direction") {
            //
            //            }
            //            else if (settingName == "value_text") {
            //
            //            }
        }

        this.onDispose = function () {
        }

        this.getHeight = function () {
            return 4;
        }

        this.onSettingsChanged(settings);
    };
}());
