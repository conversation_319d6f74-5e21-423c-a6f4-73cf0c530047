// Debug script for TensorFlow widgets
(function () {
    console.log("🔍 TensorFlow Widgets Debug Script Loaded");

    // Check if freeboard is available
    if (typeof freeboard === "undefined") {
        console.error("❌ Freeboard is not available");
        return;
    }

    // Check if TensorFlow widgets are registered
    setTimeout(function () {
        console.log("🧠 Checking TensorFlow widget registration...");

        // Check freeboard object structure
        console.log("🔍 Freeboard object:", freeboard);

        // Try to access widget plugins directly
        if (freeboard.widgetPlugins) {
            console.log("📋 Widget plugins:", Object.keys(freeboard.widgetPlugins));

            // Check for TensorFlow widgets specifically
            var tensorflowWidgets = Object.keys(freeboard.widgetPlugins).filter(function (key) {
                return key.includes("tensorflow");
            });

            if (tensorflowWidgets.length > 0) {
                console.log("✅ TensorFlow widgets found:", tensorflowWidgets);
                tensorflowWidgets.forEach(function (widgetName) {
                    console.log("📝 Widget details:", widgetName, freeboard.widgetPlugins[widgetName]);
                });
            } else {
                console.warn("⚠️ No TensorFlow widgets found in registry");
                console.log("📋 Available widgets:", Object.keys(freeboard.widgetPlugins));
            }
        } else {
            console.warn("⚠️ freeboard.widgetPlugins not available");
        }

        // Check if TensorFlow.js is loaded
        if (typeof tf !== "undefined") {
            console.log("✅ TensorFlow.js is loaded, version:", tf.version);
        } else {
            console.warn("⚠️ TensorFlow.js is not loaded");
        }

        // Check if mobilenet is loaded
        if (typeof mobilenet !== "undefined") {
            console.log("✅ MobileNet is loaded");
        } else {
            console.warn("⚠️ MobileNet is not loaded");
        }

        // Check if cocoSsd is loaded
        if (typeof cocoSsd !== "undefined") {
            console.log("✅ COCO-SSD is loaded");
        } else {
            console.warn("⚠️ COCO-SSD is not loaded");
        }

        // Check if posenet is loaded
        if (typeof posenet !== "undefined") {
            console.log("✅ PoseNet is loaded");
        } else {
            console.warn("⚠️ PoseNet is not loaded");
        }
    }, 2000); // Wait 2 seconds for everything to load
})();
