// # A Freeboard Plugin that uses the Eclipse Paho javascript client to read MQTT messages
var mqttClient;
(function () {
	// ### Datasource Definition
	// Please replace the external_scripts location with a local replica of the Paho MQTT client when possible
	// -------------------
	var currentdate = new Date();
	var datetime = currentdate.getDate()
		+ (currentdate.getMonth() + 1)
		+ currentdate.getFullYear()
		+ currentdate.getHours()
		+ currentdate.getMinutes()
		+ currentdate.getSeconds();

	freeboard.loadDatasourcePlugin({
		"type_name": "beeiot_mqtt",
		"display_name": "BeeIoT MQTT",
		"description": "Kết nối với thiết bị BeeIoT.",
		"external_scripts": [
			"https://rawgit.com/benjaminchodroff/freeboard-mqtt/paho-mqtt-default/mqttws31.js"
		],
		"settings": [
			{
				"name": "server",
				"display_name": "MQTT Server",
				"type": "text",
				"description": "Hostname for your MQTT Server",
				"default_value": "beeblock.vn",
				"required": true
			},
			{
				"name": "port",
				"display_name": "Port",
				"type": "number",
				"description": "The port to connect to the MQTT Server on",
				"default_value": 9001,
				"required": true
			},
			{
				"name": "use_ssl",
				"display_name": "Use SSL",
				"type": "boolean",
				"description": "Use SSL/TLS to connect to the MQTT Server",
				"default_value": true
			},
			{
				"name": "client_id",
				"display_name": "Client Id",
				"type": "text",
				"default_value": datetime.toString(),
				"required": false
			},
			{
				"name": "username",
				"display_name": "Username",
				"type": "text",
				"default_value": "",
				"required": true
			},
			{
				"name": "password",
				"display_name": "Password",
				"type": "password",
				"default_value": "",
				"required": true
			},
			{
				"name": "topic",
				"display_name": "Topic",
				"type": "calculated",
				"description": "The topic to subscribe to",
				"default_value": "",
				"multi_input": true,
				"required": true
			},
			{
				"name": "json_data",
				"display_name": "JSON messages?",
				"type": "boolean",
				"description": "If the messages on your topic are in JSON format they will be parsed so the individual fields can be used in freeboard widgets",
				"default_value": false
			}
		],
		// **newInstance(settings, newInstanceCallback, updateCallback)** (required) : A function that will be called when a new instance of this plugin is requested.
		// * **settings** : A javascript object with the initial settings set by the user. The names of the properties in the object will correspond to the setting names defined above.
		// * **newInstanceCallback** : A callback function that you'll call when the new instance of the plugin is ready. This function expects a single argument, which is the new instance of your plugin object.
		// * **updateCallback** : A callback function that you'll call if and when your datasource has an update for freeboard to recalculate. This function expects a single parameter which is a javascript object with the new, updated data. You should hold on to this reference and call it when needed.
		newInstance: function (settings, newInstanceCallback, updateCallback) {
			newInstanceCallback(new mqttDatasourcePlugin(settings, updateCallback));
		}
	});

	var mqttDatasourcePlugin = function (settings, updateCallback) {
		var self = this;
		var data = {};

		var currentSettings = settings;

		function onConnect() {
			data = {}
			for (var i = 0; i < currentSettings.topic.length; i++) {
				client.subscribe(currentSettings.topic[i]);
				data[currentSettings.topic[i]] = { 'topic': currentSettings.topic[i], 'msg': null };
			}
			// Preset topic and message
			updateCallback(data);
		};

		function onConnectionLost(responseObject) {
			if (responseObject.errorCode !== 0)
				console.error("onConnectionLost:" + responseObject.errorMessage);
		};

		function onMessageArrived(message) {
			msg = "";
			if (currentSettings.json_data) msg = JSON.parse(message.payloadString);
			else msg = message.payloadString;

			data = {}
			for (var i = 0; i < currentSettings.topic.length; i++) {
				if (message.destinationName == currentSettings.topic[i])
					data[currentSettings.topic[i]] = { 'topic': currentSettings.topic[i], 'msg': msg };
				else
					data[currentSettings.topic[i]] = { 'topic': currentSettings.topic[i], 'msg': null };
			}
			updateCallback(data);
		};

		// **onSettingsChanged(newSettings)** (required) : A public function we must implement that will be called when a user makes a change to the settings.
		self.onSettingsChanged = function (newSettings) {
			client.disconnect();
			data = {};
			currentSettings = newSettings;
			client.connect({
				onSuccess: onConnect,
				userName: currentSettings.username,
				password: currentSettings.password,
				useSSL: currentSettings.use_ssl
			});
		}

		// **updateNow()** (required) : A public function we must implement that will be called when the user wants to manually refresh the datasource
		self.updateNow = function () {
			// Don't need to do anything here, can't pull an update from MQTT.
		}

		// **onDispose()** (required) : A public function we must implement that will be called when this instance of this plugin is no longer needed. Do anything you need to cleanup after yourself here.
		self.onDispose = function () {
			if (client.isConnected()) {
				client.disconnect();
			}
			client = {};
		}

		var client = new Paho.MQTT.Client(currentSettings.server,
			currentSettings.port,
			currentSettings.client_id);
		client.onConnectionLost = onConnectionLost;
		client.onMessageArrived = onMessageArrived;
		client.connect({
			onSuccess: onConnect,

			userName: currentSettings.username,
			password: currentSettings.password,
			useSSL: currentSettings.use_ssl
		});


		mqttClient = client;
	}
}());
