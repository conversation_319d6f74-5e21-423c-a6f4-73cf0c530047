// TensorFlow.js Datasource Plugin for Freeboard
(function() {
    // Define the plugin
    freeboard.loadDatasourcePlugin({
        "type_name": "tensorflow_js",
        "display_name": "TensorFlow.js Camera",
        "description": "Process camera input with TensorFlow.js models for image prediction",
        "external_scripts": [
            "https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest",
            "https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@latest"
        ],
        "settings": [
            {
                "name": "model_type",
                "display_name": "Model Type",
                "type": "option",
                "options": [
                    {
                        "name": "MobileNet",
                        "value": "mobilenet"
                    },
                    {
                        "name": "Custom Model",
                        "value": "custom"
                    }
                ],
                "default_value": "mobilenet"
            },
            {
                "name": "custom_model_url",
                "display_name": "Custom Model URL",
                "type": "text",
                "description": "URL to your custom TensorFlow.js model (only if Custom Model is selected)",
                "required": false
            },
            {
                "name": "camera_id",
                "display_name": "Camera ID",
                "type": "option",
                "options": [
                    {
                        "name": "Front Camera",
                        "value": "user"
                    },
                    {
                        "name": "Back Camera",
                        "value": "environment"
                    }
                ],
                "default_value": "environment"
            },
            {
                "name": "refresh",
                "display_name": "Prediction Interval",
                "type": "number",
                "suffix": "seconds",
                "default_value": 3
            }
        ],
        // Create a new instance of the plugin
        newInstance: function(settings, newInstanceCallback, updateCallback) {
            newInstanceCallback(new tensorflowDatasource(settings, updateCallback));
        }
    });

    // Datasource implementation
    var tensorflowDatasource = function(settings, updateCallback) {
        var self = this;
        var currentSettings = settings;
        var model = null;
        var videoElement = null;
        var canvasElement = null;
        var updateTimer = null;
        var isCapturing = false;

        function initializeCamera() {
            // Create video element if it doesn't exist
            if (!videoElement) {
                videoElement = document.createElement('video');
                videoElement.setAttribute('autoplay', '');
                videoElement.setAttribute('playsinline', '');
                videoElement.style.width = '1px';
                videoElement.style.height = '1px';
                videoElement.style.position = 'absolute';
                videoElement.style.top = '-1px';
                videoElement.style.left = '-1px';
                document.body.appendChild(videoElement);
            }

            // Create canvas for processing
            if (!canvasElement) {
                canvasElement = document.createElement('canvas');
                canvasElement.style.display = 'none';
                document.body.appendChild(canvasElement);
            }

            // Start camera
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                navigator.mediaDevices.getUserMedia({
                    video: { facingMode: currentSettings.camera_id }
                }).then(function(stream) {
                    videoElement.srcObject = stream;
                    isCapturing = true;
                }).catch(function(error) {
                    console.error("Camera error:", error);
                    updateCallback({
                        error: "Camera access error: " + error.message,
                        predictions: []
                    });
                });
            } else {
                updateCallback({
                    error: "Camera not supported in this browser",
                    predictions: []
                });
            }
        }

        function loadModel() {
            if (currentSettings.model_type === "mobilenet") {
                mobilenet.load().then(function(loadedModel) {
                    model = loadedModel;
                    updateCallback({
                        status: "Model loaded successfully",
                        predictions: []
                    });
                }).catch(function(error) {
                    console.error("Model loading error:", error);
                    updateCallback({
                        error: "Model loading error: " + error.message,
                        predictions: []
                    });
                });
            } else if (currentSettings.model_type === "custom" && currentSettings.custom_model_url) {
                tf.loadLayersModel(currentSettings.custom_model_url).then(function(loadedModel) {
                    model = loadedModel;
                    updateCallback({
                        status: "Custom model loaded successfully",
                        predictions: []
                    });
                }).catch(function(error) {
                    console.error("Custom model loading error:", error);
                    updateCallback({
                        error: "Custom model loading error: " + error.message,
                        predictions: []
                    });
                });
            } else {
                updateCallback({
                    error: "Invalid model configuration",
                    predictions: []
                });
            }
        }

        function captureAndPredict() {
            if (!model || !videoElement || !isCapturing) return;

            // Set canvas dimensions to match video
            canvasElement.width = videoElement.videoWidth;
            canvasElement.height = videoElement.videoHeight;
            
            // Draw video frame to canvas
            var ctx = canvasElement.getContext('2d');
            ctx.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);
            
            // Make prediction based on model type
            if (currentSettings.model_type === "mobilenet") {
                model.classify(videoElement).then(function(predictions) {
                    updateCallback({
                        timestamp: new Date(),
                        predictions: predictions,
                        topPrediction: predictions.length > 0 ? predictions[0] : null
                    });
                });
            } else {
                // For custom model - adjust as needed for your model's input requirements
                const tensor = tf.browser.fromPixels(videoElement)
                    .resizeNearestNeighbor([224, 224])
                    .toFloat()
                    .expandDims();
                
                const prediction = model.predict(tensor);
                const results = Array.from(prediction.dataSync());
                
                updateCallback({
                    timestamp: new Date(),
                    rawPrediction: results,
                    // Add more processing specific to your model here
                });
                
                tensor.dispose();
                prediction.dispose();
            }
        }

        // Initialize the datasource
        this.initialize = function() {
            loadModel();
            initializeCamera();
            
            if (currentSettings.refresh && currentSettings.refresh > 0) {
                updateTimer = setInterval(captureAndPredict, currentSettings.refresh * 1000);
            }
        };

        // Manually trigger an update
        this.updateNow = function() {
            captureAndPredict();
        };

        // Clean up when the datasource is disposed
        this.dispose = function() {
            if (updateTimer) {
                clearInterval(updateTimer);
                updateTimer = null;
            }
            
            if (videoElement && videoElement.srcObject) {
                videoElement.srcObject.getTracks().forEach(track => track.stop());
                document.body.removeChild(videoElement);
                videoElement = null;
            }
            
            if (canvasElement) {
                document.body.removeChild(canvasElement);
                canvasElement = null;
            }
            
            isCapturing = false;
        };

        // Handle settings updates
        this.onSettingsChanged = function(newSettings) {
            // Stop current processing
            if (updateTimer) {
                clearInterval(updateTimer);
                updateTimer = null;
            }
            
            if (videoElement && videoElement.srcObject) {
                videoElement.srcObject.getTracks().forEach(track => track.stop());
                isCapturing = false;
            }
            
            // Apply new settings
            currentSettings = newSettings;
            
            // Restart with new settings
            loadModel();
            initializeCamera();
            
            if (currentSettings.refresh && currentSettings.refresh > 0) {
                updateTimer = setInterval(captureAndPredict, currentSettings.refresh * 1000);
            }
        };

        // Initialize the plugin
        this.initialize();
    };
}());