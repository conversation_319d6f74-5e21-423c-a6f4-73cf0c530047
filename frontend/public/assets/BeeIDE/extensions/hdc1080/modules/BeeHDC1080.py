from micropython import const
from machine import I2C, Pin
from time import sleep


class BeeHDC1080:
    TEMP_REG = const(0x00)  # temperature register
    HUMI_REG = const(0x01)  # humidity register
    CONF_REG = const(0x02)  # configuration register
    FSER_REG = const(0xFB)  # first two bytes of serial ID register
    MSER_REG = const(0xFC)  # middle two bytes of serial ID register
    LSER_REG = const(0xFD)  # last two bytes of serial ID register
    MFID_REG = const(0xFE)  # manufacturer ID register
    DVID_REG = const(0xFF)  # device ID register

    def __init__(self, port, slave_addr=64):
        self.i2c = I2C(scl=Pin(port.use_pins(1)), sda=Pin(port.use_pins(2)))
        self.addr = slave_addr
        self.fmt = '>2B'
        setup_data = 1 << 4
        data = bytearray(3)
        data[0] = CONF_REG
        data[1] = setup_data
        self.i2c.writeto(self.addr, data)

    @property
    def temperature(self, celsius=True):
        data = bytearray([TEMP_REG])
        self.i2c.writeto(self.addr, data)
        sleep(0.0635)
        data = self.i2c.readfrom(self.addr, 2)
        value = int.from_bytes(data, "big")
        if celsius:
            value = (value / (2 ** 16)) * 165 - 40
        else:
            value = (1.8 * value / (2 ** 16)) * 165 - 40
        return value

    @property
    def humidity(self):
        data = bytearray([HUMI_REG])
        self.i2c.writeto(self.addr, data)
        sleep(0.065)
        data = self.i2c.readfrom(self.addr, 2)
        value = int.from_bytes(data, "big")
        return (value / (2 ** 16)) * 100