import time

from machine import Pin, time_pulse_us

class BeeGroveUltrasonic:

    def __init__(self, port):
        self.sig = Pin(port.use_pins(1), Pin.OUT)

    @property
    def distance_cm(self) -> float:
        self.sig.init(Pin.OUT)
        self.sig.value(0)
        time.sleep_us(2)
        self.sig.value(1)
        time.sleep_us(10)
        self.sig.value(0)

        self.sig.init(Pin.IN)
        try:
            duration = time_pulse_us(self.sig, 1, 30000)
            distance = (duration * 0.0343) / 2
            return distance
        except OSError as ex:
            return -1

    @property
    def distance_mm(self) -> int:
        return int(self.distance_cm * 10)
