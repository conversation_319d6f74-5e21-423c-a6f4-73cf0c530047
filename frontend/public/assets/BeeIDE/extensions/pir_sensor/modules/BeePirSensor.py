import time

from machine import Pin

class BeePirSensor:
    
    def __init__(self, port):
       self.pin = Pin(port.use_pins(1), Pin.IN)

    @property
    def is_motion_detected(self):
        return self.pin.value() == 1

    def wait_for_motion(self, timeout=10):
        start = time.ticks_ms()
        while time.ticks_diff(time.ticks_ms(), start) < timeout * 1000:
            if self.is_motion_detected:
                return True
            time.sleep_ms(100)
        return False
