from machine import Pin, ADC
import time

class BeeGroveLightSensor:
    def __init__(self, port):
        self.adc = ADC(Pin(port.use_pins(1)))
        self.max_adc = 4095

    @property
    def read_light_level(self):
        return self.adc.read()

    @property
    def read_light_percent(self):
        raw_value = self.read_light_level
        return (raw_value * 100) // self.max_adc

    def is_bright(self, threshold_percent=50):
        threshold = (threshold_percent * self.max_adc) // 100
        return self.read_light_level > threshold

    def is_dark(self, threshold_percent=50):
        threshold = (threshold_percent * self.max_adc) // 100
        return self.read_light_level <= threshold
