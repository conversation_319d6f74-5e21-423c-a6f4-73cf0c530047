from machine import Pin, ADC
import time

class BeeGroveRotationSensor:
    
    def __init__(self, port):
        self.adc = ADC(Pin(port.use_pins(1)))
        self.max_adc = 4095
        self.full_angle = 300

    @property
    def read_angle_raw(self):
        return self.adc.read()

    @property
    def read_angle_degrees(self):
        raw_value = self.read_angle_raw
        voltage = (raw_value * 3.3) / self.max_adc
        degrees = (voltage * self.full_angle) / 3.3
        return round(degrees, 1)

    @property
    def read_angle_percentage(self):
        raw_value = self.read_angle_raw
        return (raw_value * 100) // self.max_adc

    def is_rotated_past(self, threshold_percent=50, unit="percentage"):
        if unit == "degree":
            threshold_percent = (threshold_percent / self.full_angle) * 100
        threshold = (threshold_percent * self.max_adc) // 100
        return self.read_angle_raw > threshold

    def is_rotated_below(self, threshold_percent=50, unit="percentage"):
        if unit == "degree":
            threshold_percent = (threshold_percent / self.full_angle) * 100
        threshold = (threshold_percent * self.max_adc) // 100
        return self.read_angle_raw <= threshold