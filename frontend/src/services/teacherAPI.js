import axiosInstance from './axiosInstance';

/**
 * Teacher API Service
 * Handles all API calls related to teacher functionality
 */

const teacherAPI = {
    // ==================== COURSE MANAGEMENT ====================

    /**
     * Get all courses for the current teacher
     * @returns {Promise} List of courses
     */
    getCourses: async () => {
        try {
            const response = await axiosInstance.get('/api/teacher/courses/');
            return response.data;
        } catch (error) {
            console.error('Error fetching teacher courses:', error);
            throw error;
        }
    },

    /**
     * Get course details by ID
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Course details
     */
    getCourseById: async (courseId) => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching course ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Create a new course
     * @param {Object} courseData - Course data
     * @returns {Promise} Created course
     */
    createCourse: async (courseData) => {
        try {
            // Check if courseData is FormData (for file uploads)
            const config = {};
            if (courseData instanceof FormData) {
                // Remove Content-Type header to let browser set it automatically with boundary
                config.headers = {
                    'Content-Type': undefined
                };
            }

            const response = await axiosInstance.post('/api/teacher/courses/', courseData, config);
            return response.data;
        } catch (error) {
            console.error('Error creating course:', error);
            throw error;
        }
    },

    /**
     * Update an existing course
     * @param {string|number} courseId - Course ID
     * @param {Object} courseData - Updated course data
     * @returns {Promise} Updated course
     */
    updateCourse: async (courseId, courseData) => {
        try {
            // Check if courseData is FormData (for file uploads)
            const config = {};
            if (courseData instanceof FormData) {
                // Remove Content-Type header to let browser set it automatically with boundary
                config.headers = {
                    'Content-Type': undefined
                };
            }

            const response = await axiosInstance.put(`/api/teacher/courses/${courseId}/`, courseData, config);
            return response.data;
        } catch (error) {
            console.error(`Error updating course ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Delete a course
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Success message
     */
    deleteCourse: async (courseId) => {
        try {
            const response = await axiosInstance.delete(`/api/teacher/courses/${courseId}/`);
            return response.data;
        } catch (error) {
            console.error(`Error deleting course ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Publish/Unpublish a course
     * @param {string|number} courseId - Course ID
     * @param {boolean} isPublished - Publish status
     * @returns {Promise} Updated course
     */
    toggleCoursePublication: async (courseId, isPublished) => {
        try {
            const response = await axiosInstance.patch(`/api/teacher/courses/${courseId}/publish/`, {
                is_published: isPublished
            });
            return response.data;
        } catch (error) {
            console.error(`Error toggling course publication ${courseId}:`, error);
            throw error;
        }
    },

    // ==================== COURSE CONTENT MANAGEMENT ====================

    /**
     * Get course content (lessons, quizzes, assignments)
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Course content
     */
    getCourseContent: async (courseId) => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/content/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching course content ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Create course content (lesson, quiz, assignment)
     * @param {string|number} courseId - Course ID
     * @param {Object} contentData - Content data
     * @returns {Promise} Created content
     */
    createCourseContent: async (courseId, contentData) => {
        try {
            const response = await axiosInstance.post(`/api/teacher/courses/${courseId}/content/`, contentData);
            return response.data;
        } catch (error) {
            console.error(`Error creating course content for ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Update course content
     * @param {string|number} courseId - Course ID
     * @param {string|number} contentId - Content ID
     * @param {Object} contentData - Updated content data
     * @returns {Promise} Updated content
     */
    updateCourseContent: async (courseId, contentId, contentData) => {
        try {
            const response = await axiosInstance.put(`/api/teacher/courses/${courseId}/content/${contentId}/`, contentData);
            return response.data;
        } catch (error) {
            console.error(`Error updating course content ${contentId}:`, error);
            throw error;
        }
    },

    /**
     * Delete course content
     * @param {string|number} courseId - Course ID
     * @param {string|number} contentId - Content ID
     * @returns {Promise} Success message
     */
    deleteCourseContent: async (courseId, contentId) => {
        try {
            const response = await axiosInstance.delete(`/api/teacher/courses/${courseId}/content/${contentId}/`);
            return response.data;
        } catch (error) {
            console.error(`Error deleting course content ${contentId}:`, error);
            throw error;
        }
    },

    // ==================== STUDENT MANAGEMENT ====================

    /**
     * Get enrolled students for a course
     * @param {string|number} courseId - Course ID
     * @returns {Promise} List of enrolled students
     */
    getCourseStudents: async (courseId) => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/students/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching course students ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Get student progress in a course
     * @param {string|number} courseId - Course ID
     * @param {string|number} studentId - Student ID
     * @returns {Promise} Student progress data
     */
    getStudentProgress: async (courseId, studentId) => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/students/${studentId}/progress/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching student progress ${studentId} in course ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Assign course to students
     * @param {string|number} courseId - Course ID
     * @param {Array} studentIds - Array of student IDs
     * @returns {Promise} Assignment result
     */
    assignCourseToStudents: async (courseId, studentIds) => {
        try {
            const response = await axiosInstance.post(`/api/teacher/courses/${courseId}/assign/`, {
                student_ids: studentIds
            });
            return response.data;
        } catch (error) {
            console.error(`Error assigning course ${courseId} to students:`, error);
            throw error;
        }
    },

    // ==================== QUIZ & ASSIGNMENT MANAGEMENT ====================

    /**
     * Get quiz submissions for a course
     * @param {string|number} courseId - Course ID
     * @param {string|number} quizId - Quiz ID
     * @returns {Promise} Quiz submissions
     */
    getQuizSubmissions: async (courseId, quizId) => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/quizzes/${quizId}/submissions/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching quiz submissions ${quizId}:`, error);
            throw error;
        }
    },

    /**
     * Grade quiz submission
     * @param {string|number} courseId - Course ID
     * @param {string|number} quizId - Quiz ID
     * @param {string|number} submissionId - Submission ID
     * @param {Object} gradeData - Grade data
     * @returns {Promise} Graded submission
     */
    gradeQuizSubmission: async (courseId, quizId, submissionId, gradeData) => {
        try {
            const response = await axiosInstance.post(
                `/api/teacher/courses/${courseId}/quizzes/${quizId}/submissions/${submissionId}/grade/`,
                gradeData
            );
            return response.data;
        } catch (error) {
            console.error(`Error grading quiz submission ${submissionId}:`, error);
            throw error;
        }
    },

    /**
     * Get TurboWarp submissions for a course
     * @param {string|number} courseId - Course ID
     * @param {string|number} assignmentId - Assignment ID
     * @returns {Promise} TurboWarp submissions
     */
    getTurboWarpSubmissions: async (courseId, assignmentId) => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/assignments/${assignmentId}/submissions/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching TurboWarp submissions ${assignmentId}:`, error);
            throw error;
        }
    },

    /**
     * Grade TurboWarp submission
     * @param {string|number} courseId - Course ID
     * @param {string|number} assignmentId - Assignment ID
     * @param {string|number} submissionId - Submission ID
     * @param {Object} gradeData - Grade data
     * @returns {Promise} Graded submission
     */
    gradeTurboWarpSubmission: async (courseId, assignmentId, submissionId, gradeData) => {
        try {
            const response = await axiosInstance.post(
                `/api/teacher/courses/${courseId}/assignments/${assignmentId}/submissions/${submissionId}/grade/`,
                gradeData
            );
            return response.data;
        } catch (error) {
            console.error(`Error grading TurboWarp submission ${submissionId}:`, error);
            throw error;
        }
    },

    // ==================== ANALYTICS & REPORTS ====================

    /**
     * Get course analytics
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Course analytics data
     */
    getCourseAnalytics: async (courseId) => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/analytics/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching course analytics ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Get teacher dashboard statistics
     * @returns {Promise} Dashboard statistics
     */
    getDashboardStats: async () => {
        try {
            const response = await axiosInstance.get('/api/teacher/dashboard/stats/');
            return response.data;
        } catch (error) {
            console.error('Error fetching teacher dashboard stats:', error);
            throw error;
        }
    },

    /**
     * Export course data
     * @param {string|number} courseId - Course ID
     * @param {string} format - Export format (csv, xlsx, pdf)
     * @returns {Promise} Export file
     */
    exportCourseData: async (courseId, format = 'csv') => {
        try {
            const response = await axiosInstance.get(`/api/teacher/courses/${courseId}/export/`, {
                params: { format },
                responseType: 'blob'
            });
            return response.data;
        } catch (error) {
            console.error(`Error exporting course data ${courseId}:`, error);
            throw error;
        }
    },

    // ==================== SUBJECTS & GRADES MANAGEMENT ====================

    /**
     * Get all subjects
     * @returns {Promise} List of subjects
     */
    getSubjects: async () => {
        try {
            const response = await axiosInstance.get('/api/elearning/teacher/subjects/');
            return response.data;
        } catch (error) {
            console.error('Error fetching subjects:', error);
            throw error;
        }
    },

    /**
     * Create a new subject
     * @param {Object} subjectData - Subject data (name, description)
     * @returns {Promise} Created subject
     */
    createSubject: async (subjectData) => {
        try {
            const response = await axiosInstance.post('/api/elearning/teacher/subjects/', subjectData);
            return response.data;
        } catch (error) {
            console.error('Error creating subject:', error);
            throw error;
        }
    },

    /**
     * Get all grades
     * @returns {Promise} List of grades
     */
    getGrades: async () => {
        try {
            const response = await axiosInstance.get('/api/elearning/teacher/grades/');
            return response.data;
        } catch (error) {
            console.error('Error fetching grades:', error);
            throw error;
        }
    },

    /**
     * Create a new grade
     * @param {Object} gradeData - Grade data (name, description)
     * @returns {Promise} Created grade
     */
    createGrade: async (gradeData) => {
        try {
            const response = await axiosInstance.post('/api/elearning/teacher/grades/', gradeData);
            return response.data;
        } catch (error) {
            console.error('Error creating grade:', error);
            throw error;
        }
    }
};

export default teacherAPI;
