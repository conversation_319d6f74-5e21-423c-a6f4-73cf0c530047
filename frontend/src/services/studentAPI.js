import axiosInstance from './axiosInstance';

/**
 * Student API Service
 * Handles all API calls related to student functionality
 */

const studentAPI = {
    // ==================== COURSE DISCOVERY & ENROLLMENT ====================

    /**
     * Get available courses for enrollment
     * @param {Object} filters - Filter options (subject, price_range, difficulty, etc.)
     * @returns {Promise} List of available courses
     */
    getAvailableCourses: async (filters = {}) => {
        try {
            const response = await axiosInstance.get('/api/student/courses/available/', {
                params: filters
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching available courses:', error);
            throw error;
        }
    },

    /**
     * Get enrolled courses for the current student
     * @returns {Promise} List of enrolled courses
     */
    getEnrolledCourses: async () => {
        try {
            const response = await axiosInstance.get('/api/student/courses/enrolled/');
            return response.data;
        } catch (error) {
            console.error('Error fetching enrolled courses:', error);
            throw error;
        }
    },

    /**
     * Get course details by ID
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Course details
     */
    getCourseDetails: async (courseId) => {
        try {
            const response = await axiosInstance.get(`/api/student/courses/${courseId}/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching course details ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Purchase/Enroll in a course
     * @param {string|number} courseId - Course ID
     * @param {Object} paymentData - Payment information (for paid courses)
     * @returns {Promise} Enrollment result
     */
    purchaseCourse: async (courseId, paymentData = {}) => {
        try {
            const response = await axiosInstance.post(`/api/student/courses/${courseId}/purchase/`, paymentData);
            return response.data;
        } catch (error) {
            console.error(`Error purchasing course ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Get course content for enrolled course
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Course content (lessons, quizzes, assignments)
     */
    getCourseContent: async (courseId) => {
        try {
            const response = await axiosInstance.get(`/api/student/courses/${courseId}/content/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching course content ${courseId}:`, error);
            throw error;
        }
    },

    // ==================== LEARNING PROGRESS ====================

    /**
     * Get student's progress in a course
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Progress data
     */
    getCourseProgress: async (courseId) => {
        try {
            const response = await axiosInstance.get(`/api/student/courses/${courseId}/progress/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching course progress ${courseId}:`, error);
            throw error;
        }
    },

    /**
     * Mark lesson as completed
     * @param {string|number} courseId - Course ID
     * @param {string|number} lessonId - Lesson ID
     * @param {Object} completionData - Completion data (time_spent, etc.)
     * @returns {Promise} Updated progress
     */
    markLessonComplete: async (courseId, lessonId, completionData = {}) => {
        try {
            const response = await axiosInstance.post(
                `/api/student/courses/${courseId}/lessons/${lessonId}/complete/`,
                completionData
            );
            return response.data;
        } catch (error) {
            console.error(`Error marking lesson complete ${lessonId}:`, error);
            throw error;
        }
    },

    /**
     * Update lesson progress (for timer tracking)
     * @param {string|number} courseId - Course ID
     * @param {string|number} lessonId - Lesson ID
     * @param {Object} progressData - Progress data (time_spent, current_position, etc.)
     * @returns {Promise} Updated progress
     */
    updateLessonProgress: async (courseId, lessonId, progressData) => {
        try {
            const response = await axiosInstance.patch(
                `/api/student/courses/${courseId}/lessons/${lessonId}/progress/`,
                progressData
            );
            return response.data;
        } catch (error) {
            console.error(`Error updating lesson progress ${lessonId}:`, error);
            throw error;
        }
    },

    /**
     * Complete course and generate certificate
     * @param {string|number} courseId - Course ID
     * @returns {Promise} Completion data with certificate
     */
    completeCourse: async (courseId) => {
        try {
            const response = await axiosInstance.post(
                `/api/student/courses/${courseId}/complete/`
            );
            return response.data;
        } catch (error) {
            console.error(`Error completing course ${courseId}:`, error);
            throw error;
        }
    },

    // ==================== QUIZ MANAGEMENT ====================

    /**
     * Get quiz details
     * @param {string|number} courseId - Course ID
     * @param {string|number} quizId - Quiz ID
     * @returns {Promise} Quiz details
     */
    getQuizDetails: async (courseId, quizId) => {
        try {
            const response = await axiosInstance.get(`/api/student/courses/${courseId}/quizzes/${quizId}/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching quiz details ${quizId}:`, error);
            throw error;
        }
    },

    /**
     * Start quiz attempt
     * @param {string|number} courseId - Course ID
     * @param {string|number} quizId - Quiz ID
     * @returns {Promise} Quiz attempt data
     */
    startQuizAttempt: async (courseId, quizId) => {
        try {
            const response = await axiosInstance.post(`/api/student/courses/${courseId}/quizzes/${quizId}/start/`);
            return response.data;
        } catch (error) {
            console.error(`Error starting quiz attempt ${quizId}:`, error);
            throw error;
        }
    },

    /**
     * Submit quiz answers
     * @param {string|number} courseId - Course ID
     * @param {string|number} quizId - Quiz ID
     * @param {string|number} attemptId - Attempt ID
     * @param {Object} answers - Quiz answers
     * @returns {Promise} Quiz result
     */
    submitQuizAnswers: async (courseId, quizId, attemptId, answers) => {
        try {
            const response = await axiosInstance.post(
                `/api/student/courses/${courseId}/quizzes/${quizId}/attempts/${attemptId}/submit/`,
                { answers }
            );
            return response.data;
        } catch (error) {
            console.error(`Error submitting quiz answers ${quizId}:`, error);
            throw error;
        }
    },

    /**
     * Get quiz results
     * @param {string|number} courseId - Course ID
     * @param {string|number} quizId - Quiz ID
     * @param {string|number} attemptId - Attempt ID
     * @returns {Promise} Quiz results
     */
    getQuizResults: async (courseId, quizId, attemptId) => {
        try {
            const response = await axiosInstance.get(
                `/api/student/courses/${courseId}/quizzes/${quizId}/attempts/${attemptId}/results/`
            );
            return response.data;
        } catch (error) {
            console.error(`Error fetching quiz results ${attemptId}:`, error);
            throw error;
        }
    },

    // ==================== TURBOWARP ASSIGNMENTS ====================

    /**
     * Get TurboWarp assignment details
     * @param {string|number} courseId - Course ID
     * @param {string|number} assignmentId - Assignment ID
     * @returns {Promise} Assignment details
     */
    getTurboWarpAssignment: async (courseId, assignmentId) => {
        try {
            const response = await axiosInstance.get(`/api/student/courses/${courseId}/assignments/${assignmentId}/`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching TurboWarp assignment ${assignmentId}:`, error);
            throw error;
        }
    },

    /**
     * Submit TurboWarp project
     * @param {string|number} courseId - Course ID
     * @param {string|number} assignmentId - Assignment ID
     * @param {Object} submissionData - Submission data (project_url, description, etc.)
     * @returns {Promise} Submission result
     */
    submitTurboWarpProject: async (courseId, assignmentId, submissionData) => {
        try {
            const response = await axiosInstance.post(
                `/api/student/courses/${courseId}/assignments/${assignmentId}/submit/`,
                submissionData
            );
            return response.data;
        } catch (error) {
            console.error(`Error submitting TurboWarp project ${assignmentId}:`, error);
            throw error;
        }
    },

    /**
     * Get TurboWarp submission status
     * @param {string|number} courseId - Course ID
     * @param {string|number} assignmentId - Assignment ID
     * @returns {Promise} Submission status
     */
    getTurboWarpSubmissionStatus: async (courseId, assignmentId) => {
        try {
            const response = await axiosInstance.get(
                `/api/student/courses/${courseId}/assignments/${assignmentId}/submission/`
            );
            return response.data;
        } catch (error) {
            console.error(`Error fetching TurboWarp submission status ${assignmentId}:`, error);
            throw error;
        }
    },

    // ==================== DASHBOARD & ANALYTICS ====================

    /**
     * Get student dashboard data
     * @returns {Promise} Dashboard data
     */
    getDashboardData: async () => {
        try {
            const response = await axiosInstance.get('/api/student/dashboard/');
            return response.data;
        } catch (error) {
            console.error('Error fetching student dashboard data:', error);
            throw error;
        }
    },

    /**
     * Get learning statistics
     * @returns {Promise} Learning statistics
     */
    getLearningStats: async () => {
        try {
            const response = await axiosInstance.get('/api/student/stats/');
            return response.data;
        } catch (error) {
            console.error('Error fetching learning stats:', error);
            throw error;
        }
    },

    /**
     * Get achievements and badges
     * @returns {Promise} Achievements data
     */
    getAchievements: async () => {
        try {
            const response = await axiosInstance.get('/api/student/achievements/');
            return response.data;
        } catch (error) {
            console.error('Error fetching achievements:', error);
            throw error;
        }
    },

    // ==================== PAYMENT & BILLING ====================

    /**
     * Get payment history
     * @returns {Promise} Payment history
     */
    getPaymentHistory: async () => {
        try {
            const response = await axiosInstance.get('/api/student/payments/');
            return response.data;
        } catch (error) {
            console.error('Error fetching payment history:', error);
            throw error;
        }
    },

    /**
     * Process payment for course
     * @param {Object} paymentData - Payment data
     * @returns {Promise} Payment result
     */
    processPayment: async (paymentData) => {
        try {
            const response = await axiosInstance.post('/api/student/payments/process/', paymentData);
            return response.data;
        } catch (error) {
            console.error('Error processing payment:', error);
            throw error;
        }
    },

    /**
     * Verify payment status
     * @param {string} paymentId - Payment ID
     * @returns {Promise} Payment status
     */
    verifyPayment: async (paymentId) => {
        try {
            const response = await axiosInstance.get(`/api/student/payments/${paymentId}/verify/`);
            return response.data;
        } catch (error) {
            console.error(`Error verifying payment ${paymentId}:`, error);
            throw error;
        }
    },

    // ==================== PROFILE & SETTINGS ====================

    /**
     * Get student profile
     * @returns {Promise} Student profile data
     */
    getProfile: async () => {
        try {
            const response = await axiosInstance.get('/api/student/profile/');
            return response.data;
        } catch (error) {
            console.error('Error fetching student profile:', error);
            throw error;
        }
    },

    /**
     * Update student profile
     * @param {Object} profileData - Profile data
     * @returns {Promise} Updated profile
     */
    updateProfile: async (profileData) => {
        try {
            const response = await axiosInstance.put('/api/student/profile/', profileData);
            return response.data;
        } catch (error) {
            console.error('Error updating student profile:', error);
            throw error;
        }
    },

    /**
     * Get learning preferences
     * @returns {Promise} Learning preferences
     */
    getLearningPreferences: async () => {
        try {
            const response = await axiosInstance.get('/api/student/preferences/');
            return response.data;
        } catch (error) {
            console.error('Error fetching learning preferences:', error);
            throw error;
        }
    },

    /**
     * Update learning preferences
     * @param {Object} preferences - Learning preferences
     * @returns {Promise} Updated preferences
     */
    updateLearningPreferences: async (preferences) => {
        try {
            const response = await axiosInstance.put('/api/student/preferences/', preferences);
            return response.data;
        } catch (error) {
            console.error('Error updating learning preferences:', error);
            throw error;
        }
    }
};

export default studentAPI;
