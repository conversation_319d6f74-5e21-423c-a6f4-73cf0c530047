/**
 * API Services Index
 * Central export point for all API services
 */

// Core services
export { default as axiosInstance } from './axiosInstance';
export { default as authService } from './authService';

// Academy services (offline)
export { default as teacherAPI } from './teacherAPI';
export { default as studentAPI } from './studentAPI';

// E-learning services (online)
export { default as elearningAPI } from './elearningAPI';

// API endpoints configuration
export const API_ENDPOINTS = {
    // Authentication
    AUTH: {
        LOGIN: '/api/account/login/',
        LOGOUT: '/api/account/logout/',
        REGISTER: '/api/account/register/',
        REFRESH_TOKEN: '/api/account/token/refresh/',
        VERIFY_EMAIL: '/api/account/verify-email/',
        RESET_PASSWORD: '/api/account/reset-password/',
        CHANGE_PASSWORD: '/api/account/change-password/',
    },

    // Teacher endpoints
    TEACHER: {
        // Course management
        COURSES: '/api/teacher/courses/',
        COURSE_DETAIL: (id) => `/api/teacher/courses/${id}/`,
        COURSE_PUBLISH: (id) => `/api/teacher/courses/${id}/publish/`,
        COURSE_CONTENT: (id) => `/api/teacher/courses/${id}/content/`,
        COURSE_CONTENT_DETAIL: (courseId, contentId) => `/api/teacher/courses/${courseId}/content/${contentId}/`,

        // Student management
        COURSE_STUDENTS: (id) => `/api/teacher/courses/${id}/students/`,
        STUDENT_PROGRESS: (courseId, studentId) => `/api/teacher/courses/${courseId}/students/${studentId}/progress/`,
        ASSIGN_COURSE: (id) => `/api/teacher/courses/${id}/assign/`,

        // Quiz & Assignment management
        QUIZ_SUBMISSIONS: (courseId, quizId) => `/api/teacher/courses/${courseId}/quizzes/${quizId}/submissions/`,
        GRADE_QUIZ: (courseId, quizId, submissionId) => `/api/teacher/courses/${courseId}/quizzes/${quizId}/submissions/${submissionId}/grade/`,
        TURBOWARP_SUBMISSIONS: (courseId, assignmentId) => `/api/teacher/courses/${courseId}/assignments/${assignmentId}/submissions/`,
        GRADE_TURBOWARP: (courseId, assignmentId, submissionId) => `/api/teacher/courses/${courseId}/assignments/${assignmentId}/submissions/${submissionId}/grade/`,

        // Analytics
        COURSE_ANALYTICS: (id) => `/api/teacher/courses/${id}/analytics/`,
        DASHBOARD_STATS: '/api/teacher/dashboard/stats/',
        EXPORT_COURSE: (id) => `/api/teacher/courses/${id}/export/`,
    },

    // Student endpoints
    STUDENT: {
        // Course discovery & enrollment
        AVAILABLE_COURSES: '/api/student/courses/available/',
        ENROLLED_COURSES: '/api/student/courses/enrolled/',
        COURSE_DETAIL: (id) => `/api/student/courses/${id}/`,
        PURCHASE_COURSE: (id) => `/api/student/courses/${id}/purchase/`,
        COURSE_CONTENT: (id) => `/api/student/courses/${id}/content/`,

        // Learning progress
        COURSE_PROGRESS: (id) => `/api/student/courses/${id}/progress/`,
        LESSON_COMPLETE: (courseId, lessonId) => `/api/student/courses/${courseId}/lessons/${lessonId}/complete/`,
        LESSON_PROGRESS: (courseId, lessonId) => `/api/student/courses/${courseId}/lessons/${lessonId}/progress/`,
        COURSE_COMPLETE: (id) => `/api/student/courses/${id}/complete/`,

        // Quiz management
        QUIZ_DETAIL: (courseId, quizId) => `/api/student/courses/${courseId}/quizzes/${quizId}/`,
        START_QUIZ: (courseId, quizId) => `/api/student/courses/${courseId}/quizzes/${quizId}/start/`,
        SUBMIT_QUIZ: (courseId, quizId, attemptId) => `/api/student/courses/${courseId}/quizzes/${quizId}/attempts/${attemptId}/submit/`,
        QUIZ_RESULTS: (courseId, quizId, attemptId) => `/api/student/courses/${courseId}/quizzes/${quizId}/attempts/${attemptId}/results/`,

        // TurboWarp assignments
        TURBOWARP_ASSIGNMENT: (courseId, assignmentId) => `/api/student/courses/${courseId}/assignments/${assignmentId}/`,
        SUBMIT_TURBOWARP: (courseId, assignmentId) => `/api/student/courses/${courseId}/assignments/${assignmentId}/submit/`,
        TURBOWARP_SUBMISSION: (courseId, assignmentId) => `/api/student/courses/${courseId}/assignments/${assignmentId}/submission/`,

        // Dashboard & analytics
        DASHBOARD: '/api/student/dashboard/',
        LEARNING_STATS: '/api/student/stats/',
        ACHIEVEMENTS: '/api/student/achievements/',

        // Payment & billing
        PAYMENTS: '/api/student/payments/',
        PROCESS_PAYMENT: '/api/student/payments/process/',
        VERIFY_PAYMENT: (id) => `/api/student/payments/${id}/verify/`,

        // Profile & settings
        PROFILE: '/api/student/profile/',
        PREFERENCES: '/api/student/preferences/',
    },

    // Common endpoints
    COMMON: {
        UPLOAD_FILE: '/api/common/upload/',
        DOWNLOAD_FILE: (id) => `/api/common/files/${id}/download/`,
        SUBJECTS: '/api/common/subjects/',
        GRADES: '/api/common/grades/',
        DIFFICULTIES: '/api/common/difficulties/',
    }
};

// HTTP status codes
export const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    INTERNAL_SERVER_ERROR: 500,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504,
};

// API response status
export const API_STATUS = {
    SUCCESS: 'success',
    ERROR: 'error',
    LOADING: 'loading',
    IDLE: 'idle',
};

// Error messages
export const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet.',
    SERVER_ERROR: 'Lỗi máy chủ. Vui lòng thử lại sau.',
    UNAUTHORIZED: 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.',
    FORBIDDEN: 'Bạn không có quyền truy cập tính năng này.',
    NOT_FOUND: 'Không tìm thấy dữ liệu yêu cầu.',
    VALIDATION_ERROR: 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại.',
    PAYMENT_FAILED: 'Thanh toán thất bại. Vui lòng thử lại.',
    COURSE_NOT_AVAILABLE: 'Khóa học không khả dụng.',
    ENROLLMENT_FAILED: 'Đăng ký khóa học thất bại.',
    SUBMISSION_FAILED: 'Nộp bài thất bại. Vui lòng thử lại.',
    QUIZ_TIME_EXPIRED: 'Thời gian làm bài đã hết.',
    FILE_UPLOAD_FAILED: 'Tải file lên thất bại.',
    FILE_TOO_LARGE: 'File quá lớn. Vui lòng chọn file nhỏ hơn.',
    INVALID_FILE_TYPE: 'Loại file không được hỗ trợ.',
};

// Success messages
export const SUCCESS_MESSAGES = {
    COURSE_CREATED: 'Tạo khóa học thành công!',
    COURSE_UPDATED: 'Cập nhật khóa học thành công!',
    COURSE_DELETED: 'Xóa khóa học thành công!',
    COURSE_PUBLISHED: 'Xuất bản khóa học thành công!',
    COURSE_UNPUBLISHED: 'Hủy xuất bản khóa học thành công!',
    COURSE_PURCHASED: 'Mua khóa học thành công!',
    COURSE_ENROLLED: 'Đăng ký khóa học thành công!',
    LESSON_COMPLETED: 'Hoàn thành bài học!',
    QUIZ_SUBMITTED: 'Nộp bài kiểm tra thành công!',
    ASSIGNMENT_SUBMITTED: 'Nộp bài tập thành công!',
    PAYMENT_SUCCESS: 'Thanh toán thành công!',
    PROFILE_UPDATED: 'Cập nhật hồ sơ thành công!',
    PREFERENCES_UPDATED: 'Cập nhật tùy chọn thành công!',
    FILE_UPLOADED: 'Tải file lên thành công!',
    GRADE_SUBMITTED: 'Chấm điểm thành công!',
};

// File upload configuration
export const FILE_CONFIG = {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: {
        IMAGE: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        VIDEO: ['video/mp4', 'video/webm', 'video/ogg'],
        DOCUMENT: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        ARCHIVE: ['application/zip', 'application/x-rar-compressed'],
        SCRATCH: ['application/json', '.sb3'],
    },
    UPLOAD_PATHS: {
        COURSE_THUMBNAILS: '/uploads/courses/thumbnails/',
        LESSON_CONTENT: '/uploads/lessons/content/',
        ASSIGNMENT_FILES: '/uploads/assignments/',
        SUBMISSION_FILES: '/uploads/submissions/',
        PROFILE_AVATARS: '/uploads/profiles/avatars/',
    }
};

// Pagination configuration
export const PAGINATION = {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
};

// Cache configuration
export const CACHE_CONFIG = {
    TTL: {
        SHORT: 5 * 60 * 1000, // 5 minutes
        MEDIUM: 30 * 60 * 1000, // 30 minutes
        LONG: 24 * 60 * 60 * 1000, // 24 hours
    },
    KEYS: {
        USER_PROFILE: 'user_profile',
        ENROLLED_COURSES: 'enrolled_courses',
        COURSE_CONTENT: (id) => `course_content_${id}`,
        DASHBOARD_STATS: 'dashboard_stats',
    }
};

// Export utility functions
export const apiUtils = {
    /**
     * Format error message from API response
     * @param {Object} error - Error object
     * @returns {string} Formatted error message
     */
    formatErrorMessage: (error) => {
        if (error.response?.data?.message) {
            return error.response.data.message;
        }
        if (error.response?.data?.detail) {
            return error.response.data.detail;
        }
        if (error.message) {
            return error.message;
        }
        return ERROR_MESSAGES.SERVER_ERROR;
    },

    /**
     * Check if error is network related
     * @param {Object} error - Error object
     * @returns {boolean} True if network error
     */
    isNetworkError: (error) => {
        return !error.response || error.code === 'ECONNABORTED';
    },

    /**
     * Check if error is authentication related
     * @param {Object} error - Error object
     * @returns {boolean} True if auth error
     */
    isAuthError: (error) => {
        return error.response?.status === 401;
    },

    /**
     * Build query string from object
     * @param {Object} params - Query parameters
     * @returns {string} Query string
     */
    buildQueryString: (params) => {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                searchParams.append(key, value);
            }
        });
        return searchParams.toString();
    },

    /**
     * Format file size
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted file size
     */
    formatFileSize: (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
};
