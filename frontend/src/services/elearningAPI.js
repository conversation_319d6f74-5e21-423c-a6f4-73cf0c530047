import axiosInstance from './axiosInstance';

// ==================== COMMON APIs ====================

export const getSubjects = async () => {
	try {
		const response = await axiosInstance.get('/api/elearning/subjects/');
		return response.data;
	} catch (error) {
		console.error('Error fetching subjects:', error);
		// Return mock data as fallback
		return [];
	}
};

export const getGrades = async () => {
	try {
		const response = await axiosInstance.get('/api/elearning/grades/');
		return response.data;
	} catch (error) {
		console.error('Error fetching grades:', error);
		// Return mock data as fallback
		return [
			{ id: 1, name: 'Lớp 6', order: 6 },
			{ id: 2, name: 'Lớp 7', order: 7 },
			{ id: 3, name: 'Lớp 8', order: 8 },
			{ id: 4, name: 'Lớp 9', order: 9 },
			{ id: 5, name: 'Lớp 10', order: 10 },
			{ id: 6, name: 'Lớp 11', order: 11 },
			{ id: 7, name: 'Lớp 12', order: 12 },
			{ id: 8, name: 'Tất cả', order: 0 }
		];
	}
};

// ==================== TEACHER APIs ====================

export const teacherAPI = {
	// Dashboard
	getDashboard: async () => {
		try {
			const response = await axiosInstance.get('/api/elearning/teacher/dashboard/');
			return response.data;
		} catch (error) {
			console.error('Error fetching teacher dashboard:', error);
			throw error;
		}
	},

	// Course Management
	getCourses: async () => {
		try {
			const response = await axiosInstance.get('/api/elearning/teacher/courses/');
			return response.data;
		} catch (error) {
			console.error('Error fetching teacher courses:', error);
			throw error;
		}
	},

	createCourse: async (courseData) => {
		try {
			// Check if courseData is FormData (for file uploads)
			const config = {};
			if (courseData instanceof FormData) {
				// Remove Content-Type header to let browser set it automatically with boundary
				config.headers = {
					'Content-Type': undefined
				};
			}

			const response = await axiosInstance.post('/api/elearning/teacher/courses/', courseData, config);
			return response.data;
		} catch (error) {
			console.error('Error creating course:', error);
			throw error;
		}
	},

	getCourse: async (courseId) => {
		try {
			const response = await axiosInstance.get(`/api/elearning/teacher/courses/${courseId}/`);
			return response.data;
		} catch (error) {
			console.error('Error fetching course:', error);
			throw error;
		}
	},

	updateCourse: async (courseId, courseData) => {
		try {
			// Check if courseData is FormData (for file uploads)
			const config = {};
			if (courseData instanceof FormData) {
				// Remove Content-Type header to let browser set it automatically with boundary
				config.headers = {
					'Content-Type': undefined
				};
			}

			const response = await axiosInstance.put(`/api/elearning/teacher/courses/${courseId}/`, courseData, config);
			return response.data;
		} catch (error) {
			console.error('Error updating course:', error);
			throw error;
		}
	},

	deleteCourse: async (courseId) => {
		try {
			await axiosInstance.delete(`/api/elearning/teacher/courses/${courseId}/`);
			return true;
		} catch (error) {
			console.error('Error deleting course:', error);
			throw error;
		}
	},

	publishCourse: async (courseId, isPublished) => {
		try {
			const response = await axiosInstance.patch(`/api/elearning/teacher/courses/${courseId}/publish/`, {
				is_published: isPublished
			});
			return response.data;
		} catch (error) {
			console.error('Error publishing course:', error);
			throw error;
		}
	},

	// Course Content
	getCourseContent: async (courseId) => {
		try {
			const response = await axiosInstance.get(`/api/elearning/teacher/courses/${courseId}/content/`);
			return response.data;
		} catch (error) {
			console.error('Error fetching course content:', error);
			throw error;
		}
	},

	// Lesson Management
	createLesson: async (courseId, lessonData) => {
		try {
			const response = await axiosInstance.post(`/api/elearning/teacher/courses/${courseId}/lessons/`, lessonData);
			return response.data;
		} catch (error) {
			console.error('Error creating lesson:', error);
			throw error;
		}
	},

	updateLesson: async (courseId, lessonId, lessonData) => {
		try {
			const response = await axiosInstance.put(`/api/elearning/teacher/courses/${courseId}/lessons/${lessonId}/`, lessonData);
			return response.data;
		} catch (error) {
			console.error('Error updating lesson:', error);
			throw error;
		}
	},

	deleteLesson: async (courseId, lessonId) => {
		try {
			await axiosInstance.delete(`/api/elearning/teacher/courses/${courseId}/lessons/${lessonId}/`);
			return true;
		} catch (error) {
			console.error('Error deleting lesson:', error);
			throw error;
		}
	},

	// Quiz management
	createQuiz: async (courseId, quizData) => {
		try {
			const response = await axiosInstance.post(`/api/elearning/teacher/courses/${courseId}/quizzes/`, quizData);
			return response.data;
		} catch (error) {
			console.error('Error creating quiz:', error);
			throw error;
		}
	},

	updateQuiz: async (courseId, quizId, quizData) => {
		try {
			const response = await axiosInstance.put(`/api/elearning/teacher/courses/${courseId}/quizzes/${quizId}/`, quizData);
			return response.data;
		} catch (error) {
			console.error('Error updating quiz:', error);
			throw error;
		}
	},

	deleteQuiz: async (courseId, quizId) => {
		try {
			await axiosInstance.delete(`/api/elearning/teacher/courses/${courseId}/quizzes/${quizId}/`);
			return true;
		} catch (error) {
			console.error('Error deleting quiz:', error);
			throw error;
		}
	},

	// Assignment management
	createAssignment: async (courseId, assignmentData) => {
		try {
			const response = await axiosInstance.post(`/api/elearning/teacher/courses/${courseId}/assignments/`, assignmentData);
			return response.data;
		} catch (error) {
			console.error('Error creating assignment:', error);
			throw error;
		}
	},

	updateAssignment: async (courseId, assignmentId, assignmentData) => {
		try {
			const response = await axiosInstance.put(`/api/elearning/teacher/courses/${courseId}/assignments/${assignmentId}/`, assignmentData);
			return response.data;
		} catch (error) {
			console.error('Error updating assignment:', error);
			throw error;
		}
	},

	deleteAssignment: async (courseId, assignmentId) => {
		try {
			await axiosInstance.delete(`/api/elearning/teacher/courses/${courseId}/assignments/${assignmentId}/`);
			return true;
		} catch (error) {
			console.error('Error deleting assignment:', error);
			throw error;
		}
	},

	// Question bank
	getQuestions: async (filters = {}) => {
		try {
			const params = new URLSearchParams();
			if (filters.subject) params.append('subject', filters.subject);
			if (filters.grade) params.append('grade', filters.grade);
			if (filters.difficulty) params.append('difficulty', filters.difficulty);
			if (filters.type) params.append('type', filters.type);

			const url = `/api/elearning/teacher/questions/${params.toString() ? '?' + params.toString() : ''}`;
			const response = await axiosInstance.get(url);
			return response.data;
		} catch (error) {
			console.error('Error fetching questions:', error);
			throw error;
		}
	},

	createQuestion: async (questionData) => {
		try {
			const response = await axiosInstance.post('/api/elearning/teacher/questions/', questionData);
			return response.data;
		} catch (error) {
			console.error('Error creating question:', error);
			throw error;
		}
	},

	updateQuestion: async (questionId, questionData) => {
		try {
			const response = await axiosInstance.put(`/api/elearning/teacher/questions/${questionId}/`, questionData);
			return response.data;
		} catch (error) {
			console.error('Error updating question:', error);
			throw error;
		}
	},

	deleteQuestion: async (questionId) => {
		try {
			await axiosInstance.delete(`/api/elearning/teacher/questions/${questionId}/`);
		} catch (error) {
			console.error('Error deleting question:', error);
			throw error;
		}
	},

	getQuestion: async (questionId) => {
		try {
			const response = await axiosInstance.get(`/api/elearning/teacher/questions/${questionId}/`);
			return response.data;
		} catch (error) {
			console.error('Error fetching question:', error);
			throw error;
		}
	},

	// Get subjects and grades for dropdowns (teacher-specific endpoints)
	getSubjects: async () => {
		try {
			const response = await axiosInstance.get('/api/elearning/teacher/subjects/');
			return response.data;
		} catch (error) {
			console.error('Error fetching teacher subjects:', error);
			// Fallback to common subjects endpoint
			try {
				const response = await axiosInstance.get('/api/elearning/subjects/');
				return response.data;
			} catch (fallbackError) {
				console.error('Error fetching subjects fallback:', fallbackError);
				return [];
			}
		}
	},

	getGrades: async () => {
		try {
			const response = await axiosInstance.get('/api/elearning/teacher/grades/');
			return response.data;
		} catch (error) {
			console.error('Error fetching teacher grades:', error);
			// Fallback to common grades endpoint
			try {
				const response = await axiosInstance.get('/api/elearning/grades/');
				return response.data;
			} catch (fallbackError) {
				console.error('Error fetching grades fallback:', fallbackError);
				return [
					{ id: 1, name: 'Lớp 6', order: 6 },
					{ id: 2, name: 'Lớp 7', order: 7 },
					{ id: 3, name: 'Lớp 8', order: 8 },
					{ id: 4, name: 'Lớp 9', order: 9 },
					{ id: 5, name: 'Lớp 10', order: 10 },
					{ id: 6, name: 'Lớp 11', order: 11 },
					{ id: 7, name: 'Lớp 12', order: 12 },
					{ id: 8, name: 'Tất cả', order: 0 }
				];
			}
		}
	}
};

// ==================== STUDENT APIs ====================

export const studentAPI = {
	// Dashboard
	getDashboard: async () => {
		try {
			const response = await axiosInstance.get('/api/elearning/student/dashboard/');
			return response.data;
		} catch (error) {
			console.error('Error fetching student dashboard:', error);
			throw error;
		}
	},

	// Course Discovery
	getAvailableCourses: async (filters = {}) => {
		try {
			const params = new URLSearchParams();
			Object.keys(filters).forEach(key => {
				if (filters[key]) {
					params.append(key, filters[key]);
				}
			});

			const response = await axiosInstance.get(`/api/elearning/student/courses/available/?${params}`);
			return response.data;
		} catch (error) {
			console.error('Error fetching available courses:', error);
			// Return mock data with backend-like structure
			return {
				count: 0,
				next: null,
				previous: null,
				results: []
			};
		}
	},

	getEnrolledCourses: async () => {
		try {
			const response = await axiosInstance.get('/api/elearning/student/courses/enrolled/');
			return response.data;
		} catch (error) {
			console.error('Error fetching enrolled courses:', error);
			throw error;
		}
	},

	getCourse: async (courseId) => {
		try {
			const response = await axiosInstance.get(`/api/elearning/student/courses/${courseId}/`);
			return response.data;
		} catch (error) {
			console.error('Error fetching course:', error);
			return {};
		}
	},

	// Course Enrollment
	purchaseCourse: async (courseId, paymentData) => {
		try {
			const response = await axiosInstance.post(`/api/elearning/student/courses/${courseId}/purchase/`, paymentData);
			return response.data;
		} catch (error) {
			console.error('Error purchasing course:', error);
			// Return mock success response as fallback
			return {
				success: true,
				message: paymentData.payment_method === 'free'
					? 'Đăng ký khóa học miễn phí thành công!'
					: 'Mua khóa học thành công!',
				enrollment: {
					id: Math.random().toString(36).substring(2, 11),
					course_id: courseId,
					enrolled_at: new Date().toISOString(),
					payment_method: paymentData.payment_method,
					status: 'active'
				}
			};
		}
	},

	// Enrolled Courses
	getEnrolledCourses: async () => {
		try {
			const response = await axiosInstance.get('/api/elearning/student/courses/enrolled/');
			return response.data;
		} catch (error) {
			console.error('Error fetching enrolled courses:', error);
			return [];
		}
	},

	// Course Content Access
	getCourseContent: async (courseId) => {
		try {
			const response = await axiosInstance.get(`/api/elearning/student/courses/${courseId}/content/`);
			return response.data;
		} catch (error) {
			console.error('Error fetching course content:', error);
			// Return empty structure instead of throwing
			return {
				course: { id: courseId, title: 'Unknown Course' },
				lessons: [],
				quizzes: [],
				assignments: []
			};
		}
	},



	// ==================== PROGRESS TRACKING ====================

	// Get course progress
	getCourseProgress: async (courseId) => {
		try {
			const response = await axiosInstance.get(`/api/elearning/student/courses/${courseId}/progress/`);
			return response.data;
		} catch (error) {
			console.error('Error fetching course progress:', error);
			console.error('Error details:', {
				status: error.response?.status,
				statusText: error.response?.statusText,
				data: error.response?.data,
				message: error.message
			});

			// Return empty progress structure for now
			// TODO: Remove this fallback when backend is ready
			const fallbackData = {
				course: { id: parseInt(courseId), title: 'STEM Course' },
				lesson_progress: [],
				quiz_attempts: [],
				enrollment: { id: 1, course_id: parseInt(courseId), status: 'active' }
			};

			console.error('Returning fallback progress data:', fallbackData);
			return fallbackData;
		}
	},

	// Update lesson progress
	updateLessonProgress: async (courseId, lessonId, progressData) => {
		try {
			const response = await axiosInstance.patch(
				`/api/elearning/student/courses/${courseId}/lessons/${lessonId}/progress/`,
				progressData
			);
			return response.data;
		} catch (error) {
			console.error('Error updating lesson progress:', error);
			throw error;
		}
	},

	// Start quiz attempt
	startQuizAttempt: async (courseId, quizId) => {
		try {
			const response = await axiosInstance.post(
				`/api/elearning/student/courses/${courseId}/quizzes/${quizId}/attempt/`
			);
			return response.data;
		} catch (error) {
			console.error('Error starting quiz attempt:', error);
			throw error;
		}
	},

	// Submit quiz answers
	submitQuizAnswers: async (courseId, quizId, attemptId, answersData) => {
		try {
			const response = await axiosInstance.post(
				`/api/elearning/student/courses/${courseId}/quizzes/${quizId}/attempt/${attemptId}/submit/`,
				answersData
			);
			return response.data;
		} catch (error) {
			console.error('Error submitting quiz answers:', error);
			throw error;
		}
	},

	// ==================== ASSIGNMENT SUBMISSION ====================

	// Submit assignment
	submitAssignment: async (courseId, assignmentId, formData) => {
		try {
			const response = await axiosInstance.post(
				`/api/elearning/student/courses/${courseId}/assignments/${assignmentId}/submit/`,
				formData,
				{
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				}
			);
			return response.data;
		} catch (error) {
			console.error('Error submitting assignment:', error);
			throw error;
		}
	},

	// Get assignment submissions
	getAssignmentSubmissions: async (courseId, assignmentId) => {
		try {
			const response = await axiosInstance.get(
				`/api/elearning/student/courses/${courseId}/assignments/${assignmentId}/submissions/`
			);
			return response.data;
		} catch (error) {
			console.error('Error fetching assignment submissions:', error);
			return [];
		}
	}
};

// ==================== UTILITY FUNCTIONS ====================

export const formatPrice = (price) => {
	if (price === 0) return 'Miễn phí';
	return new Intl.NumberFormat('vi-VN', {
		style: 'currency',
		currency: 'VND'
	}).format(price);
};

export const formatDuration = (minutes) => {
	if (minutes < 60) return `${minutes} phút`;
	const hours = Math.floor(minutes / 60);
	const remainingMinutes = minutes % 60;
	if (remainingMinutes === 0) return `${hours} giờ`;
	return `${hours}h ${remainingMinutes}m`;
};

export const getDifficultyLabel = (difficulty) => {
	const labels = {
		'beginner': 'Cơ bản',
		'intermediate': 'Trung bình',
		'advanced': 'Nâng cao',
		'expert': 'Chuyên sâu'
	};
	return labels[difficulty] || difficulty;
};

export const getDifficultyColor = (difficulty) => {
	const colors = {
		'beginner': '#4CAF50',
		'intermediate': '#FF9800',
		'advanced': '#F44336',
		'expert': '#9C27B0'
	};
	return colors[difficulty] || '#757575';
};

export default {
	getSubjects,
	getGrades,
	teacherAPI,
	studentAPI,
	formatPrice,
	formatDuration,
	getDifficultyLabel,
	getDifficultyColor
};
