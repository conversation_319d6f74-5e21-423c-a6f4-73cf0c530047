import { createTheme } from '@mui/material/styles';
import { beeColors } from '../components/Common/CustomButton';

// BeE STEM Solutions Theme Configuration
const beeTheme = createTheme({
    palette: {
        primary: {
            main: beeColors.primary.main,
            light: beeColors.primary.light,
            dark: beeColors.primary.dark,
            contrastText: beeColors.primary.contrastText,
        },
        secondary: {
            main: beeColors.secondary.main,
            light: beeColors.secondary.light,
            dark: beeColors.secondary.dark,
            contrastText: beeColors.secondary.contrastText,
        },
        background: {
            default: beeColors.background.main,
            paper: beeColors.background.paper,
        },
        text: {
            primary: beeColors.neutral.main,
            secondary: beeColors.neutral.light,
        },
        divider: `${beeColors.neutral.main}20`,
    },
    typography: {
        fontFamily: '"Inter", "Public Sans", "Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
            fontSize: '2.5rem',
            fontWeight: 700,
            lineHeight: 1.2,
            color: beeColors.neutral.main,
        },
        h2: {
            fontSize: '2rem',
            fontWeight: 600,
            lineHeight: 1.3,
            color: beeColors.neutral.main,
        },
        h3: {
            fontSize: '1.75rem',
            fontWeight: 600,
            lineHeight: 1.3,
            color: beeColors.neutral.main,
        },
        h4: {
            fontSize: '1.5rem',
            fontWeight: 600,
            lineHeight: 1.4,
            color: beeColors.neutral.main,
        },
        h5: {
            fontSize: '1.25rem',
            fontWeight: 600,
            lineHeight: 1.4,
            color: beeColors.neutral.main,
        },
        h6: {
            fontSize: '1.125rem',
            fontWeight: 600,
            lineHeight: 1.4,
            color: beeColors.neutral.main,
        },
        body1: {
            fontSize: '1rem',
            lineHeight: 1.6,
            color: beeColors.neutral.main,
        },
        body2: {
            fontSize: '0.875rem',
            lineHeight: 1.5,
            color: beeColors.neutral.light,
        },
        button: {
            textTransform: 'none',
            fontWeight: 600,
        },
        caption: {
            fontSize: '0.75rem',
            lineHeight: 1.4,
            color: beeColors.neutral.light,
        },
    },
    shape: {
        borderRadius: 12,
    },
    spacing: 8,
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    borderRadius: '12px',
                    textTransform: 'none',
                    fontWeight: 600,
                    padding: '10px 24px',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                        transform: 'translateY(-2px)',
                    },
                },
                contained: {
                    boxShadow: `0 4px 16px ${beeColors.primary.main}40`,
                    '&:hover': {
                        boxShadow: `0 8px 25px ${beeColors.primary.main}40`,
                    },
                },
            },
        },
        MuiCard: {
            styleOverrides: {
                root: {
                    borderRadius: '16px',
                    boxShadow: `0 4px 16px ${beeColors.neutral.main}08`,
                    border: 'none',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 12px 40px ${beeColors.neutral.main}15`,
                    },
                },
            },
        },
        MuiTextField: {
            styleOverrides: {
                root: {
                    '& .MuiOutlinedInput-root': {
                        borderRadius: '12px',
                        transition: 'all 0.3s ease',
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: beeColors.primary.light,
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: beeColors.primary.main,
                            borderWidth: '2px',
                        },
                    },
                },
            },
        },
        MuiChip: {
            styleOverrides: {
                root: {
                    borderRadius: '20px',
                    fontWeight: 500,
                },
                filled: {
                    backgroundColor: beeColors.accent.main,
                    color: beeColors.accent.contrastText,
                    '&:hover': {
                        backgroundColor: beeColors.accent.dark,
                    },
                },
            },
        },
        MuiAppBar: {
            styleOverrides: {
                root: {
                    backgroundColor: beeColors.background.paper,
                    color: beeColors.neutral.main,
                    boxShadow: `0 2px 12px ${beeColors.neutral.main}10`,
                    borderBottom: `1px solid ${beeColors.neutral.main}10`,
                },
            },
        },
        MuiDrawer: {
            styleOverrides: {
                paper: {
                    backgroundColor: beeColors.background.paper,
                    borderRight: `1px solid ${beeColors.neutral.main}10`,
                },
            },
        },
        MuiListItemButton: {
            styleOverrides: {
                root: {
                    borderRadius: '12px',
                    margin: '4px 8px',
                    transition: 'all 0.3s ease',
                    '&.Mui-selected': {
                        backgroundColor: `${beeColors.primary.main}15`,
                        color: beeColors.primary.main,
                        '&:hover': {
                            backgroundColor: `${beeColors.primary.main}20`,
                        },
                        '& .MuiListItemIcon-root': {
                            color: beeColors.primary.main,
                        },
                    },
                    '&:hover': {
                        backgroundColor: `${beeColors.neutral.main}08`,
                    },
                },
            },
        },
        MuiTableHead: {
            styleOverrides: {
                root: {
                    backgroundColor: beeColors.background.main,
                    '& .MuiTableCell-head': {
                        fontWeight: 600,
                        color: beeColors.neutral.main,
                        borderBottom: `2px solid ${beeColors.neutral.main}10`,
                    },
                },
            },
        },
        MuiTableRow: {
            styleOverrides: {
                root: {
                    '&:hover': {
                        backgroundColor: `${beeColors.primary.main}05`,
                    },
                },
            },
        },
        MuiPaper: {
            styleOverrides: {
                root: {
                    borderRadius: '16px',
                },
                elevation1: {
                    boxShadow: `0 4px 16px ${beeColors.neutral.main}08`,
                },
            },
        },
    },
});

// Custom breakpoints for responsive design
beeTheme.breakpoints.values = {
    xs: 0,
    sm: 600,
    md: 960,
    lg: 1280,
    xl: 1920,
};

// Custom mixins for common patterns
beeTheme.mixins = {
    ...beeTheme.mixins,
    gradientBackground: {
        background: beeColors.background.gradient,
        color: 'white',
    },
    glassmorphism: {
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
    },
    centerFlex: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    spaceBetween: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
};

export default beeTheme;
export { beeColors };
