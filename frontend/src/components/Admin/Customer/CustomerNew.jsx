import React from "react";
import AdminLayout from "../Common/AdminLayout2";
import Grid from "@mui/material/Grid2";
import {
    Avatar,
    Box,
    Button,
    Paper,
    Switch,
    Divider,
    Typography,
    styled,
    Checkbox,
    FormControlLabel,
} from "@mui/material";
import { deepPurple, grey } from "@mui/material/colors";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import { useParams } from "react-router-dom";
import axiosInstance from "../../../services/axiosInstance";
import Loading from "../../Common/Loading";
import CustomTextField from "../../Common/CustomTextField";
import CustomButton from "../../Common/CustomButton";
import CheckIcon from "@mui/icons-material/Check";
import AddIcon from "@mui/icons-material/Add";
import BeeNotification from "../../Common/BeeNotification";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";

const VisuallyHiddenInput = styled("input")({
    clip: "rect(0 0 0 0)",
    clipPath: "inset(50%)",
    height: 1,
    overflow: "hidden",
    position: "absolute",
    bottom: 0,
    left: 0,
    whiteSpace: "nowrap",
    width: 1,
});

function CustomerNew({ user }) {
    const { username } = useParams();

    const [openSnackbar, setOpenSnackbar] = React.useState(false);
    const [statusSnackbar, setStatusSnackbar] = React.useState("success");
    const [messageSnackbar, setMessageSnackbar] = React.useState("");

    const [loading, setLoading] = React.useState(true);
    const [targetUser, setTargetUser] = React.useState({
        username: "",
        first_name: "",
        last_name: "",
        phone: "",
        address: "",
        ward: "",
        district: "",
        province: "",
        is_active: true,
        is_supervisor: false,
        is_saler: false,
        is_teacher: false,
        note: "",
        avatar: null,
        avatar_url: null,
    });

    useDocumentTitle(username === null ? "Thêm khách hàng | BeE" : "Chỉnh sửa khách hàng | BeE");

    React.useEffect(() => {
        if (username != null && username != undefined) {
            const fetchData = async () => {
                try {
                    const response = await axiosInstance.get(`/api/admin/user/${username}`);
                    setTargetUser(response.data);
                } catch (err) {
                    console.error(err);
                } finally {
                    setLoading(false);
                }
            };
            fetchData();
        }
    }, []);

    if (loading && username != null && username != undefined) return <Loading />;

    const handleUpdateUser = () => {
        const formData = new FormData();
        formData.append("first_name", targetUser.first_name);
        formData.append("last_name", targetUser.last_name);
        formData.append("phone", targetUser.phone);
        formData.append("address", targetUser.address);
        formData.append("ward", targetUser.ward);
        formData.append("district", targetUser.district);
        formData.append("province", targetUser.province);
        formData.append("about", targetUser.about);

        formData.append("is_active", targetUser.is_active);
        formData.append("is_supervisor", targetUser.is_supervisor);
        formData.append("is_saler", targetUser.is_saler);
        formData.append("is_teacher", targetUser.is_teacher);

        if (targetUser.avatar && targetUser.avatar instanceof File) formData.append("avatar", targetUser.avatar);

        axiosInstance
            .patch(`api/admin/user/${targetUser.username}`, formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            })
            .then((response) => {
                if (response.status === 200) {
                    setStatusSnackbar("success");
                    setMessageSnackbar("Người dùng đã được cập nhật thành công!");
                    setOpenSnackbar(true);
                }
            })
            .catch((error) => {
                setStatusSnackbar("error");
                setMessageSnackbar(error.response.data);
                setOpenSnackbar(true);
            });
    };

    const handleCreateUser = () => {
        const formData = new FormData();
        formData.append("first_name", targetUser.first_name);
        formData.append("last_name", targetUser.last_name);
        formData.append("phone", targetUser.phone);
        formData.append("username", targetUser.email);
        formData.append("email", targetUser.email);
        formData.append("address", targetUser.address);
        formData.append("ward", targetUser.ward);
        formData.append("district", targetUser.district);
        formData.append("province", targetUser.province);
        formData.append("about", targetUser.about);

        if (targetUser.avatar && targetUser.avatar instanceof File) formData.append("avatar", targetUser.avatar);

        axiosInstance
            .post("api/account/profile/", formData, {
                headers: {
                    "Content-Type": "multipart/form-data",
                },
            })
            .then((response) => {
                if (response.status === 200) {
                    setStatusSnackbar("success");
                    setMessageSnackbar("Người dùng đã được cập nhật thành công!");
                    setOpenSnackbar(true);
                }
            })
            .catch((error) => {
                setStatusSnackbar("error");
                setMessageSnackbar(error.response.data);
                setOpenSnackbar(true);
            });
    };

    const handleImageChange = (event) => {
        setTargetUser({
            ...targetUser,
            avatar: event.target.files[0],
            avatar_url: URL.createObjectURL(event.target.files[0]),
        });
    };

    return (
        <AdminLayout title="Thêm khách hàng" user={user}>
            <BeeNotification
                openSnackbar={openSnackbar}
                setOpenSnackbar={setOpenSnackbar}
                status={statusSnackbar}
                message={messageSnackbar}
            />
            <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={2} sx={{ width: "100%" }}>
                    <Grid size={{ xs: 12, md: 4 }}>
                        <Paper
                            sx={{
                                width: "100%",
                                height: "450px",
                                borderRadius: "20px",
                                alignItems: "center",
                                display: "flex",
                                justifyContent: "center",
                                flexDirection: "column",
                            }}
                        >
                            <Box
                                sx={{
                                    alignItems: "center",
                                    display: "flex",
                                    justifyContent: "center",
                                    flexDirection: "column",
                                    mb: "20px",
                                }}
                            >
                                <Box
                                    sx={{
                                        height: "150px",
                                        width: "150px",
                                        border: "1.5px dashed rgb(227, 233, 239)",
                                        borderRadius: "75px",
                                        display: "flex",
                                        justifyContent: "center",
                                        alignItems: "center",
                                    }}
                                >
                                    {targetUser.avatar_url != null ? (
                                        <Avatar
                                            sx={{
                                                width: "120px",
                                                height: "120px",
                                            }}
                                            src={targetUser.avatar_url}
                                            alt={targetUser.username}
                                        />
                                    ) : (
                                        <Avatar
                                            sx={{
                                                width: "120px",
                                                height: "120px",
                                                bgcolor: deepPurple[800],
                                                color: "white",
                                            }}
                                        >
                                            {targetUser.lastname != "" ? targetUser.last_name[0] : "A"}
                                        </Avatar>
                                    )}
                                </Box>
                                <Typography
                                    sx={{
                                        color: grey[600],
                                        fontSize: "13px",
                                        mt: "10px",
                                        mb: "20px",
                                    }}
                                >
                                    *.jpg, *.png, *.webp
                                </Typography>
                                <Button
                                    component="label"
                                    role={undefined}
                                    variant="outlined"
                                    tabIndex={-1}
                                    sx={{
                                        borderRadius: "10px",
                                        textTransform: "none",
                                    }}
                                    startIcon={<FileUploadIcon />}
                                >
                                    Tải ảnh lên
                                    <VisuallyHiddenInput type="file" onChange={handleImageChange} />
                                </Button>
                            </Box>
                            <Box sx={{ width: "100%", mb: "10px" }}>
                                <Typography sx={{ paddingLeft: "30px" }}>Đã xác thực email</Typography>
                            </Box>
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    padding: "0px 30px 0px 30px",
                                    alignItems: "center",
                                }}
                            >
                                <Box sx={{ mr: "10px" }}>
                                    <Typography sx={{ color: grey[600] }}>
                                        Nếu tài khoản khách hàng chưa xác thực thì email tự động sẽ được gửi để xác minh
                                    </Typography>
                                </Box>
                                <Switch
                                    inputProps={{
                                        "aria-label": "Size switch demo",
                                    }}
                                    defaultChecked
                                    size="small"
                                />
                            </Box>
                        </Paper>
                    </Grid>
                    <Grid size={{ xs: 12, md: 8 }}>
                        <Paper
                            sx={{
                                width: "100%",
                                padding: "30px",
                                borderRadius: "20px",
                            }}
                        >
                            <Box sx={{ mb: "20px" }}>
                                <Typography variant="h6" sx={{ fontSize: "20px", fontWeight: 500 }}>
                                    Thông Tin Chung
                                </Typography>
                                <Typography sx={{ fontSize: "15px", color: grey[500] }}>
                                    Họ và tên, SĐT, Email...
                                </Typography>
                            </Box>
                            <Grid container spacing={3}>
                                <Grid size={6}>
                                    <CustomTextField
                                        id="family-name"
                                        label="Họ & Tên Đệm"
                                        variant="outlined"
                                        value={targetUser.first_name}
                                        onChange={(e) => {
                                            setTargetUser({
                                                ...user,
                                                first_name: e.target.value,
                                            });
                                        }}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid size={6}>
                                    <CustomTextField
                                        id="family-name"
                                        label="Tên"
                                        variant="outlined"
                                        value={targetUser.last_name}
                                        onChange={(e) => {
                                            setTargetUser({
                                                ...user,
                                                last_name: e.target.value,
                                            });
                                        }}
                                        fullWidth
                                    />
                                    {/* <Autocomplete
                                    fullWidth
                                    disablePortal
                                    id="user-role-combobox"
                                    value={targetUser.role}
                                    options={role_list}
                                    onChange={(e, value) => { setTargetUser({ ...targetUser, first_name: e.target.value }) }}
                                    renderInput={(params) => <CustomTextField
                                        {...params}
                                        label="Chức năng"
                                    />}
                                /> */}
                                </Grid>
                            </Grid>
                            <Grid container spacing={3} sx={{ mt: "20px", mb: "20px" }}>
                                <Grid size={6}>
                                    <CustomTextField
                                        id="phone"
                                        label="Số điện thoại"
                                        variant="outlined"
                                        value={targetUser.phone}
                                        onChange={(e) => {
                                            setTargetUser({
                                                ...user,
                                                phone: e.target.value,
                                            });
                                        }}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid size={6}>
                                    <CustomTextField
                                        id="email"
                                        label="Email"
                                        variant="outlined"
                                        value={targetUser.email}
                                        onChange={(e) => {
                                            setTargetUser({
                                                ...user,
                                                email: e.target.value,
                                            });
                                        }}
                                        disabled={username != null && username != undefined}
                                        fullWidth
                                    />
                                </Grid>
                            </Grid>
                            <Divider sx={{ mt: "30px" }} />
                            <Box sx={{ mb: "20px", mt: "20px" }}>
                                <Typography variant="h6" sx={{ fontSize: "20px", fontWeight: 500 }}>
                                    Địa Chỉ
                                </Typography>
                                <Typography sx={{ fontSize: "15px", color: grey[500] }}>
                                    Số nhà, quận/huyện, tỉnh...
                                </Typography>
                            </Box>
                            <Typography variant="h6" sx={{ mb: "20px", mt: "20px" }}></Typography>
                            <CustomTextField
                                sx={{ mb: "20px" }}
                                id="address"
                                label="Số nhà, đường"
                                variant="outlined"
                                value={targetUser.address}
                                onChange={(e) => {
                                    setTargetUser({
                                        ...user,
                                        address: e.target.value,
                                    });
                                }}
                                fullWidth
                            />
                            <Grid container spacing={2} sx={{ mb: "10px" }}>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <CustomTextField
                                        id="ward"
                                        label="Phường/Xã"
                                        variant="outlined"
                                        value={targetUser.ward}
                                        onChange={(e) => {
                                            setTargetUser({
                                                ...user,
                                                ward: e.target.value,
                                            });
                                        }}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <CustomTextField
                                        id="district"
                                        label="Quận/Huyện"
                                        variant="outlined"
                                        value={targetUser.district}
                                        onChange={(e) => {
                                            setTargetUser({
                                                ...user,
                                                district: e.target.value,
                                            });
                                        }}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <CustomTextField
                                        id="province"
                                        label="Tỉnh/Thành phố"
                                        variant="outlined"
                                        value={targetUser.province}
                                        onChange={(e) => {
                                            setTargetUser({
                                                ...user,
                                                province: e.target.value,
                                            });
                                        }}
                                        fullWidth
                                    />
                                </Grid>
                            </Grid>
                            <Divider sx={{ mt: "30px" }} />
                            <Box sx={{ mb: "20px", mt: "20px" }}>
                                <Typography variant="h6" sx={{ fontSize: "20px", fontWeight: 500 }}>
                                    Phân Quyền
                                </Typography>
                                <Typography sx={{ fontSize: "15px", color: grey[500] }}>
                                    Đang hoạt động, Quản lý, Bán hàng,...
                                </Typography>
                            </Box>
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    mb: "20px",
                                    flexDirection: "column",
                                }}
                            >
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={targetUser.is_active}
                                            onChange={(e) => {
                                                setTargetUser({
                                                    ...user,
                                                    is_active: e.target.checked,
                                                });
                                            }}
                                        />
                                    }
                                    label="Đang hoạt động"
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={targetUser.is_supervisor}
                                            onChange={(e) => {
                                                setTargetUser({
                                                    ...user,
                                                    is_supervisor: e.target.checked,
                                                });
                                            }}
                                        />
                                    }
                                    label="Quản lý"
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={targetUser.is_saler}
                                            onChange={(e) => {
                                                setTargetUser({
                                                    ...user,
                                                    is_saler: e.target.checked,
                                                });
                                            }}
                                        />
                                    }
                                    label="Bán hàng"
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={targetUser.is_teacher}
                                            onChange={(e) => {
                                                setTargetUser({
                                                    ...user,
                                                    is_teacher: e.target.checked,
                                                });
                                            }}
                                        />
                                    }
                                    label="Giảng viên"
                                />
                            </Box>
                            <Divider />
                            <Box sx={{ mb: "20px", mt: "20px" }}>
                                <Typography variant="h6" sx={{ fontSize: "20px", fontWeight: 500 }}>
                                    Khác
                                </Typography>
                                <Typography sx={{ fontSize: "15px", color: grey[500] }}>Thông tin thêm...</Typography>
                            </Box>
                            <CustomTextField
                                sx={{ mb: "20px" }}
                                id="about"
                                label="Về tôi"
                                multiline
                                rows={4}
                                variant="outlined"
                                value={targetUser.about}
                                onChange={(e) => {
                                    setTargetUser({
                                        ...user,
                                        about: e.target.value,
                                    });
                                }}
                                fullWidth
                            />
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "right",
                                }}
                            >
                                {username != null && username != undefined ? (
                                    <CustomButton onClick={handleUpdateUser} startIcon={<CheckIcon />}>
                                        Cập nhật
                                    </CustomButton>
                                ) : (
                                    <CustomButton onClick={handleCreateUser} startIcon={<AddIcon />}>
                                        Thêm khách hàng
                                    </CustomButton>
                                )}
                            </Box>
                        </Paper>
                    </Grid>
                </Grid>
            </Box>
        </AdminLayout>
    );
}

export default CustomerNew;
