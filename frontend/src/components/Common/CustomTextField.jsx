import TextField from "@mui/material/TextField";
import { styled } from "@mui/material/styles";
import { beeColors } from "./CustomButton";

// Create a styled version of the TextField with BeE STEM design
const CustomTextField = styled(TextField)(({ theme, variant = "outlined" }) => ({
    "& .MuiOutlinedInput-root": {
        borderRadius: "12px",
        backgroundColor: beeColors.background.paper,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: beeColors.primary.light,
            borderWidth: '2px',
        },
        "&.Mui-focused": {
            transform: 'translateY(-2px)',
            boxShadow: `0 8px 25px ${beeColors.primary.main}20`,
            "& .MuiOutlinedInput-notchedOutline": {
                borderColor: beeColors.primary.main,
                borderWidth: '2px',
            },
        },
        "&.Mui-error .MuiOutlinedInput-notchedOutline": {
            borderColor: '#E74C3C',
        },
    },
    "& .MuiInputLabel-root": {
        color: beeColors.neutral.light,
        fontWeight: 500,
        "&.Mui-focused": {
            color: beeColors.primary.main,
        },
        "&.Mui-error": {
            color: '#E74C3C',
        },
    },
    "& .MuiFormHelperText-root": {
        marginLeft: '12px',
        fontSize: '0.75rem',
        "&.Mui-error": {
            color: '#E74C3C',
        },
    },
    "& input[type=number]": {
        MozAppearance: "textfield",
        "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
            WebkitAppearance: "none",
            margin: 0,
        },
    },
    "& .MuiInputBase-input": {
        color: beeColors.neutral.main,
        fontSize: '0.95rem',
        "&::placeholder": {
            color: beeColors.neutral.light,
            opacity: 0.7,
        },
    },
}));

export default CustomTextField;
