import React from 'react';
import { Card, CardContent, CardActions, Box, styled, alpha } from '@mui/material';
import { beeColors } from './CustomButton';

const StyledCard = styled(Card, {
    shouldForwardProp: (prop) => !['variant', 'hover'].includes(prop),
})(({ variant = 'default', hover = true }) => {
    const getCardStyles = () => {
        switch (variant) {
            case 'gradient':
                return {
                    background: beeColors.background.gradient,
                    color: 'white',
                    '& .MuiCardContent-root': {
                        color: 'white'
                    }
                };
            case 'outlined':
                return {
                    backgroundColor: beeColors.background.paper,
                    border: `2px solid ${beeColors.primary.main}`,
                    boxShadow: 'none'
                };
            case 'elevated':
                return {
                    backgroundColor: beeColors.background.paper,
                    boxShadow: `0 8px 32px ${alpha(beeColors.neutral.main, 0.12)}`
                };
            default:
                return {
                    backgroundColor: beeColors.background.paper,
                    boxShadow: `0 4px 16px ${alpha(beeColors.neutral.main, 0.08)}`
                };
        }
    };

    return {
        borderRadius: '16px',
        border: 'none',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        position: 'relative',
        overflow: 'hidden',
        ...getCardStyles(),
        ...(hover && {
            '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: variant === 'gradient'
                    ? `0 12px 40px ${alpha(beeColors.primary.main, 0.3)}`
                    : `0 12px 40px ${alpha(beeColors.neutral.main, 0.15)}`,
                '&::before': {
                    opacity: 1
                }
            }
        }),
        '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: beeColors.background.gradient,
            opacity: variant === 'gradient' ? 0 : 0.7,
            transition: 'opacity 0.3s ease'
        }
    };
});

const BeeCard = ({
    children,
    variant = 'default',
    hover = true,
    actions,
    sx = {},
    ...props
}) => {
    return (
        <StyledCard
            variant={variant}
            hover={hover}
            sx={sx}
            {...props}
        >
            {children}
            {actions && (
                <CardActions sx={{
                    padding: '16px 24px',
                    borderTop: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`
                }}>
                    {actions}
                </CardActions>
            )}
        </StyledCard>
    );
};

// Specialized card variants
export const DashboardCard = ({ icon, title, value, subtitle, trend, ...props }) => (
    <BeeCard variant="elevated" {...props}>
        <CardContent sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box sx={{
                    p: 1.5,
                    borderRadius: '12px',
                    backgroundColor: alpha(beeColors.primary.main, 0.1),
                    color: beeColors.primary.main,
                    mr: 2
                }}>
                    {icon}
                </Box>
                <Box sx={{ flexGrow: 1 }}>
                    <Box sx={{
                        fontSize: '0.875rem',
                        color: beeColors.neutral.light,
                        fontWeight: 500
                    }}>
                        {title}
                    </Box>
                    <Box sx={{
                        fontSize: '1.75rem',
                        fontWeight: 700,
                        color: beeColors.neutral.main,
                        lineHeight: 1.2
                    }}>
                        {value}
                    </Box>
                    {subtitle && (
                        <Box sx={{
                            fontSize: '0.75rem',
                            color: beeColors.neutral.light,
                            mt: 0.5
                        }}>
                            {subtitle}
                        </Box>
                    )}
                </Box>
                {trend && (
                    <Box sx={{
                        fontSize: '0.875rem',
                        fontWeight: 600,
                        color: trend.positive ? beeColors.secondary.main : '#E74C3C'
                    }}>
                        {trend.value}
                    </Box>
                )}
            </Box>
        </CardContent>
    </BeeCard>
);

export const CourseCard = ({
    title,
    description,
    image,
    subject,
    students,
    progress,
    actions,
    ...props
}) => (
    <BeeCard hover {...props}>
        {image && (
            <Box sx={{
                height: 200,
                backgroundImage: `url(${image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                position: 'relative'
            }}>
                {subject && (
                    <Box sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        backgroundColor: alpha(beeColors.accent.main, 0.9),
                        color: beeColors.accent.contrastText,
                        px: 2,
                        py: 0.5,
                        borderRadius: '20px',
                        fontSize: '0.75rem',
                        fontWeight: 600
                    }}>
                        {subject}
                    </Box>
                )}
            </Box>
        )}
        <CardContent sx={{ p: 3 }}>
            <Box sx={{
                fontSize: '1.25rem',
                fontWeight: 600,
                color: beeColors.neutral.main,
                mb: 1,
                lineHeight: 1.3
            }}>
                {title}
            </Box>
            {description && (
                <Box sx={{
                    fontSize: '0.875rem',
                    color: beeColors.neutral.light,
                    mb: 2,
                    lineHeight: 1.5
                }}>
                    {description}
                </Box>
            )}
            {(students || progress) && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    {students && (
                        <Box sx={{
                            fontSize: '0.875rem',
                            color: beeColors.neutral.light
                        }}>
                            {students} học sinh
                        </Box>
                    )}
                    {progress && (
                        <Box sx={{
                            fontSize: '0.875rem',
                            fontWeight: 600,
                            color: beeColors.secondary.main
                        }}>
                            {progress}% hoàn thành
                        </Box>
                    )}
                </Box>
            )}
        </CardContent>
        {actions}
    </BeeCard>
);

export default BeeCard;
