import * as React from "react";
import PropTypes from "prop-types";

import { Navigate, Outlet, useLocation } from "react-router-dom";
import Loading from "./Loading";
import PageNotFound from "../404";

function ProtectedTeacherRoute({ user, loading }) {
    const location = useLocation();

    if (loading) return <Loading />;

    if (!user) {
        // Redirect them to the login page, but save the current location they were trying to go to
        return <Navigate to="/login" state={{ from: location }} />;
    }

    if (!(user.is_admin || user.is_teacher)) {
        return <PageNotFound />;
    }

    return <Outlet />;
}

export default ProtectedTeacherRoute;
