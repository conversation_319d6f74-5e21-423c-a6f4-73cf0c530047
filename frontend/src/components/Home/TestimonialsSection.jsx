import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Box,
    Typography,
    Container,
    Grid,
    Card,
    CardContent,
    Avatar,
    Rating,
    IconButton,
    useTheme,
    Chip
} from '@mui/material';
import {
    FormatQuote as QuoteIcon,
    ArrowBackIos as PrevIcon,
    ArrowForwardIos as NextIcon,
    Star as StarIcon
} from '@mui/icons-material';

const TestimonialsSection = () => {
    const theme = useTheme();
    const [currentTestimonial, setCurrentTestimonial] = useState(0);

    const testimonials = [
        {
            id: 1,
            name: "<PERSON><PERSON><PERSON><PERSON>",
            role: "Học sinh lớp 8",
            avatar: "/api/placeholder/60/60",
            rating: 5,
            content: "Mình đã học được rất nhiều từ khóa học Scratch. Giáo viên dạy rất dễ hiểu và vui vẻ. Bây giờ mình có thể tạo ra những game đơn giản rồi!",
            course: "<PERSON>ậ<PERSON> trình <PERSON>ratch",
            achievement: "<PERSON><PERSON><PERSON> thành 15 dự án"
        },
        {
            id: 2,
            name: "<PERSON>rần Thị Hương",
            role: "Phụ huynh",
            avatar: "/api/placeholder/60/60",
            rating: 5,
            content: "Con tôi rất thích học ở BeE STEM. Từ khi học, con trở nên tự tin hơn và có khả năng tư duy logic tốt hơn. Tôi rất hài lòng với chất lượng giảng dạy.",
            course: "Python cơ bản",
            achievement: "Con học được 3 tháng"
        },
        {
            id: 3,
            name: "Lê Hoàng Nam",
            role: "Học sinh lớp 10",
            avatar: "/api/placeholder/60/60",
            rating: 5,
            content: "Khóa học Arduino thật sự tuyệt vời! Mình đã tạo được nhiều dự án IoT thú vị. Thầy cô hướng dẫn rất tận tình và chi tiết.",
            course: "Arduino & IoT",
            achievement: "Tạo 8 dự án IoT"
        },
        {
            id: 4,
            name: "Phạm Thị Mai",
            role: "Phụ huynh",
            avatar: "/api/placeholder/60/60",
            rating: 5,
            content: "Nền tảng học tập rất hiện đại và dễ sử dụng. Con tôi có thể học mọi lúc mọi nơi. Hệ thống theo dõi tiến độ giúp tôi nắm được quá trình học của con.",
            course: "Toán tư duy",
            achievement: "Điểm số cải thiện 30%"
        },
        {
            id: 5,
            name: "Võ Minh Tuấn",
            role: "Học sinh lớp 9",
            avatar: "/api/placeholder/60/60",
            rating: 5,
            content: "Mình thích cách giảng dạy bằng video và bài tập thực hành. Đặc biệt là phần AI hỗ trợ giúp mình giải đáp thắc mắc ngay lập tức.",
            course: "Python nâng cao",
            achievement: "Top 5 học sinh xuất sắc"
        },
        {
            id: 6,
            name: "Đặng Thị Lan",
            role: "Giáo viên",
            avatar: "/api/placeholder/60/60",
            rating: 5,
            content: "Là một giáo viên, tôi thấy BeE STEM có phương pháp giảng dạy rất hiệu quả. Học sinh tiếp thu kiến thức nhanh và có hứng thú học tập cao.",
            course: "Khóa đào tạo giáo viên",
            achievement: "Đào tạo 50+ học sinh"
        }
    ];

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2
            }
        }
    };

    const itemVariants = {
        hidden: { y: 30, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        }
    };

    const slideVariants = {
        enter: (direction) => ({
            x: direction > 0 ? 300 : -300,
            opacity: 0
        }),
        center: {
            zIndex: 1,
            x: 0,
            opacity: 1
        },
        exit: (direction) => ({
            zIndex: 0,
            x: direction < 0 ? 300 : -300,
            opacity: 0
        })
    };

    const nextTestimonial = () => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    };

    const prevTestimonial = () => {
        setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    };

    const getVisibleTestimonials = () => {
        const visible = [];
        for (let i = 0; i < 3; i++) {
            const index = (currentTestimonial + i) % testimonials.length;
            visible.push(testimonials[index]);
        }
        return visible;
    };

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: '#f8f9fa' }}>
            <Container maxWidth="lg">
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                >
                    {/* Section Header */}
                    <motion.div variants={itemVariants}>
                        <Box sx={{ textAlign: 'center', mb: 8 }}>
                            <Typography
                                variant="h2"
                                sx={{
                                    fontSize: { xs: '2rem', md: '2.5rem' },
                                    fontWeight: 700,
                                    color: theme.palette.text.primary,
                                    mb: 2
                                }}
                            >
                                Học sinh & Phụ huynh
                                <span style={{ color: theme.palette.primary.main }}> nói gì?</span>
                            </Typography>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    maxWidth: '600px',
                                    mx: 'auto',
                                    lineHeight: 1.6
                                }}
                            >
                                Hàng nghìn học sinh và phụ huynh đã tin tưởng và đồng hành cùng BeE STEM Solutions
                            </Typography>
                        </Box>
                    </motion.div>

                    {/* Statistics */}
                    <motion.div variants={itemVariants}>
                        <Grid container spacing={4} sx={{ mb: 6 }}>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                                        4.9
                                    </Typography>
                                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                                        <Rating value={4.9} precision={0.1} readOnly />
                                    </Box>
                                    <Typography variant="body2" color="text.secondary">
                                        Đánh giá trung bình
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.secondary.main }}>
                                        1,200+
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Học sinh hài lòng
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
                                        95%
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Tỷ lệ hoàn thành
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.warning.main }}>
                                        500+
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Phụ huynh tin tưởng
                                    </Typography>
                                </Box>
                            </Grid>
                        </Grid>
                    </motion.div>

                    {/* Main Testimonial Carousel */}
                    <motion.div variants={itemVariants}>
                        <Box sx={{ position: 'relative', mb: 6 }}>
                            {/* Navigation Buttons */}
                            <IconButton
                                onClick={prevTestimonial}
                                sx={{
                                    position: 'absolute',
                                    left: { xs: -20, md: -60 },
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    zIndex: 2,
                                    backgroundColor: 'white',
                                    boxShadow: theme.shadows[4],
                                    '&:hover': {
                                        backgroundColor: theme.palette.primary.main,
                                        color: 'white'
                                    }
                                }}
                            >
                                <PrevIcon />
                            </IconButton>
                            
                            <IconButton
                                onClick={nextTestimonial}
                                sx={{
                                    position: 'absolute',
                                    right: { xs: -20, md: -60 },
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    zIndex: 2,
                                    backgroundColor: 'white',
                                    boxShadow: theme.shadows[4],
                                    '&:hover': {
                                        backgroundColor: theme.palette.primary.main,
                                        color: 'white'
                                    }
                                }}
                            >
                                <NextIcon />
                            </IconButton>

                            {/* Testimonials Grid */}
                            <Grid container spacing={3}>
                                {getVisibleTestimonials().map((testimonial, index) => (
                                    <Grid item xs={12} md={4} key={testimonial.id}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: index * 0.1 }}
                                            whileHover={{ 
                                                scale: 1.03,
                                                transition: { duration: 0.2 }
                                            }}
                                        >
                                            <Card
                                                sx={{
                                                    height: '100%',
                                                    borderRadius: '20px',
                                                    overflow: 'hidden',
                                                    position: 'relative',
                                                    background: index === 1 ? 
                                                        `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})` : 
                                                        'white',
                                                    color: index === 1 ? 'white' : 'inherit',
                                                    transition: 'all 0.3s ease',
                                                    '&:hover': {
                                                        boxShadow: theme.shadows[12],
                                                        transform: 'translateY(-4px)'
                                                    }
                                                }}
                                            >
                                                {/* Quote Icon */}
                                                <Box
                                                    sx={{
                                                        position: 'absolute',
                                                        top: 16,
                                                        right: 16,
                                                        opacity: 0.2
                                                    }}
                                                >
                                                    <QuoteIcon sx={{ fontSize: 40 }} />
                                                </Box>

                                                <CardContent sx={{ p: 4 }}>
                                                    {/* Rating */}
                                                    <Box sx={{ mb: 2 }}>
                                                        <Rating 
                                                            value={testimonial.rating} 
                                                            readOnly 
                                                            sx={{
                                                                '& .MuiRating-iconFilled': {
                                                                    color: index === 1 ? 'white' : theme.palette.warning.main
                                                                }
                                                            }}
                                                        />
                                                    </Box>

                                                    {/* Content */}
                                                    <Typography
                                                        variant="body1"
                                                        sx={{
                                                            mb: 3,
                                                            lineHeight: 1.6,
                                                            fontStyle: 'italic'
                                                        }}
                                                    >
                                                        "{testimonial.content}"
                                                    </Typography>

                                                    {/* User Info */}
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                        <Avatar
                                                            src={testimonial.avatar}
                                                            sx={{ width: 50, height: 50, mr: 2 }}
                                                        />
                                                        <Box>
                                                            <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                                                {testimonial.name}
                                                            </Typography>
                                                            <Typography 
                                                                variant="body2" 
                                                                sx={{ 
                                                                    opacity: index === 1 ? 0.9 : 0.7 
                                                                }}
                                                            >
                                                                {testimonial.role}
                                                            </Typography>
                                                        </Box>
                                                    </Box>

                                                    {/* Course & Achievement */}
                                                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                                        <Chip
                                                            label={testimonial.course}
                                                            size="small"
                                                            sx={{
                                                                backgroundColor: index === 1 ? 
                                                                    'rgba(255, 255, 255, 0.2)' : 
                                                                    theme.palette.primary.main,
                                                                color: index === 1 ? 'white' : 'white',
                                                                fontWeight: 600,
                                                                alignSelf: 'flex-start'
                                                            }}
                                                        />
                                                        <Typography 
                                                            variant="body2" 
                                                            sx={{ 
                                                                opacity: index === 1 ? 0.9 : 0.7,
                                                                fontWeight: 500
                                                            }}
                                                        >
                                                            🏆 {testimonial.achievement}
                                                        </Typography>
                                                    </Box>
                                                </CardContent>
                                            </Card>
                                        </motion.div>
                                    </Grid>
                                ))}
                            </Grid>

                            {/* Dots Indicator */}
                            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, gap: 1 }}>
                                {testimonials.map((_, index) => (
                                    <Box
                                        key={index}
                                        onClick={() => setCurrentTestimonial(index)}
                                        sx={{
                                            width: 12,
                                            height: 12,
                                            borderRadius: '50%',
                                            backgroundColor: index === currentTestimonial ? 
                                                theme.palette.primary.main : 
                                                theme.palette.grey[300],
                                            cursor: 'pointer',
                                            transition: 'all 0.3s ease',
                                            '&:hover': {
                                                backgroundColor: theme.palette.primary.main,
                                                transform: 'scale(1.2)'
                                            }
                                        }}
                                    />
                                ))}
                            </Box>
                        </Box>
                    </motion.div>

                    {/* Call to Action */}
                    <motion.div variants={itemVariants}>
                        <Box
                            sx={{
                                textAlign: 'center',
                                p: { xs: 4, md: 6 },
                                borderRadius: '20px',
                                background: `linear-gradient(135deg, 
                                    ${theme.palette.primary.main}15, 
                                    ${theme.palette.secondary.main}15)`,
                                border: '1px solid',
                                borderColor: `${theme.palette.primary.main}30`
                            }}
                        >
                            <Typography
                                variant="h4"
                                sx={{
                                    fontWeight: 700,
                                    mb: 2,
                                    color: theme.palette.text.primary
                                }}
                            >
                                Bạn cũng muốn trở thành một phần của cộng đồng?
                            </Typography>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    mb: 3
                                }}
                            >
                                Hãy bắt đầu hành trình học tập cùng chúng tôi ngay hôm nay!
                            </Typography>
                        </Box>
                    </motion.div>
                </motion.div>
            </Container>
        </Box>
    );
};

export default TestimonialsSection;
