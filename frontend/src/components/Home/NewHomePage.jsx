import React from "react";
import { motion } from "framer-motion";
import Hero<PERSON><PERSON><PERSON> from "./HeroSection";
import ElearningSection from "./ElearningSection";
import FeaturesSection from "./FeaturesSection";
import TestimonialsSection from "./TestimonialsSection";
import ModernShopping from "./ModernShopping";
import ModernBlog from "./ModernBlog";
import CustomerLayout from "../Common/CustomerLayout";
import axiosInstance from "../../services/axiosInstance";
import Snackbar from "@mui/material/Snackbar";
import Alert from "@mui/material/Alert";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";
import { staggerContainer, fadeInUp } from './AnimationUtils';

function NewHomePage({ user, setUser, cartItem, setCartItem }) {
    const [loading, setLoading] = React.useState(true);
    const [itemList, setItemList] = React.useState([]);
    const [openSnackbar, setOpenSnackbar] = React.useState(false);

    useDocumentTitle("Trang chủ mới | BeE STEM Solutions");

    const handleAddToCart = (item) => {
        setCartItem([...cartItem, item]);
        setOpenSnackbar(true);
    };

    const handleCloseSnackbar = () => {
        setOpenSnackbar(false);
    };

    React.useEffect(() => {
        const getCategory = async () => {
            try {
                const response = await axiosInstance.get("/api/shopping/home/<USER>/");
                setItemList(response.data);
            } catch (err) {
                console.error(JSON.stringify(err));
            } finally {
                setLoading(false);
            }
        };
        getCategory();
    }, []);

    const containerVariants = staggerContainer;
    const itemVariants = fadeInUp;

    return (
        <CustomerLayout user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem}>
            {/* Hero Section - Main landing area with CTA */}
            <HeroSection />

            {/* E-learning Section - Showcase courses and academy */}
            <ElearningSection />

            {/* Features Section - Platform capabilities */}
            <FeaturesSection />

            {/* Shopping Section - Products showcase */}
            <ModernShopping
                loading={loading}
                items={itemList}
                cartItem={cartItem}
                setCartItem={setCartItem}
                handleAddToCart={handleAddToCart}
            />

            {/* Testimonials Section - Social proof */}
            <TestimonialsSection />

            {/* Blog Section - Latest articles */}
            <ModernBlog />

            <Snackbar open={openSnackbar} autoHideDuration={3000} onClose={handleCloseSnackbar}>
                <Alert severity="success" onClose={handleCloseSnackbar}>
                    Thêm vào giỏ hàng thành công!
                </Alert>
            </Snackbar>
        </CustomerLayout>
    );
}

export default NewHomePage;
