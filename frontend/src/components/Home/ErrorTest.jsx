import React from 'react';
import { Box, Typography, Container } from '@mui/material';

function ErrorTest() {
    // Test data to check for object rendering issues
    const testData = {
        category: { id: 1, name: "Test Category", slug: "test-category" },
        subject: { id: 1, name: "Test Subject", color: "#2196F3" },
        price: 299000,
        created_at: "2024-01-15"
    };

    return (
        <Container maxWidth="lg" sx={{ py: 4 }}>
            <Typography variant="h4" gutterBottom>
                Error Test Component
            </Typography>
            
            <Box sx={{ p: 2, mb: 2, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
                <Typography variant="h6">Testing object rendering:</Typography>
                
                {/* Safe rendering */}
                <Typography>Category Name: {testData.category?.name || 'Unknown'}</Typography>
                <Typography>Subject Name: {testData.subject?.name || 'Unknown'}</Typography>
                <Typography>Price: {testData.price ? `${testData.price.toLocaleString()}đ` : 'Free'}</Typography>
                <Typography>Date: {testData.created_at || 'Unknown'}</Typography>
                
                {/* This would cause error if uncommented */}
                {/* <Typography>Category Object: {testData.category}</Typography> */}
                {/* <Typography>Subject Object: {testData.subject}</Typography> */}
            </Box>

            <Typography variant="body1" color="success.main">
                ✅ All object properties are safely rendered as strings
            </Typography>
        </Container>
    );
}

export default ErrorTest;
