import { motion } from "framer-motion";
import React from "react";
// import Project from './Project';
// import Course from './Course';
import Shopping from "./Shopping";
import Carousel from "./Carousel";
import HeroSection from "./HeroSection";
import ElearningSection from "./ElearningSection";
import FeaturesSection from "./FeaturesSection";
import TestimonialsSection from "./TestimonialsSection";
import CustomerLayout from "../Common/CustomerLayout";
import axiosInstance from "../../services/axiosInstance";
import Blog from "./Blog";
import Snackbar from "@mui/material/Snackbar";
import Alert from "@mui/material/Alert";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";

function Home({ user, setUser, cartItem, setCartItem }) {
    const [loading, setLoading] = React.useState(true);
    const [itemList, setItemList] = React.useState([]);
    const [openSnackbar, setOpenSnackbar] = React.useState(false);

    useDocumentTitle("Trang chủ | BeE");

    const handleAddToCart = (item) => {
        setCartItem([...cartItem, item]);
        setOpenSnackbar(true);
    };

    const handleCloseSnackbar = () => {
        setOpenSnackbar(false);
    };

    React.useEffect(() => {
        const getCategory = async () => {
            try {
                const response = await axiosInstance.get("/api/shopping/home/<USER>/");
                setItemList(response.data);
            } catch (err) {
                console.error(JSON.stringify(err));
            } finally {
                setLoading(false);
            }
        };
        getCategory();
    }, []);

    // Animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
        },
    };

    return (
        <CustomerLayout user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem}>
            {/* Hero Section - Main landing area */}
            <HeroSection />

            {/* E-learning Section - Showcase courses and academy */}
            <ElearningSection />

            {/* Features Section - Platform capabilities */}
            <FeaturesSection />

            {/* Original Carousel - Keep for admin-managed banners */}
            <motion.div initial="hidden" animate="visible" variants={containerVariants}>
                <motion.div variants={itemVariants}>
                    <Carousel />
                </motion.div>
            </motion.div>

            {/* Shopping Section - Products showcase */}
            <motion.div initial="hidden" animate="visible" variants={containerVariants}>
                <motion.div variants={itemVariants}>
                    <Shopping
                        loading={loading}
                        items={itemList}
                        cartItem={cartItem}
                        setCartItem={setCartItem}
                        handleAddToCart={handleAddToCart}
                    />
                </motion.div>
            </motion.div>

            {/* Testimonials Section - Social proof */}
            <TestimonialsSection />

            {/* Blog Section - Latest articles */}
            <motion.div initial="hidden" animate="visible" variants={containerVariants}>
                <motion.div variants={itemVariants}>
                    <Blog />
                </motion.div>
            </motion.div>

            <Snackbar open={openSnackbar} autoHideDuration={3000} onClose={handleCloseSnackbar}>
                <Alert severity="success" onClose={handleCloseSnackbar}>
                    Thêm vào giỏ hàng thành công!
                </Alert>
            </Snackbar>
        </CustomerLayout>
    );
}

export default Home;
