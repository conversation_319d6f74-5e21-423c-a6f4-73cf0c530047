import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
    Box,
    Typography,
    Button,
    Container,
    Grid,
    Card,
    CardContent,
    CardMedia,
    CardActions,
    Chip,
    Avatar,
    Rating,
    useTheme,
    Skeleton
} from '@mui/material';
import {
    School as SchoolIcon,
    Person as PersonIcon,
    AccessTime as TimeIcon,
    Star as StarIcon,
    TrendingUp as TrendingIcon,
    PlayCircleOutline as PlayIcon,
    ArrowForward as ArrowIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import elearningAPI from '../../services/elearningAPI';

const ElearningSection = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const [featuredCourses, setFeaturedCourses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [stats, setStats] = useState({
        totalCourses: 0,
        totalStudents: 0,
        totalTeachers: 0,
        completionRate: 0
    });

    useEffect(() => {
        loadData();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            
            // Load featured courses
            try {
                const coursesData = await elearningAPI.studentAPI.getFeaturedCourses();
                setFeaturedCourses(coursesData.slice(0, 3)); // Take first 3 courses
            } catch (error) {
                console.error('Error loading courses:', error);
                // Fallback to mock data
                setFeaturedCourses([
                    {
                        id: 1,
                        title: "Lập trình Scratch cho trẻ em",
                        description: "Học lập trình cơ bản qua Scratch với các dự án thú vị",
                        thumbnail: "/api/placeholder/300/200",
                        difficulty: "beginner",
                        duration: "4 tuần",
                        rating: 4.8,
                        enrolled_count: 245,
                        instructor_name: "Cô Mai",
                        price: 299000,
                        subject: { name: "Lập trình", color: "#2196F3" }
                    },
                    {
                        id: 2,
                        title: "Python cơ bản cho học sinh",
                        description: "Khóa học Python từ cơ bản đến nâng cao",
                        thumbnail: "/api/placeholder/300/200",
                        difficulty: "intermediate",
                        duration: "6 tuần",
                        rating: 4.9,
                        enrolled_count: 189,
                        instructor_name: "Thầy Nam",
                        price: 399000,
                        subject: { name: "Python", color: "#4CAF50" }
                    },
                    {
                        id: 3,
                        title: "Arduino & IoT cho thiếu niên",
                        description: "Tạo các dự án IoT thông minh với Arduino",
                        thumbnail: "/api/placeholder/300/200",
                        difficulty: "advanced",
                        duration: "8 tuần",
                        rating: 4.7,
                        enrolled_count: 156,
                        instructor_name: "Thầy Hùng",
                        price: 499000,
                        subject: { name: "Arduino", color: "#FF9800" }
                    }
                ]);
            }

            // Mock stats data
            setStats({
                totalCourses: 50,
                totalStudents: 1200,
                totalTeachers: 25,
                completionRate: 92
            });

        } catch (error) {
            console.error('Error loading e-learning data:', error);
        } finally {
            setLoading(false);
        }
    };

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2
            }
        }
    };

    const itemVariants = {
        hidden: { y: 30, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        }
    };

    const getDifficultyColor = (difficulty) => {
        switch (difficulty) {
            case 'beginner': return theme.palette.success.main;
            case 'intermediate': return theme.palette.warning.main;
            case 'advanced': return theme.palette.error.main;
            default: return theme.palette.primary.main;
        }
    };

    const getDifficultyText = (difficulty) => {
        switch (difficulty) {
            case 'beginner': return 'Cơ bản';
            case 'intermediate': return 'Trung bình';
            case 'advanced': return 'Nâng cao';
            default: return 'Cơ bản';
        }
    };

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: '#fafafa' }}>
            <Container maxWidth="lg">
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                >
                    {/* Section Header */}
                    <motion.div variants={itemVariants}>
                        <Box sx={{ textAlign: 'center', mb: 6 }}>
                            <Typography
                                variant="h2"
                                sx={{
                                    fontSize: { xs: '2rem', md: '2.5rem' },
                                    fontWeight: 700,
                                    color: theme.palette.text.primary,
                                    mb: 2
                                }}
                            >
                                Nền tảng E-Learning
                                <span style={{ color: theme.palette.primary.main }}> hàng đầu</span>
                            </Typography>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    maxWidth: '600px',
                                    mx: 'auto',
                                    mb: 4
                                }}
                            >
                                Khám phá hàng trăm khóa học chất lượng cao được thiết kế đặc biệt 
                                cho trẻ em và thanh thiếu niên
                            </Typography>
                        </Box>
                    </motion.div>

                    {/* Stats Section */}
                    <motion.div variants={itemVariants}>
                        <Grid container spacing={3} sx={{ mb: 6 }}>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                                        {stats.totalCourses}+
                                    </Typography>
                                    <Typography variant="body1" color="text.secondary">
                                        Khóa học
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.secondary.main }}>
                                        {stats.totalStudents}+
                                    </Typography>
                                    <Typography variant="body1" color="text.secondary">
                                        Học sinh
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
                                        {stats.totalTeachers}+
                                    </Typography>
                                    <Typography variant="body1" color="text.secondary">
                                        Giáo viên
                                    </Typography>
                                </Box>
                            </Grid>
                            <Grid item xs={6} md={3}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.warning.main }}>
                                        {stats.completionRate}%
                                    </Typography>
                                    <Typography variant="body1" color="text.secondary">
                                        Hoàn thành
                                    </Typography>
                                </Box>
                            </Grid>
                        </Grid>
                    </motion.div>

                    {/* Featured Courses */}
                    <motion.div variants={itemVariants}>
                        <Typography
                            variant="h4"
                            sx={{
                                fontSize: { xs: '1.5rem', md: '2rem' },
                                fontWeight: 600,
                                mb: 4,
                                textAlign: 'center'
                            }}
                        >
                            Khóa học nổi bật
                        </Typography>
                    </motion.div>

                    <Grid container spacing={3} sx={{ mb: 6 }}>
                        {loading ? (
                            // Loading skeletons
                            Array.from({ length: 3 }).map((_, index) => (
                                <Grid item xs={12} md={4} key={index}>
                                    <Card sx={{ height: '100%' }}>
                                        <Skeleton variant="rectangular" height={200} />
                                        <CardContent>
                                            <Skeleton variant="text" height={32} />
                                            <Skeleton variant="text" height={20} />
                                            <Skeleton variant="text" height={20} />
                                        </CardContent>
                                    </Card>
                                </Grid>
                            ))
                        ) : (
                            featuredCourses.map((course, index) => (
                                <Grid item xs={12} md={4} key={course.id}>
                                    <motion.div
                                        variants={itemVariants}
                                        whileHover={{ 
                                            scale: 1.03,
                                            transition: { duration: 0.2 }
                                        }}
                                    >
                                        <Card
                                            sx={{
                                                height: '100%',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                borderRadius: '16px',
                                                overflow: 'hidden',
                                                transition: 'all 0.3s ease',
                                                '&:hover': {
                                                    boxShadow: theme.shadows[8]
                                                }
                                            }}
                                        >
                                            <CardMedia
                                                component="img"
                                                height="200"
                                                image={course.thumbnail || '/api/placeholder/300/200'}
                                                alt={course.title}
                                                sx={{ objectFit: 'cover' }}
                                            />
                                            <CardContent sx={{ flexGrow: 1, p: 3 }}>
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                                                    <Chip
                                                        label={course.subject?.name || 'Lập trình'}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: course.subject?.color || theme.palette.primary.main,
                                                            color: 'white',
                                                            fontWeight: 600
                                                        }}
                                                    />
                                                    <Chip
                                                        label={getDifficultyText(course.difficulty)}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: getDifficultyColor(course.difficulty),
                                                            color: 'white'
                                                        }}
                                                    />
                                                </Box>

                                                <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                                    {course.title}
                                                </Typography>
                                                
                                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                                    {course.description}
                                                </Typography>

                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                    <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
                                                        <PersonIcon fontSize="small" />
                                                    </Avatar>
                                                    <Typography variant="body2" sx={{ mr: 2 }}>
                                                        {course.instructor_name}
                                                    </Typography>
                                                    <TimeIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                                                    <Typography variant="body2" color="text.secondary">
                                                        {course.duration}
                                                    </Typography>
                                                </Box>

                                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <Rating value={course.rating} precision={0.1} size="small" readOnly />
                                                        <Typography variant="body2" sx={{ ml: 1 }}>
                                                            ({course.enrolled_count})
                                                        </Typography>
                                                    </Box>
                                                    <Typography variant="h6" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                                                        {course.price?.toLocaleString()}đ
                                                    </Typography>
                                                </Box>
                                            </CardContent>
                                            <CardActions sx={{ p: 3, pt: 0 }}>
                                                <Button
                                                    fullWidth
                                                    variant="contained"
                                                    endIcon={<ArrowIcon />}
                                                    onClick={() => navigate(`/academy/course/${course.id}`)}
                                                    sx={{
                                                        borderRadius: '10px',
                                                        py: 1.5,
                                                        fontWeight: 600
                                                    }}
                                                >
                                                    Xem chi tiết
                                                </Button>
                                            </CardActions>
                                        </Card>
                                    </motion.div>
                                </Grid>
                            ))
                        )}
                    </Grid>

                    {/* CTA Section */}
                    <motion.div variants={itemVariants}>
                        <Box
                            sx={{
                                textAlign: 'center',
                                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                borderRadius: '20px',
                                p: { xs: 4, md: 6 },
                                color: 'white'
                            }}
                        >
                            <Typography variant="h4" sx={{ fontWeight: 700, mb: 2 }}>
                                Sẵn sàng bắt đầu hành trình học tập?
                            </Typography>
                            <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                                Tham gia cùng hàng nghìn học sinh đã tin tướng BeE STEM Solutions
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                                <Button
                                    variant="contained"
                                    size="large"
                                    endIcon={<SchoolIcon />}
                                    onClick={() => navigate('/academy')}
                                    sx={{
                                        backgroundColor: 'white',
                                        color: theme.palette.primary.main,
                                        px: 4,
                                        py: 1.5,
                                        borderRadius: '50px',
                                        fontWeight: 600,
                                        '&:hover': {
                                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                            transform: 'translateY(-2px)'
                                        }
                                    }}
                                >
                                    Khám phá Academy
                                </Button>
                                <Button
                                    variant="outlined"
                                    size="large"
                                    startIcon={<PlayIcon />}
                                    onClick={() => navigate('/play')}
                                    sx={{
                                        borderColor: 'white',
                                        color: 'white',
                                        px: 4,
                                        py: 1.5,
                                        borderRadius: '50px',
                                        fontWeight: 600,
                                        '&:hover': {
                                            borderColor: 'white',
                                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                            transform: 'translateY(-2px)'
                                        }
                                    }}
                                >
                                    Thử ngay miễn phí
                                </Button>
                            </Box>
                        </Box>
                    </motion.div>
                </motion.div>
            </Container>
        </Box>
    );
};

export default ElearningSection;
