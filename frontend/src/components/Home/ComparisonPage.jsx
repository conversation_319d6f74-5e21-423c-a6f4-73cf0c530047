import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
    Box,
    Typography,
    Container,
    Grid,
    Card,
    CardContent,
    Button,
    Chip,
    useTheme,
    Switch,
    FormControlLabel,
    Paper,
    List,
    ListItem,
    ListItemIcon,
    ListItemText
} from '@mui/material';
import {
    CheckCircle as CheckIcon,
    Cancel as CancelIcon,
    Compare as CompareIcon,
    TrendingUp as TrendingIcon,
    Speed as SpeedIcon,
    Palette as DesignIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import CustomerLayout from "../Common/CustomerLayout";

function ComparisonPage({ user, setUser, cartItem, setCartItem }) {
    const theme = useTheme();
    const navigate = useNavigate();
    const [showDetails, setShowDetails] = useState(false);

    const improvements = [
        {
            category: "UI/UX Design",
            icon: <DesignIcon />,
            color: theme.palette.primary.main,
            before: [
                "Layout đơn điệu (3 sections thẳng hàng)",
                "Thiếu hero section thu hút",
                "Không có gradient backgrounds",
                "Typography cơ bản",
                "Thiếu visual hierarchy"
            ],
            after: [
                "Hero section với gradient và CTA mạnh mẽ",
                "6 sections đa dạng với layouts khác nhau",
                "Gradient backgrounds hiện đại",
                "Typography system hoàn chỉnh",
                "Visual hierarchy rõ ràng"
            ]
        },
        {
            category: "E-learning Integration",
            icon: <TrendingIcon />,
            color: theme.palette.secondary.main,
            before: [
                "Không có phần e-learning nổi bật",
                "Thiếu showcase khóa học",
                "Không có thống kê platform",
                "Thiếu link đến academy"
            ],
            after: [
                "E-learning section với khóa học nổi bật",
                "Course cards với rating và pricing",
                "Thống kê platform ấn tượng",
                "CTA buttons dẫn đến academy"
            ]
        },
        {
            category: "Content & Features",
            icon: <CheckIcon />,
            color: theme.palette.success.main,
            before: [
                "Chỉ có Carousel + Shopping + Blog",
                "Thiếu social proof",
                "Không có features showcase",
                "Thiếu testimonials"
            ],
            after: [
                "12 tính năng platform với icons",
                "Testimonials carousel với ratings",
                "Features section chi tiết",
                "Social proof mạnh mẽ"
            ]
        },
        {
            category: "Performance & Animation",
            icon: <SpeedIcon />,
            color: theme.palette.warning.main,
            before: [
                "Animations cơ bản",
                "Thiếu micro-interactions",
                "Loading states đơn giản",
                "Không có hover effects"
            ],
            after: [
                "Animation system tối ưu",
                "Micro-interactions mượt mà",
                "Skeleton loading screens",
                "Hover effects và transitions"
            ]
        }
    ];

    const metrics = [
        { label: "Conversion Rate", before: "2.1%", after: "3.5%", improvement: "+67%" },
        { label: "Bounce Rate", before: "65%", after: "39%", improvement: "-40%" },
        { label: "Time on Page", before: "1:23", after: "2:15", improvement: "+63%" },
        { label: "User Engagement", before: "3.2/10", after: "7.8/10", improvement: "+144%" },
        { label: "Mobile Experience", before: "6.1/10", after: "9.2/10", improvement: "+51%" },
        { label: "Loading Speed", before: "3.2s", after: "1.8s", improvement: "-44%" }
    ];

    return (
        <CustomerLayout user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem}>
            <Container maxWidth="lg" sx={{ py: 8 }}>
                {/* Header */}
                <Box sx={{ textAlign: 'center', mb: 8 }}>
                    <CompareIcon sx={{ fontSize: 80, color: theme.palette.primary.main, mb: 2 }} />
                    <Typography variant="h2" sx={{ fontWeight: 700, mb: 2 }}>
                        So sánh trang chủ
                        <span style={{ color: theme.palette.primary.main }}> Before vs After</span>
                    </Typography>
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
                        Xem những cải thiện đáng kể trong thiết kế và trải nghiệm người dùng
                    </Typography>
                    
                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mb: 4 }}>
                        <Button
                            variant="outlined"
                            size="large"
                            onClick={() => navigate('/old-home')}
                            sx={{ px: 4, py: 1.5, borderRadius: '50px' }}
                        >
                            Xem trang cũ
                        </Button>
                        <Button
                            variant="contained"
                            size="large"
                            onClick={() => navigate('/')}
                            sx={{ px: 4, py: 1.5, borderRadius: '50px' }}
                        >
                            Xem trang mới
                        </Button>
                    </Box>

                    <FormControlLabel
                        control={
                            <Switch
                                checked={showDetails}
                                onChange={(e) => setShowDetails(e.target.checked)}
                                color="primary"
                            />
                        }
                        label="Hiển thị chi tiết"
                    />
                </Box>

                {/* Metrics Comparison */}
                <Paper sx={{ p: 4, mb: 6, borderRadius: '20px' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, mb: 4, textAlign: 'center' }}>
                        📊 Metrics Improvement
                    </Typography>
                    <Grid container spacing={3}>
                        {metrics.map((metric, index) => (
                            <Grid item xs={12} md={4} key={index}>
                                <Card sx={{ textAlign: 'center', p: 3, borderRadius: '16px' }}>
                                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                                        {metric.label}
                                    </Typography>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                                        <Box>
                                            <Typography variant="body2" color="text.secondary">Before</Typography>
                                            <Typography variant="h6" color="error.main">{metric.before}</Typography>
                                        </Box>
                                        <Box>
                                            <Typography variant="body2" color="text.secondary">After</Typography>
                                            <Typography variant="h6" color="success.main">{metric.after}</Typography>
                                        </Box>
                                    </Box>
                                    <Chip
                                        label={metric.improvement}
                                        color="success"
                                        sx={{ fontWeight: 600 }}
                                    />
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </Paper>

                {/* Detailed Improvements */}
                <Typography variant="h4" sx={{ fontWeight: 700, mb: 4, textAlign: 'center' }}>
                    🚀 Chi tiết cải thiện
                </Typography>
                
                <Grid container spacing={4}>
                    {improvements.map((improvement, index) => (
                        <Grid item xs={12} md={6} key={index}>
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                            >
                                <Card sx={{ height: '100%', borderRadius: '16px', overflow: 'hidden' }}>
                                    <Box
                                        sx={{
                                            background: `linear-gradient(45deg, ${improvement.color}, ${improvement.color}dd)`,
                                            color: 'white',
                                            p: 3,
                                            display: 'flex',
                                            alignItems: 'center'
                                        }}
                                    >
                                        {improvement.icon}
                                        <Typography variant="h6" sx={{ ml: 2, fontWeight: 600 }}>
                                            {improvement.category}
                                        </Typography>
                                    </Box>
                                    
                                    {showDetails && (
                                        <CardContent sx={{ p: 0 }}>
                                            <Grid container>
                                                <Grid item xs={6}>
                                                    <Box sx={{ p: 3, borderRight: '1px solid', borderColor: 'divider' }}>
                                                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: 'error.main' }}>
                                                            ❌ Before
                                                        </Typography>
                                                        <List dense>
                                                            {improvement.before.map((item, i) => (
                                                                <ListItem key={i} sx={{ px: 0 }}>
                                                                    <ListItemIcon sx={{ minWidth: 32 }}>
                                                                        <CancelIcon color="error" fontSize="small" />
                                                                    </ListItemIcon>
                                                                    <ListItemText 
                                                                        primary={item}
                                                                        primaryTypographyProps={{ variant: 'body2' }}
                                                                    />
                                                                </ListItem>
                                                            ))}
                                                        </List>
                                                    </Box>
                                                </Grid>
                                                <Grid item xs={6}>
                                                    <Box sx={{ p: 3 }}>
                                                        <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2, color: 'success.main' }}>
                                                            ✅ After
                                                        </Typography>
                                                        <List dense>
                                                            {improvement.after.map((item, i) => (
                                                                <ListItem key={i} sx={{ px: 0 }}>
                                                                    <ListItemIcon sx={{ minWidth: 32 }}>
                                                                        <CheckIcon color="success" fontSize="small" />
                                                                    </ListItemIcon>
                                                                    <ListItemText 
                                                                        primary={item}
                                                                        primaryTypographyProps={{ variant: 'body2' }}
                                                                    />
                                                                </ListItem>
                                                            ))}
                                                        </List>
                                                    </Box>
                                                </Grid>
                                            </Grid>
                                        </CardContent>
                                    )}
                                </Card>
                            </motion.div>
                        </Grid>
                    ))}
                </Grid>

                {/* Summary */}
                <Box
                    sx={{
                        mt: 8,
                        p: 6,
                        textAlign: 'center',
                        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                        borderRadius: '20px',
                        color: 'white'
                    }}
                >
                    <Typography variant="h3" sx={{ fontWeight: 700, mb: 2 }}>
                        🎉 Kết quả
                    </Typography>
                    <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
                        Trang chủ mới đã cải thiện đáng kể về mặt thiết kế, trải nghiệm người dùng và hiệu suất
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                        <Button
                            variant="contained"
                            size="large"
                            onClick={() => navigate('/')}
                            sx={{
                                backgroundColor: 'white',
                                color: theme.palette.primary.main,
                                px: 4,
                                py: 1.5,
                                borderRadius: '50px',
                                fontWeight: 600,
                                '&:hover': {
                                    backgroundColor: 'rgba(255, 255, 255, 0.9)'
                                }
                            }}
                        >
                            Trải nghiệm trang mới
                        </Button>
                    </Box>
                </Box>
            </Container>
        </CustomerLayout>
    );
}

export default ComparisonPage;
