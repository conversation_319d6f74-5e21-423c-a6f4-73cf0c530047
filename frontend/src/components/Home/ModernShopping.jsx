import React from "react";
import PropTypes from "prop-types";
import { motion } from "framer-motion";
import {
    Box,
    Typography,
    Container,
    Grid,
    Card,
    CardContent,
    CardMedia,
    CardActions,
    Button,
    Chip,
    useTheme,
    IconButton,
    Avatar
} from "@mui/material";
import {
    ShoppingCart as CartIcon,
    Favorite as FavoriteIcon,
    Star as StarIcon,
    ArrowForward as ArrowIcon,
    LocalOffer as OfferIcon,
    Inventory as ProductIcon
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import Loading from "../Common/Loading";
import { staggerContainer, fadeInUp, cardHover, buttonHover } from './AnimationUtils';

ModernShopping.propTypes = {
    items: PropTypes.array.isRequired,
    cartItem: PropTypes.array.isRequired,
    setCartItem: PropTypes.func.isRequired,
    loading: PropTypes.bool.isRequired,
    handleAddToCart: PropTypes.func.isRequired
};

function ModernShopping(props) {
    const theme = useTheme();
    const navigate = useNavigate();

    const containerVariants = staggerContainer;
    const itemVariants = fadeInUp;

    const formatPrice = (price) => {
        if (!price || isNaN(price)) return 'Liên hệ';
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    };

    const handleAddToCart = (item) => {
        props.handleAddToCart(item);
    };

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: 'white' }}>
            <Container maxWidth="lg">
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                >
                    {/* Section Header */}
                    <motion.div variants={itemVariants}>
                        <Box sx={{ textAlign: 'center', mb: 6 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                                <Avatar
                                    sx={{
                                        width: 60,
                                        height: 60,
                                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                        mr: 2
                                    }}
                                >
                                    <ProductIcon sx={{ fontSize: 30, color: 'white' }} />
                                </Avatar>
                                <Typography
                                    variant="h2"
                                    sx={{
                                        fontSize: { xs: '2rem', md: '2.5rem' },
                                        fontWeight: 700,
                                        color: theme.palette.text.primary
                                    }}
                                >
                                    Cửa hàng
                                    <span style={{ color: theme.palette.primary.main }}> STEM</span>
                                </Typography>
                            </Box>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    maxWidth: '600px',
                                    mx: 'auto',
                                    mb: 4
                                }}
                            >
                                Khám phá bộ sưu tập sản phẩm STEM chất lượng cao, từ kit Arduino đến robot giáo dục
                            </Typography>

                            {/* Quick Stats */}
                            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 4, mb: 4 }}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                                        100+
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Sản phẩm
                                    </Typography>
                                </Box>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.secondary.main }}>
                                        4.8★
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Đánh giá
                                    </Typography>
                                </Box>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
                                        24h
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Giao hàng
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>
                    </motion.div>

                    {/* Products Grid */}
                    {props.loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                            <Loading />
                        </Box>
                    ) : (
                        <Grid container spacing={3} sx={{ mb: 6 }}>
                            {props.items.slice(0, 8).map((item, index) => (
                                <Grid item xs={12} sm={6} md={3} key={index}>
                                    <motion.div
                                        variants={itemVariants}
                                        {...cardHover}
                                    >
                                        <Card
                                            sx={{
                                                height: '100%',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                borderRadius: '16px',
                                                overflow: 'hidden',
                                                border: '1px solid',
                                                borderColor: 'rgba(0, 0, 0, 0.08)',
                                                transition: 'all 0.3s ease',
                                                '&:hover': {
                                                    borderColor: theme.palette.primary.main,
                                                    boxShadow: theme.shadows[8]
                                                }
                                            }}
                                        >
                                            {/* Product Image */}
                                            <Box sx={{ position: 'relative' }}>
                                                <CardMedia
                                                    component="img"
                                                    height="200"
                                                    image={item.image || '/api/placeholder/300/200'}
                                                    alt={item.name}
                                                    sx={{ objectFit: 'cover' }}
                                                />

                                                {/* Favorite Button */}
                                                <IconButton
                                                    sx={{
                                                        position: 'absolute',
                                                        top: 8,
                                                        right: 8,
                                                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                                        '&:hover': {
                                                            backgroundColor: 'white',
                                                            color: theme.palette.error.main
                                                        }
                                                    }}
                                                >
                                                    <FavoriteIcon />
                                                </IconButton>

                                                {/* Sale Badge */}
                                                {item.sale_price && (
                                                    <Chip
                                                        label="SALE"
                                                        size="small"
                                                        sx={{
                                                            position: 'absolute',
                                                            top: 8,
                                                            left: 8,
                                                            backgroundColor: theme.palette.error.main,
                                                            color: 'white',
                                                            fontWeight: 600
                                                        }}
                                                    />
                                                )}
                                            </Box>

                                            <CardContent sx={{ flexGrow: 1, p: 3 }}>
                                                {/* Category */}
                                                <Chip
                                                    label={
                                                        typeof item.category === 'object'
                                                            ? item.category?.name || 'STEM Kit'
                                                            : item.category || 'STEM Kit'
                                                    }
                                                    size="small"
                                                    sx={{
                                                        backgroundColor: theme.palette.primary.main,
                                                        color: 'white',
                                                        fontWeight: 600,
                                                        mb: 2
                                                    }}
                                                />

                                                {/* Product Name */}
                                                <Typography
                                                    variant="h6"
                                                    sx={{
                                                        fontWeight: 600,
                                                        mb: 1,
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 2,
                                                        WebkitBoxOrient: 'vertical'
                                                    }}
                                                >
                                                    {item.name}
                                                </Typography>

                                                {/* Description */}
                                                <Typography
                                                    variant="body2"
                                                    color="text.secondary"
                                                    sx={{
                                                        mb: 2,
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 2,
                                                        WebkitBoxOrient: 'vertical'
                                                    }}
                                                >
                                                    {item.description || 'Sản phẩm STEM chất lượng cao cho học tập và nghiên cứu'}
                                                </Typography>

                                                {/* Rating */}
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                                                        {[...Array(5)].map((_, i) => (
                                                            <StarIcon
                                                                key={i}
                                                                sx={{
                                                                    fontSize: 16,
                                                                    color: i < 4 ? theme.palette.warning.main : theme.palette.grey[300]
                                                                }}
                                                            />
                                                        ))}
                                                    </Box>
                                                    <Typography variant="body2" color="text.secondary">
                                                        (4.8)
                                                    </Typography>
                                                </Box>

                                                {/* Price */}
                                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                                    <Box>
                                                        {item.sale_price ? (
                                                            <>
                                                                <Typography
                                                                    variant="h6"
                                                                    sx={{
                                                                        fontWeight: 700,
                                                                        color: theme.palette.error.main
                                                                    }}
                                                                >
                                                                    {formatPrice(item.sale_price)}
                                                                </Typography>
                                                                <Typography
                                                                    variant="body2"
                                                                    sx={{
                                                                        textDecoration: 'line-through',
                                                                        color: theme.palette.text.secondary
                                                                    }}
                                                                >
                                                                    {formatPrice(item.price)}
                                                                </Typography>
                                                            </>
                                                        ) : (
                                                            <Typography
                                                                variant="h6"
                                                                sx={{
                                                                    fontWeight: 700,
                                                                    color: theme.palette.primary.main
                                                                }}
                                                            >
                                                                {formatPrice(item.price)}
                                                            </Typography>
                                                        )}
                                                    </Box>
                                                </Box>
                                            </CardContent>

                                            <CardActions sx={{ p: 3, pt: 0 }}>
                                                <motion.div {...buttonHover} style={{ width: '100%' }}>
                                                    <Button
                                                        fullWidth
                                                        variant="contained"
                                                        startIcon={<CartIcon />}
                                                        onClick={() => handleAddToCart(item)}
                                                        sx={{
                                                            borderRadius: '10px',
                                                            py: 1.5,
                                                            fontWeight: 600,
                                                            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                                                        }}
                                                    >
                                                        Thêm vào giỏ
                                                    </Button>
                                                </motion.div>
                                            </CardActions>
                                        </Card>
                                    </motion.div>
                                </Grid>
                            ))}
                        </Grid>
                    )}

                    {/* CTA Section */}
                    <motion.div variants={itemVariants}>
                        <Box
                            sx={{
                                textAlign: 'center',
                                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                borderRadius: '20px',
                                p: { xs: 4, md: 6 },
                                color: 'white'
                            }}
                        >
                            <OfferIcon sx={{ fontSize: 60, mb: 2, opacity: 0.9 }} />
                            <Typography variant="h4" sx={{ fontWeight: 700, mb: 2 }}>
                                Khám phá toàn bộ cửa hàng
                            </Typography>
                            <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                                Hơn 100 sản phẩm STEM chất lượng cao đang chờ bạn khám phá
                            </Typography>
                            <motion.div {...buttonHover}>
                                <Button
                                    variant="contained"
                                    size="large"
                                    endIcon={<ArrowIcon />}
                                    onClick={() => navigate('/shop')}
                                    sx={{
                                        backgroundColor: 'white',
                                        color: theme.palette.primary.main,
                                        px: 4,
                                        py: 1.5,
                                        borderRadius: '50px',
                                        fontWeight: 600,
                                        '&:hover': {
                                            backgroundColor: 'rgba(255, 255, 255, 0.9)'
                                        }
                                    }}
                                >
                                    Xem tất cả sản phẩm
                                </Button>
                            </motion.div>
                        </Box>
                    </motion.div>
                </motion.div>
            </Container>
        </Box>
    );
}

export default ModernShopping;
