import React from 'react';
import { motion } from 'framer-motion';
import {
    Box,
    Typography,
    Button,
    Container,
    Grid,
    Card,
    CardContent,
    useTheme,
    useMediaQuery
} from '@mui/material';
import {
    School as SchoolIcon,
    Code as CodeIcon,
    Psychology as PsychologyIcon,
    EmojiEvents as TrophyIcon,
    PlayArrow as PlayIcon,
    ArrowForward as ArrowIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import {
    staggerContainer,
    fadeInUp,
    fadeInLeft,
    fadeInRight,
    floatingAnimation,
    buttonHover,
    cardHover,
    hoverScale
} from './AnimationUtils';

const HeroSection = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const navigate = useNavigate();

    // Use imported animation variants
    const containerVariants = staggerContainer;
    const itemVariants = fadeInUp;

    const features = [
        {
            icon: <SchoolIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />,
            title: "E-Learning",
            description: "<PERSON>ọ<PERSON> trực tuyến với gi<PERSON>o trình chất lượng cao"
        },
        {
            icon: <CodeIcon sx={{ fontSize: 40, color: theme.palette.secondary.main }} />,
            title: "Lập trình",
            description: "Scratch, Python, Arduino và nhiều ngôn ngữ khác"
        },
        {
            icon: <PsychologyIcon sx={{ fontSize: 40, color: theme.palette.success.main }} />,
            title: "STEM",
            description: "Phát triển tư duy logic và sáng tạo"
        },
        {
            icon: <TrophyIcon sx={{ fontSize: 40, color: theme.palette.warning.main }} />,
            title: "Chứng chỉ",
            description: "Nhận chứng chỉ hoàn thành khóa học"
        }
    ];

    return (
        <Box
            sx={{
                background: `linear-gradient(135deg, 
                    ${theme.palette.primary.main}15 0%, 
                    ${theme.palette.secondary.main}10 50%, 
                    ${theme.palette.primary.main}05 100%)`,
                minHeight: '80vh',
                display: 'flex',
                alignItems: 'center',
                position: 'relative',
                overflow: 'hidden',
                py: { xs: 4, md: 8 }
            }}
        >
            {/* Background decorative elements */}
            <Box
                sx={{
                    position: 'absolute',
                    top: '10%',
                    right: '10%',
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: `linear-gradient(45deg, ${theme.palette.primary.main}20, ${theme.palette.secondary.main}20)`,
                    zIndex: 0
                }}
                component={motion.div}
                variants={floatingAnimation}
                animate="animate"
            />
            <Box
                sx={{
                    position: 'absolute',
                    bottom: '20%',
                    left: '5%',
                    width: 60,
                    height: 60,
                    borderRadius: '50%',
                    background: `linear-gradient(45deg, ${theme.palette.secondary.main}15, ${theme.palette.primary.main}15)`,
                    zIndex: 0
                }}
                component={motion.div}
                variants={floatingAnimation}
                animate="animate"
                style={{ animationDelay: '1s' }}
            />

            <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                >
                    <Grid container spacing={4} alignItems="center">
                        {/* Left side - Main content */}
                        <Grid item xs={12} md={6}>
                            <motion.div variants={itemVariants}>
                                <Typography
                                    variant="h1"
                                    sx={{
                                        fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                                        fontWeight: 700,
                                        background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                        backgroundClip: 'text',
                                        WebkitBackgroundClip: 'text',
                                        WebkitTextFillColor: 'transparent',
                                        mb: 2,
                                        lineHeight: 1.2
                                    }}
                                >
                                    Khám phá thế giới
                                    <br />
                                    <span style={{ color: theme.palette.text.primary }}>
                                        STEM & Lập trình
                                    </span>
                                </Typography>
                            </motion.div>

                            <motion.div variants={itemVariants}>
                                <Typography
                                    variant="h5"
                                    sx={{
                                        color: theme.palette.text.secondary,
                                        mb: 4,
                                        fontSize: { xs: '1.1rem', md: '1.3rem' },
                                        lineHeight: 1.6,
                                        maxWidth: '500px'
                                    }}
                                >
                                    Nền tảng học tập trực tuyến hàng đầu cho trẻ em và thanh thiếu niên.
                                    Học lập trình, khoa học, và phát triển tư duy sáng tạo.
                                </Typography>
                            </motion.div>

                            <motion.div variants={itemVariants}>
                                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 4 }}>
                                    <motion.div {...buttonHover}>
                                        <Button
                                            variant="contained"
                                            size="large"
                                            endIcon={<ArrowIcon />}
                                            onClick={() => navigate('/academy')}
                                            sx={{
                                                px: 4,
                                                py: 1.5,
                                                borderRadius: '50px',
                                                fontSize: '1.1rem',
                                                fontWeight: 600,
                                                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                                                boxShadow: theme.shadows[4],
                                                transition: 'all 0.3s ease'
                                            }}
                                        >
                                            Bắt đầu học ngay
                                        </Button>
                                    </motion.div>
                                    <motion.div {...buttonHover}>
                                        <Button
                                            variant="outlined"
                                            size="large"
                                            startIcon={<PlayIcon />}
                                            onClick={() => navigate('/play')}
                                            sx={{
                                                px: 4,
                                                py: 1.5,
                                                borderRadius: '50px',
                                                fontSize: '1.1rem',
                                                fontWeight: 600,
                                                borderWidth: 2,
                                                transition: 'all 0.3s ease'
                                            }}
                                        >
                                            Thử lập trình
                                        </Button>
                                    </motion.div>
                                </Box>
                            </motion.div>

                            {/* Stats */}
                            <motion.div variants={itemVariants}>
                                <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                                            1000+
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Học sinh
                                        </Typography>
                                    </Box>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.secondary.main }}>
                                            50+
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Khóa học
                                        </Typography>
                                    </Box>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
                                            98%
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Hài lòng
                                        </Typography>
                                    </Box>
                                </Box>
                            </motion.div>
                        </Grid>

                        {/* Right side - Feature cards */}
                        <Grid item xs={12} md={6}>
                            <motion.div variants={itemVariants}>
                                <Grid container spacing={2}>
                                    {features.map((feature, index) => (
                                        <Grid item xs={6} key={index}>
                                            <motion.div
                                                {...cardHover}
                                                variants={itemVariants}
                                            >
                                                <Card
                                                    sx={{
                                                        height: '100%',
                                                        background: 'rgba(255, 255, 255, 0.9)',
                                                        backdropFilter: 'blur(10px)',
                                                        border: '1px solid rgba(255, 255, 255, 0.2)',
                                                        borderRadius: '16px',
                                                        transition: 'all 0.3s ease',
                                                        '&:hover': {
                                                            boxShadow: theme.shadows[8],
                                                            background: 'rgba(255, 255, 255, 0.95)'
                                                        }
                                                    }}
                                                >
                                                    <CardContent sx={{ textAlign: 'center', p: 3 }}>
                                                        <Box sx={{ mb: 2 }}>
                                                            {feature.icon}
                                                        </Box>
                                                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                                                            {feature.title}
                                                        </Typography>
                                                        <Typography variant="body2" color="text.secondary">
                                                            {feature.description}
                                                        </Typography>
                                                    </CardContent>
                                                </Card>
                                            </motion.div>
                                        </Grid>
                                    ))}
                                </Grid>
                            </motion.div>
                        </Grid>
                    </Grid>
                </motion.div>
            </Container>
        </Box>
    );
};

export default HeroSection;
