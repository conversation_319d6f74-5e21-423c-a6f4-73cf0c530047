// Animation utilities and variants for consistent animations across the homepage

export const fadeInUp = {
    hidden: { 
        y: 30, 
        opacity: 0 
    },
    visible: {
        y: 0,
        opacity: 1,
        transition: {
            duration: 0.6,
            ease: "easeOut"
        }
    }
};

export const fadeInLeft = {
    hidden: { 
        x: -30, 
        opacity: 0 
    },
    visible: {
        x: 0,
        opacity: 1,
        transition: {
            duration: 0.6,
            ease: "easeOut"
        }
    }
};

export const fadeInRight = {
    hidden: { 
        x: 30, 
        opacity: 0 
    },
    visible: {
        x: 0,
        opacity: 1,
        transition: {
            duration: 0.6,
            ease: "easeOut"
        }
    }
};

export const scaleIn = {
    hidden: { 
        scale: 0.8, 
        opacity: 0 
    },
    visible: {
        scale: 1,
        opacity: 1,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    }
};

export const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    }
};

export const staggerFast = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.05,
            delayChildren: 0.1
        }
    }
};

export const floatingAnimation = {
    animate: {
        y: [-10, 10, -10],
        transition: {
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
        }
    }
};

export const pulseAnimation = {
    animate: {
        scale: [1, 1.05, 1],
        transition: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
        }
    }
};

export const rotateAnimation = {
    animate: {
        rotate: [0, 360],
        transition: {
            duration: 20,
            repeat: Infinity,
            ease: "linear"
        }
    }
};

// Hover animations
export const hoverScale = {
    whileHover: { 
        scale: 1.05,
        transition: { duration: 0.2 }
    },
    whileTap: { scale: 0.95 }
};

export const hoverLift = {
    whileHover: { 
        y: -5,
        boxShadow: "0 10px 25px rgba(0,0,0,0.15)",
        transition: { duration: 0.2 }
    }
};

export const hoverGlow = {
    whileHover: { 
        boxShadow: "0 0 20px rgba(33, 150, 243, 0.3)",
        transition: { duration: 0.2 }
    }
};

// Button animations
export const buttonHover = {
    whileHover: { 
        scale: 1.02,
        y: -2,
        transition: { duration: 0.2 }
    },
    whileTap: { 
        scale: 0.98,
        y: 0
    }
};

export const iconSpin = {
    whileHover: {
        rotate: 360,
        transition: { duration: 0.5 }
    }
};

// Text animations
export const typewriter = {
    hidden: { width: 0 },
    visible: {
        width: "100%",
        transition: {
            duration: 2,
            ease: "easeInOut"
        }
    }
};

export const slideInFromBottom = {
    hidden: { 
        y: 100, 
        opacity: 0 
    },
    visible: {
        y: 0,
        opacity: 1,
        transition: {
            duration: 0.8,
            ease: "easeOut"
        }
    }
};

// Card animations
export const cardHover = {
    whileHover: { 
        scale: 1.03,
        y: -8,
        boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
        transition: { 
            duration: 0.3,
            ease: "easeOut"
        }
    }
};

export const cardTap = {
    whileTap: { 
        scale: 0.97,
        transition: { duration: 0.1 }
    }
};

// Loading animations
export const shimmer = {
    animate: {
        backgroundPosition: ["200% 0", "-200% 0"],
        transition: {
            duration: 2,
            repeat: Infinity,
            ease: "linear"
        }
    }
};

export const bounce = {
    animate: {
        y: [0, -20, 0],
        transition: {
            duration: 0.6,
            repeat: Infinity,
            ease: "easeInOut"
        }
    }
};

// Page transition animations
export const pageTransition = {
    initial: { opacity: 0, y: 20 },
    animate: { 
        opacity: 1, 
        y: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: { 
        opacity: 0, 
        y: -20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

// Scroll-triggered animations
export const scrollReveal = {
    hidden: { 
        y: 50, 
        opacity: 0 
    },
    visible: {
        y: 0,
        opacity: 1,
        transition: {
            duration: 0.6,
            ease: "easeOut"
        }
    }
};

// Number counter animation
export const counterAnimation = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    }
};

// Gradient animation
export const gradientShift = {
    animate: {
        backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
        transition: {
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut"
        }
    }
};

export default {
    fadeInUp,
    fadeInLeft,
    fadeInRight,
    scaleIn,
    staggerContainer,
    staggerFast,
    floatingAnimation,
    pulseAnimation,
    rotateAnimation,
    hoverScale,
    hoverLift,
    hoverGlow,
    buttonHover,
    iconSpin,
    typewriter,
    slideInFromBottom,
    cardHover,
    cardTap,
    shimmer,
    bounce,
    pageTransition,
    scrollReveal,
    counterAnimation,
    gradientShift
};
