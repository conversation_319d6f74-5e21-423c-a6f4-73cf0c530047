import React from 'react';
import { Box, Typography, Container, useTheme, useMediaQuery } from '@mui/material';

const ResponsiveTest = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

    return (
        <Container maxWidth="lg" sx={{ py: 4 }}>
            <Typography variant="h4" gutterBottom>
                Responsive Design Test
            </Typography>
            
            <Box sx={{ 
                p: 2, 
                mb: 2, 
                backgroundColor: theme.palette.primary.light,
                borderRadius: 2
            }}>
                <Typography variant="h6">Current Breakpoint:</Typography>
                <Typography>
                    {isMobile && "Mobile (< 900px)"}
                    {isTablet && "Tablet (900px - 1200px)"}
                    {isDesktop && "Desktop (> 1200px)"}
                </Typography>
            </Box>

            <Box sx={{ 
                display: 'grid',
                gridTemplateColumns: {
                    xs: '1fr',
                    sm: '1fr 1fr',
                    md: '1fr 1fr 1fr',
                    lg: '1fr 1fr 1fr 1fr'
                },
                gap: 2,
                mb: 4
            }}>
                {[1, 2, 3, 4].map((item) => (
                    <Box
                        key={item}
                        sx={{
                            p: 3,
                            backgroundColor: theme.palette.secondary.light,
                            borderRadius: 2,
                            textAlign: 'center'
                        }}
                    >
                        <Typography variant="h6">Card {item}</Typography>
                        <Typography variant="body2">
                            This card adapts to screen size
                        </Typography>
                    </Box>
                ))}
            </Box>

            <Typography variant="body1">
                Screen width: {window.innerWidth}px
            </Typography>
        </Container>
    );
};

export default ResponsiveTest;
