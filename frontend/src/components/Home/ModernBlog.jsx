import React from "react";
import { motion } from "framer-motion";
import {
    Box,
    Typography,
    Container,
    Grid,
    Card,
    CardContent,
    CardMedia,
    CardActions,
    Button,
    Chip,
    Avatar,
    useTheme,
    IconButton
} from "@mui/material";
import {
    Article as ArticleIcon,
    AccessTime as TimeIcon,
    Person as PersonIcon,
    Visibility as ViewIcon,
    ThumbUp as LikeIcon,
    Share as ShareIcon,
    ArrowForward as ArrowIcon,
    TrendingUp as TrendingIcon
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import Loading from "../Common/Loading";
import axiosInstance from "../../services/axiosInstance";
import { staggerContainer, fadeInUp, cardHover, buttonHover } from './AnimationUtils';

function ModernBlog() {
    const theme = useTheme();
    const navigate = useNavigate();
    const [loading, setLoading] = React.useState(true);
    const [posts, setPosts] = React.useState([]);

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axiosInstance.get("/api/shopping/home/<USER>");
                setPosts(response.data);
            } catch (error) {
                console.error("Đã có lỗi fetch bài viết");
                // Fallback to mock data
                setPosts([
                    {
                        id: 1,
                        title: "Hướng dẫn lập trình Arduino cho người mới bắt đầu",
                        excerpt: "Tìm hiểu cách bắt đầu với Arduino từ những bước cơ bản nhất. Hướng dẫn chi tiết từ A-Z.",
                        image: "/api/placeholder/400/250",
                        author: "Thầy Minh",
                        created_at: "2024-01-15",
                        category: "Arduino",
                        views: 1250,
                        likes: 89,
                        reading_time: "5 phút đọc"
                    },
                    {
                        id: 2,
                        title: "Top 10 dự án Scratch thú vị cho trẻ em",
                        excerpt: "Khám phá những dự án Scratch sáng tạo giúp trẻ em học lập trình một cách vui vẻ và hiệu quả.",
                        image: "/api/placeholder/400/250",
                        author: "Cô Lan",
                        created_at: "2024-01-12",
                        category: "Scratch",
                        views: 2100,
                        likes: 156,
                        reading_time: "7 phút đọc"
                    },
                    {
                        id: 3,
                        title: "Python cho trẻ em: Bắt đầu từ đâu?",
                        excerpt: "Hướng dẫn chi tiết cách dạy Python cho trẻ em một cách dễ hiểu và thú vị.",
                        image: "/api/placeholder/400/250",
                        author: "Thầy Nam",
                        created_at: "2024-01-10",
                        category: "Python",
                        views: 1800,
                        likes: 134,
                        reading_time: "6 phút đọc"
                    }
                ]);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    const containerVariants = staggerContainer;
    const itemVariants = fadeInUp;

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const getCategoryColor = (category) => {
        const colors = {
            'Arduino': theme.palette.primary.main,
            'Scratch': theme.palette.secondary.main,
            'Python': theme.palette.success.main,
            'STEM': theme.palette.warning.main,
            'default': theme.palette.info.main
        };
        return colors[category] || colors.default;
    };

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: '#f8f9fa' }}>
            <Container maxWidth="lg">
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                >
                    {/* Section Header */}
                    <motion.div variants={itemVariants}>
                        <Box sx={{ textAlign: 'center', mb: 6 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                                <Avatar
                                    sx={{
                                        width: 60,
                                        height: 60,
                                        background: `linear-gradient(45deg, ${theme.palette.secondary.main}, ${theme.palette.primary.main})`,
                                        mr: 2
                                    }}
                                >
                                    <ArticleIcon sx={{ fontSize: 30, color: 'white' }} />
                                </Avatar>
                                <Typography
                                    variant="h2"
                                    sx={{
                                        fontSize: { xs: '2rem', md: '2.5rem' },
                                        fontWeight: 700,
                                        color: theme.palette.text.primary
                                    }}
                                >
                                    Blog
                                    <span style={{ color: theme.palette.secondary.main }}> STEM</span>
                                </Typography>
                            </Box>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    maxWidth: '600px',
                                    mx: 'auto',
                                    mb: 4
                                }}
                            >
                                Khám phá những bài viết hữu ích về STEM, lập trình và công nghệ giáo dục
                            </Typography>
                            
                            {/* Quick Stats */}
                            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 4, mb: 4 }}>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.secondary.main }}>
                                        50+
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Bài viết
                                    </Typography>
                                </Box>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                                        10K+
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Lượt đọc
                                    </Typography>
                                </Box>
                                <Box sx={{ textAlign: 'center' }}>
                                    <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.success.main }}>
                                        Weekly
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Cập nhật
                                    </Typography>
                                </Box>
                            </Box>
                        </Box>
                    </motion.div>

                    {/* Blog Posts Grid */}
                    {loading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                            <Loading />
                        </Box>
                    ) : (
                        <Grid container spacing={3} sx={{ mb: 6 }}>
                            {posts.slice(0, 6).map((post, index) => (
                                <Grid item xs={12} md={4} key={post.id || index}>
                                    <motion.div
                                        variants={itemVariants}
                                        {...cardHover}
                                    >
                                        <Card
                                            sx={{
                                                height: '100%',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                borderRadius: '16px',
                                                overflow: 'hidden',
                                                border: '1px solid',
                                                borderColor: 'rgba(0, 0, 0, 0.08)',
                                                transition: 'all 0.3s ease',
                                                '&:hover': {
                                                    borderColor: theme.palette.secondary.main,
                                                    boxShadow: theme.shadows[8]
                                                }
                                            }}
                                        >
                                            {/* Blog Image */}
                                            <Box sx={{ position: 'relative' }}>
                                                <CardMedia
                                                    component="img"
                                                    height="200"
                                                    image={post.image || '/api/placeholder/400/250'}
                                                    alt={post.title}
                                                    sx={{ objectFit: 'cover' }}
                                                />
                                                
                                                {/* Category Badge */}
                                                <Chip
                                                    label={post.category || 'STEM'}
                                                    size="small"
                                                    sx={{
                                                        position: 'absolute',
                                                        top: 12,
                                                        left: 12,
                                                        backgroundColor: getCategoryColor(post.category),
                                                        color: 'white',
                                                        fontWeight: 600
                                                    }}
                                                />

                                                {/* Trending Badge */}
                                                {index === 0 && (
                                                    <Chip
                                                        icon={<TrendingIcon sx={{ fontSize: 16, color: 'white' }} />}
                                                        label="HOT"
                                                        size="small"
                                                        sx={{
                                                            position: 'absolute',
                                                            top: 12,
                                                            right: 12,
                                                            backgroundColor: theme.palette.error.main,
                                                            color: 'white',
                                                            fontWeight: 600
                                                        }}
                                                    />
                                                )}
                                            </Box>

                                            <CardContent sx={{ flexGrow: 1, p: 3 }}>
                                                {/* Meta Info */}
                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <PersonIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
                                                        <Typography variant="caption" color="text.secondary">
                                                            {post.author || 'BeE Team'}
                                                        </Typography>
                                                    </Box>
                                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <TimeIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
                                                        <Typography variant="caption" color="text.secondary">
                                                            {post.reading_time || '5 phút đọc'}
                                                        </Typography>
                                                    </Box>
                                                </Box>

                                                {/* Title */}
                                                <Typography
                                                    variant="h6"
                                                    sx={{
                                                        fontWeight: 600,
                                                        mb: 2,
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 2,
                                                        WebkitBoxOrient: 'vertical',
                                                        lineHeight: 1.4
                                                    }}
                                                >
                                                    {post.title}
                                                </Typography>

                                                {/* Excerpt */}
                                                <Typography
                                                    variant="body2"
                                                    color="text.secondary"
                                                    sx={{
                                                        mb: 3,
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 3,
                                                        WebkitBoxOrient: 'vertical',
                                                        lineHeight: 1.5
                                                    }}
                                                >
                                                    {post.excerpt || post.description || 'Khám phá những kiến thức thú vị về STEM và lập trình...'}
                                                </Typography>

                                                {/* Stats */}
                                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                            <ViewIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
                                                            <Typography variant="caption" color="text.secondary">
                                                                {post.views || 1000}
                                                            </Typography>
                                                        </Box>
                                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                            <LikeIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
                                                            <Typography variant="caption" color="text.secondary">
                                                                {post.likes || 50}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                    <Typography variant="caption" color="text.secondary">
                                                        {formatDate(post.created_at || new Date())}
                                                    </Typography>
                                                </Box>
                                            </CardContent>

                                            <CardActions sx={{ p: 3, pt: 0, justifyContent: 'space-between' }}>
                                                <motion.div {...buttonHover}>
                                                    <Button
                                                        variant="contained"
                                                        endIcon={<ArrowIcon />}
                                                        onClick={() => navigate(`/blog/${post.id}`)}
                                                        sx={{
                                                            borderRadius: '10px',
                                                            px: 3,
                                                            py: 1,
                                                            fontWeight: 600,
                                                            background: `linear-gradient(45deg, ${theme.palette.secondary.main}, ${theme.palette.primary.main})`
                                                        }}
                                                    >
                                                        Đọc tiếp
                                                    </Button>
                                                </motion.div>
                                                <IconButton
                                                    size="small"
                                                    sx={{
                                                        color: theme.palette.text.secondary,
                                                        '&:hover': {
                                                            color: theme.palette.primary.main
                                                        }
                                                    }}
                                                >
                                                    <ShareIcon />
                                                </IconButton>
                                            </CardActions>
                                        </Card>
                                    </motion.div>
                                </Grid>
                            ))}
                        </Grid>
                    )}

                    {/* CTA Section */}
                    <motion.div variants={itemVariants}>
                        <Box
                            sx={{
                                textAlign: 'center',
                                background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.primary.main})`,
                                borderRadius: '20px',
                                p: { xs: 4, md: 6 },
                                color: 'white'
                            }}
                        >
                            <ArticleIcon sx={{ fontSize: 60, mb: 2, opacity: 0.9 }} />
                            <Typography variant="h4" sx={{ fontWeight: 700, mb: 2 }}>
                                Khám phá thêm nhiều bài viết
                            </Typography>
                            <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                                Cập nhật những kiến thức mới nhất về STEM, lập trình và công nghệ giáo dục
                            </Typography>
                            <motion.div {...buttonHover}>
                                <Button
                                    variant="contained"
                                    size="large"
                                    endIcon={<ArrowIcon />}
                                    onClick={() => navigate('/blog')}
                                    sx={{
                                        backgroundColor: 'white',
                                        color: theme.palette.secondary.main,
                                        px: 4,
                                        py: 1.5,
                                        borderRadius: '50px',
                                        fontWeight: 600,
                                        '&:hover': {
                                            backgroundColor: 'rgba(255, 255, 255, 0.9)'
                                        }
                                    }}
                                >
                                    Xem tất cả bài viết
                                </Button>
                            </motion.div>
                        </Box>
                    </motion.div>
                </motion.div>
            </Container>
        </Box>
    );
}

export default ModernBlog;
