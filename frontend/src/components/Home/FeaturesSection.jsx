import React from 'react';
import { motion } from 'framer-motion';
import {
    Box,
    Typography,
    Container,
    Grid,
    Card,
    CardContent,
    useTheme,
    Avatar
} from '@mui/material';
import {
    School as SchoolIcon,
    Code as CodeIcon,
    Quiz as QuizIcon,
    Assignment as AssignmentIcon,
    VideoLibrary as VideoIcon,
    EmojiEvents as CertificateIcon,
    Group as CommunityIcon,
    Support as SupportIcon,
    Psychology as AIIcon,
    Devices as DevicesIcon,
    CloudDownload as CloudIcon,
    Security as SecurityIcon
} from '@mui/icons-material';

const FeaturesSection = () => {
    const theme = useTheme();

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2
            }
        }
    };

    const itemVariants = {
        hidden: { y: 30, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        }
    };

    const features = [
        {
            icon: <SchoolIcon sx={{ fontSize: 40 }} />,
            title: "<PERSON><PERSON><PERSON> tập trực tuyến",
            description: "<PERSON><PERSON>n tảng e-learning hiện đại với giao di<PERSON> thân thiện, d<PERSON> sử dụng cho mọi lứa tuổi",
            color: theme.palette.primary.main,
            gradient: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`
        },
        {
            icon: <CodeIcon sx={{ fontSize: 40 }} />,
            title: "Lập trình đa ngôn ngữ",
            description: "Hỗ trợ Scratch, Python, Arduino, JavaScript và nhiều ngôn ngữ lập trình khác",
            color: theme.palette.secondary.main,
            gradient: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`
        },
        {
            icon: <VideoIcon sx={{ fontSize: 40 }} />,
            title: "Video bài giảng HD",
            description: "Thư viện video bài giảng chất lượng cao với âm thanh rõ ràng, dễ hiểu",
            color: theme.palette.error.main,
            gradient: `linear-gradient(135deg, ${theme.palette.error.main}, ${theme.palette.error.dark})`
        },
        {
            icon: <QuizIcon sx={{ fontSize: 40 }} />,
            title: "Kiểm tra & Đánh giá",
            description: "Hệ thống quiz và bài kiểm tra tự động với phản hồi chi tiết",
            color: theme.palette.warning.main,
            gradient: `linear-gradient(135deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`
        },
        {
            icon: <AssignmentIcon sx={{ fontSize: 40 }} />,
            title: "Bài tập thực hành",
            description: "Dự án thực tế giúp học sinh áp dụng kiến thức vào thực tiễn",
            color: theme.palette.success.main,
            gradient: `linear-gradient(135deg, ${theme.palette.success.main}, ${theme.palette.success.dark})`
        },
        {
            icon: <CertificateIcon sx={{ fontSize: 40 }} />,
            title: "Chứng chỉ hoàn thành",
            description: "Nhận chứng chỉ được công nhận sau khi hoàn thành khóa học",
            color: theme.palette.info.main,
            gradient: `linear-gradient(135deg, ${theme.palette.info.main}, ${theme.palette.info.dark})`
        },
        {
            icon: <AIIcon sx={{ fontSize: 40 }} />,
            title: "AI Hỗ trợ học tập",
            description: "Trợ lý AI thông minh giúp giải đáp thắc mắc và hướng dẫn học tập",
            color: '#9C27B0',
            gradient: 'linear-gradient(135deg, #9C27B0, #7B1FA2)'
        },
        {
            icon: <CommunityIcon sx={{ fontSize: 40 }} />,
            title: "Cộng đồng học tập",
            description: "Kết nối với bạn bè, chia sẻ kinh nghiệm và học hỏi lẫn nhau",
            color: '#FF5722',
            gradient: 'linear-gradient(135deg, #FF5722, #D84315)'
        },
        {
            icon: <DevicesIcon sx={{ fontSize: 40 }} />,
            title: "Đa nền tảng",
            description: "Học mọi lúc, mọi nơi trên máy tính, tablet và điện thoại",
            color: '#607D8B',
            gradient: 'linear-gradient(135deg, #607D8B, #455A64)'
        },
        {
            icon: <CloudIcon sx={{ fontSize: 40 }} />,
            title: "Lưu trữ đám mây",
            description: "Dự án và tiến độ học tập được đồng bộ an toàn trên cloud",
            color: '#795548',
            gradient: 'linear-gradient(135deg, #795548, #5D4037)'
        },
        {
            icon: <SupportIcon sx={{ fontSize: 40 }} />,
            title: "Hỗ trợ 24/7",
            description: "Đội ngũ hỗ trợ chuyên nghiệp sẵn sàng giúp đỡ mọi lúc",
            color: '#E91E63',
            gradient: 'linear-gradient(135deg, #E91E63, #C2185B)'
        },
        {
            icon: <SecurityIcon sx={{ fontSize: 40 }} />,
            title: "Bảo mật cao",
            description: "Thông tin cá nhân và dữ liệu học tập được bảo vệ tuyệt đối",
            color: '#3F51B5',
            gradient: 'linear-gradient(135deg, #3F51B5, #303F9F)'
        }
    ];

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: 'white' }}>
            <Container maxWidth="lg">
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                >
                    {/* Section Header */}
                    <motion.div variants={itemVariants}>
                        <Box sx={{ textAlign: 'center', mb: 8 }}>
                            <Typography
                                variant="h2"
                                sx={{
                                    fontSize: { xs: '2rem', md: '2.5rem' },
                                    fontWeight: 700,
                                    color: theme.palette.text.primary,
                                    mb: 2
                                }}
                            >
                                Tại sao chọn
                                <span style={{ color: theme.palette.primary.main }}> BeE STEM?</span>
                            </Typography>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    maxWidth: '700px',
                                    mx: 'auto',
                                    lineHeight: 1.6
                                }}
                            >
                                Chúng tôi cung cấp nền tảng học tập toàn diện với công nghệ tiên tiến 
                                và phương pháp giảng dạy hiệu quả nhất
                            </Typography>
                        </Box>
                    </motion.div>

                    {/* Features Grid */}
                    <Grid container spacing={4}>
                        {features.map((feature, index) => (
                            <Grid item xs={12} sm={6} md={4} key={index}>
                                <motion.div
                                    variants={itemVariants}
                                    whileHover={{ 
                                        scale: 1.05,
                                        transition: { duration: 0.2 }
                                    }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Card
                                        sx={{
                                            height: '100%',
                                            borderRadius: '20px',
                                            overflow: 'hidden',
                                            transition: 'all 0.3s ease',
                                            border: '1px solid',
                                            borderColor: 'rgba(0, 0, 0, 0.08)',
                                            '&:hover': {
                                                boxShadow: theme.shadows[12],
                                                borderColor: feature.color,
                                                transform: 'translateY(-4px)'
                                            }
                                        }}
                                    >
                                        <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                            {/* Icon with gradient background */}
                                            <Box
                                                sx={{
                                                    display: 'inline-flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    width: 80,
                                                    height: 80,
                                                    borderRadius: '50%',
                                                    background: feature.gradient,
                                                    mb: 3,
                                                    position: 'relative',
                                                    '&::before': {
                                                        content: '""',
                                                        position: 'absolute',
                                                        top: -2,
                                                        left: -2,
                                                        right: -2,
                                                        bottom: -2,
                                                        borderRadius: '50%',
                                                        background: feature.gradient,
                                                        opacity: 0.3,
                                                        zIndex: -1
                                                    }
                                                }}
                                            >
                                                <Box sx={{ color: 'white' }}>
                                                    {feature.icon}
                                                </Box>
                                            </Box>

                                            <Typography
                                                variant="h6"
                                                sx={{
                                                    fontWeight: 700,
                                                    mb: 2,
                                                    color: theme.palette.text.primary
                                                }}
                                            >
                                                {feature.title}
                                            </Typography>

                                            <Typography
                                                variant="body1"
                                                sx={{
                                                    color: theme.palette.text.secondary,
                                                    lineHeight: 1.6
                                                }}
                                            >
                                                {feature.description}
                                            </Typography>
                                        </CardContent>
                                    </Card>
                                </motion.div>
                            </Grid>
                        ))}
                    </Grid>

                    {/* Bottom CTA */}
                    <motion.div variants={itemVariants}>
                        <Box
                            sx={{
                                textAlign: 'center',
                                mt: 8,
                                p: { xs: 4, md: 6 },
                                borderRadius: '20px',
                                background: `linear-gradient(135deg, 
                                    ${theme.palette.primary.main}10, 
                                    ${theme.palette.secondary.main}10)`,
                                border: '1px solid',
                                borderColor: `${theme.palette.primary.main}20`
                            }}
                        >
                            <Typography
                                variant="h4"
                                sx={{
                                    fontWeight: 700,
                                    mb: 2,
                                    color: theme.palette.text.primary
                                }}
                            >
                                Trải nghiệm ngay hôm nay!
                            </Typography>
                            <Typography
                                variant="h6"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    mb: 3
                                }}
                            >
                                Tham gia cùng hàng nghìn học sinh đang học tập hiệu quả với BeE STEM
                            </Typography>
                            
                            {/* Feature highlights */}
                            <Grid container spacing={2} sx={{ maxWidth: '600px', mx: 'auto' }}>
                                <Grid item xs={4}>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Avatar
                                            sx={{
                                                width: 48,
                                                height: 48,
                                                mx: 'auto',
                                                mb: 1,
                                                background: theme.palette.success.main
                                            }}
                                        >
                                            <SchoolIcon />
                                        </Avatar>
                                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                            Học tập hiệu quả
                                        </Typography>
                                    </Box>
                                </Grid>
                                <Grid item xs={4}>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Avatar
                                            sx={{
                                                width: 48,
                                                height: 48,
                                                mx: 'auto',
                                                mb: 1,
                                                background: theme.palette.primary.main
                                            }}
                                        >
                                            <CertificateIcon />
                                        </Avatar>
                                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                            Chứng chỉ uy tín
                                        </Typography>
                                    </Box>
                                </Grid>
                                <Grid item xs={4}>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Avatar
                                            sx={{
                                                width: 48,
                                                height: 48,
                                                mx: 'auto',
                                                mb: 1,
                                                background: theme.palette.secondary.main
                                            }}
                                        >
                                            <SupportIcon />
                                        </Avatar>
                                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                            Hỗ trợ 24/7
                                        </Typography>
                                    </Box>
                                </Grid>
                            </Grid>
                        </Box>
                    </motion.div>
                </motion.div>
            </Container>
        </Box>
    );
};

export default FeaturesSection;
