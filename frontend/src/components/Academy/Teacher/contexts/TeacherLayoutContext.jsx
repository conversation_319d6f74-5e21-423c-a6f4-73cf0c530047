import React, { createContext, useContext, useState } from 'react';

const TeacherLayoutContext = createContext();

export const useTeacherLayout = () => {
    const context = useContext(TeacherLayoutContext);
    if (!context) {
        throw new Error('useTeacherLayout must be used within a TeacherLayoutProvider');
    }
    return context;
};

export const TeacherLayoutProvider = ({ children }) => {
    const [isFullScreen, setIsFullScreen] = useState(false);
    const [fullScreenTitle, setFullScreenTitle] = useState('');

    const enterFullScreen = (title = '') => {
        setIsFullScreen(true);
        setFullScreenTitle(title);
    };

    const exitFullScreen = () => {
        setIsFullScreen(false);
        setFullScreenTitle('');
    };

    const value = {
        isFullScreen,
        fullScreenTitle,
        enterFullScreen,
        exitFullScreen
    };

    return (
        <TeacherLayoutContext.Provider value={value}>
            {children}
        </TeacherLayoutContext.Provider>
    );
};

export default TeacherLayoutContext;
