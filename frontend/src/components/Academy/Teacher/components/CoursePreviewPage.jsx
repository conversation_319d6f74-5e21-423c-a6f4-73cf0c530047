import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { elearningAPI } from '../../../../services';
import { useTeacherLayout } from '../contexts/TeacherLayoutContext';
import {
    Box,
    Typography,
    Button,
    Paper,
    Drawer,
    AppBar,
    Toolbar,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    Chip,
    Card,
    CardContent,
    IconButton,
    Alert,
    LinearProgress
} from '@mui/material';
import {
    ArrowBack as BackIcon,
    MenuBook as LessonIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as TextIcon,
    Schedule as ScheduleIcon,
    PlayArrow as PlayIcon,
    CheckCircle as CheckIcon,
    Download as DownloadIcon
} from '@mui/icons-material';

const drawerWidth = 320;

function CoursePreviewPage({ courseId, onClose }) {
    const navigate = useNavigate();

    const { enterFullScreen, exitFullScreen } = useTeacherLayout();
    const [course, setCourse] = useState(null);
    const [courseContent, setCourseContent] = useState([]);
    const [selectedContent, setSelectedContent] = useState(null);
    const [completedItems, setCompletedItems] = useState(new Set());
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        if (courseId) {
            loadCourseData();
        }
    }, [courseId]);

    // Enter full-screen mode when component mounts
    useEffect(() => {
        enterFullScreen('Course Preview');
        return () => {
            exitFullScreen();
        };
    }, [enterFullScreen, exitFullScreen]);

    const handleClose = () => {
        exitFullScreen();
        onClose();
    };

    const loadCourseData = async () => {
        try {
            setLoading(true);
            setError(null);

            const [courseData, contentData] = await Promise.all([
                elearningAPI.teacherAPI.getCourse(courseId),
                elearningAPI.teacherAPI.getCourseContent(courseId)
            ]);

            setCourse(courseData);

            // Combine all content types with type markers and sort by order
            const allContent = [
                ...(contentData.lessons || []).map(item => ({ ...item, _type: 'lesson' })),
                ...(contentData.quizzes || []).map(item => ({ ...item, _type: 'quiz' })),
                ...(contentData.assignments || []).map(item => ({ ...item, _type: 'assignment' }))
            ].sort((a, b) => a.order - b.order);

            setCourseContent(allContent);

            // Auto-select first content item
            if (allContent.length > 0) {
                setSelectedContent(allContent[0]);
            }

        } catch (error) {
            console.error('Error loading course data:', error);
            setError('Không thể tải dữ liệu khóa học');
        } finally {
            setLoading(false);
        }
    };

    const handleSelectContent = (content) => {
        setSelectedContent(content);
    };

    const handleMarkComplete = (content) => {
        setCompletedItems(prev => new Set([...prev, content]));
    };

    const getContentIcon = (content) => {
        const type = content._type || content.type;
        const contentType = content.content_type || content.contentType;

        if (type === 'lesson') {
            switch (contentType) {
                case 'video': return <VideoIcon sx={{ color: '#f44336' }} />;
                case 'file': return <FileIcon sx={{ color: '#ff9800' }} />;
                default: return <TextIcon sx={{ color: '#2196f3' }} />;
            }
        } else if (type === 'quiz') {
            return <QuizIcon sx={{ color: '#4caf50' }} />;
        } else if (type === 'turbowarp' || type === 'assignment') {
            return <TurboWarpIcon sx={{ color: '#9c27b0' }} />;
        }
        return <LessonIcon sx={{ color: '#607d8b' }} />;
    };

    const getEmbedUrl = (url) => {
        if (!url) return "";
        const videoId = new URL(url).searchParams.get("v");
        return `https://www.youtube.com/embed/${videoId}`;
    };

    const renderContent = () => {
        if (!selectedContent) return null;

        const type = selectedContent._type || selectedContent.type;
        const contentType = selectedContent.content_type || selectedContent.contentType;
        const videoUrl = selectedContent.video_url || selectedContent.videoUrl;

        switch (type) {
            case 'lesson':
                if (contentType === 'text') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <Typography variant="h5" sx={{ mb: 3, color: 'primary.main' }}>
                                {selectedContent.title}
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
                                {selectedContent.description}
                            </Typography>
                            <Divider sx={{ mb: 3 }} />
                            <div
                                dangerouslySetInnerHTML={{ __html: selectedContent.content || 'Nội dung bài học đang được cập nhật...' }}
                                style={{ lineHeight: 1.6 }}
                            />
                        </Box>
                    );
                } else if (contentType === 'video') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <Typography variant="h5" sx={{ mb: 2, color: 'primary.main' }}>
                                {selectedContent.title}
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
                                {selectedContent.description}
                            </Typography>
                            {videoUrl ? (
                                <Box sx={{
                                    position: 'relative',
                                    paddingBottom: '56.25%',
                                    height: 0,
                                    borderRadius: '10px',
                                    overflow: 'hidden'
                                }}>
                                    <iframe
                                        src={getEmbedUrl(videoUrl)}
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            width: '100%',
                                            height: '100%',
                                            border: 'none'
                                        }}
                                        allowFullScreen
                                        title={selectedContent.title}
                                    />
                                </Box>
                            ) : (
                                <Alert severity="info">
                                    Video đang được cập nhật...
                                </Alert>
                            )}
                        </Box>
                    );
                } else if (selectedContent.contentType === 'file') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <Typography variant="body1" sx={{ mb: 3 }}>
                                {selectedContent.description}
                            </Typography>
                            <Card sx={{ borderRadius: '10px', border: '2px dashed #ddd' }}>
                                <CardContent sx={{ textAlign: 'center', py: 4 }}>
                                    <FileIcon sx={{ fontSize: 64, color: '#ff9800', mb: 2 }} />
                                    <Typography variant="h6" gutterBottom>
                                        {selectedContent.fileName}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                                        Kích thước: {selectedContent.fileSize}
                                    </Typography>
                                    <Button
                                        variant="contained"
                                        startIcon={<DownloadIcon />}
                                        component="a"
                                        href={selectedContent.fileUrl}
                                        download
                                        sx={{ borderRadius: '10px' }}
                                    >
                                        Tải xuống
                                    </Button>
                                </CardContent>
                            </Card>
                        </Box>
                    );
                }
                break;

            case 'quiz':
                return (
                    <Box sx={{ p: 3 }}>
                        <Typography variant="h5" sx={{ mb: 2, color: 'primary.main' }}>
                            {selectedContent.title}
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
                            {selectedContent.description}
                        </Typography>
                        <Alert severity="info" sx={{ mb: 3, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <QuizIcon />
                                <Box>
                                    <Typography variant="body2">
                                        Bài kiểm tra gồm {selectedContent.questions?.length || 0} câu hỏi
                                    </Typography>
                                    <Typography variant="body2">
                                        Thời gian: {selectedContent.duration} phút | Điểm đạt: {selectedContent.passing_score}%
                                    </Typography>
                                    <Typography variant="body2">
                                        Số lần làm tối đa: {selectedContent.max_attempts}
                                    </Typography>
                                </Box>
                            </Box>
                        </Alert>

                        {selectedContent.questions && selectedContent.questions.length > 0 ? (
                            selectedContent.questions.map((question, index) => (
                                <Card key={index} sx={{ mb: 2, borderRadius: '10px' }}>
                                    <CardContent>
                                        <Typography variant="h6" gutterBottom>
                                            Câu {index + 1}: {question.question_text || question.question}
                                        </Typography>
                                        {(question.options || []).map((option, optIndex) => (
                                            <Typography
                                                key={optIndex}
                                                variant="body2"
                                                sx={{
                                                    ml: 2,
                                                    mb: 1,
                                                    color: optIndex === question.correctAnswer ? '#2e7d32' : 'inherit',
                                                    fontWeight: optIndex === question.correctAnswer ? 'bold' : 'normal'
                                                }}
                                            >
                                                {String.fromCharCode(65 + optIndex)}. {option}
                                                {optIndex === question.correctAnswer && ' ✓'}
                                            </Typography>
                                        ))}
                                    </CardContent>
                                </Card>
                            ))
                        ) : (
                            <Alert severity="info" sx={{ mb: 3 }}>
                                Bài kiểm tra chưa có câu hỏi nào.
                            </Alert>
                        )}

                        <Button
                            variant="contained"
                            startIcon={<PlayIcon />}
                            fullWidth
                            sx={{ mt: 2, borderRadius: '10px' }}
                            disabled={!selectedContent.questions || selectedContent.questions.length === 0}
                        >
                            Bắt đầu làm bài
                        </Button>
                    </Box>
                );

            case 'turbowarp':
            case 'assignment':
                const requiredFeatures = selectedContent.required_features || selectedContent.requiredFeatures || [];
                const gradingCriteria = selectedContent.grading_criteria || selectedContent.gradingCriteria || [];

                return (
                    <Box sx={{ p: 3 }}>
                        <Typography variant="h5" sx={{ mb: 2, color: 'primary.main' }}>
                            {selectedContent.title}
                        </Typography>
                        <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
                            {selectedContent.description}
                        </Typography>

                        <Alert severity="info" sx={{ mb: 3, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <TurboWarpIcon />
                                <Box>
                                    <Typography variant="body2">
                                        Loại bài tập: {selectedContent.assignment_type || 'TurboWarp'}
                                    </Typography>
                                    <Typography variant="body2">
                                        Điểm tối đa: {selectedContent.max_score} | Số lần nộp tối đa: {selectedContent.max_attempts}
                                    </Typography>
                                    {selectedContent.due_date && (
                                        <Typography variant="body2">
                                            Hạn nộp: {new Date(selectedContent.due_date).toLocaleDateString('vi-VN')}
                                        </Typography>
                                    )}
                                </Box>
                            </Box>
                        </Alert>

                        {requiredFeatures.length > 0 && (
                            <Card sx={{ mb: 3, borderRadius: '10px' }}>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        Yêu cầu tính năng:
                                    </Typography>
                                    {requiredFeatures.map((feature, index) => (
                                        <Typography key={index} variant="body2" sx={{ mb: 1, ml: 2 }}>
                                            • {feature}
                                        </Typography>
                                    ))}
                                </CardContent>
                            </Card>
                        )}

                        {gradingCriteria.length > 0 && (
                            <Card sx={{ mb: 3, borderRadius: '10px' }}>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        Tiêu chí chấm điểm:
                                    </Typography>
                                    {gradingCriteria.map((criteria, index) => (
                                        <Typography key={index} variant="body2" sx={{ mb: 1, ml: 2 }}>
                                            • {criteria}
                                        </Typography>
                                    ))}
                                </CardContent>
                            </Card>
                        )}

                        {selectedContent.starter_project_url && (
                            <Card sx={{ mb: 3, borderRadius: '10px' }}>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        Dự án mẫu:
                                    </Typography>
                                    <Button
                                        variant="outlined"
                                        href={selectedContent.starter_project_url}
                                        target="_blank"
                                        sx={{ borderRadius: '10px' }}
                                    >
                                        Mở dự án mẫu
                                    </Button>
                                </CardContent>
                            </Card>
                        )}

                        <Button
                            variant="outlined"
                            component="a"
                            href={selectedContent.starterProject}
                            target="_blank"
                            fullWidth
                            sx={{ mb: 2, borderRadius: '10px' }}
                        >
                            Mở dự án mẫu
                        </Button>

                        <Button
                            variant="contained"
                            startIcon={<PlayIcon />}
                            fullWidth
                            sx={{ borderRadius: '10px' }}
                        >
                            Bắt đầu làm bài tập
                        </Button>
                    </Box>
                );

            default:
                return null;
        }
    };

    const getTotalDuration = () => {
        return courseContent.reduce((total, item) => total + (item.duration || 0), 0);
    };

    const getCompletedDuration = () => {
        return courseContent
            .filter(item => completedItems.has(item.id))
            .reduce((total, item) => total + (item.duration || 0), 0);
    };

    const formatDuration = (minutes) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    };

    if (loading) {
        return (
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
                flexDirection: 'column',
                gap: 2
            }}>
                <LinearProgress sx={{ width: '300px' }} />
                <Typography>Đang tải dữ liệu khóa học...</Typography>
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
                flexDirection: 'column',
                gap: 2
            }}>
                <Alert severity="error" sx={{ mb: 2 }}>
                    {error}
                </Alert>
                <Button variant="contained" onClick={() => loadCourseData()}>
                    Thử lại
                </Button>
                <Button variant="outlined" onClick={onClose}>
                    Quay lại
                </Button>
            </Box>
        );
    }

    if (!course) {
        return (
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh'
            }}>
                <Typography>Không tìm thấy khóa học</Typography>
            </Box>
        );
    }

    return (
        <Box sx={{
            display: 'flex',
            height: '100vh',
            '& @keyframes pulse': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.5 },
                '100%': { opacity: 1 }
            }
        }}>
            {/* AppBar */}
            <AppBar
                position="fixed"
                sx={{
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    bgcolor: '#1a237e',
                    // borderRadius: '0 0 10px 10px'
                }}
            >
                <Toolbar>
                    <IconButton
                        color="inherit"
                        onClick={handleClose}
                        sx={{ mr: 2 }}
                    >
                        <BackIcon />
                    </IconButton>
                    <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
                        {course.title}
                    </Typography>
                    <Chip
                        label={`${formatDuration(getCompletedDuration())} / ${formatDuration(getTotalDuration())}`}
                        color="warning"
                        sx={{ borderRadius: '10px' }}
                    />
                </Toolbar>
            </AppBar>

            {/* Sidebar */}
            <Drawer
                variant="permanent"
                sx={{
                    width: drawerWidth,
                    flexShrink: 0,
                    '& .MuiDrawer-paper': {
                        width: drawerWidth,
                        boxSizing: 'border-box',
                        borderRadius: '0 10px 10px 0'
                    },
                }}
            >
                <Toolbar />
                <Box sx={{ p: 2 }}>
                    {/* <Typography variant="h6" gutterBottom>
                        {course.title}
                    </Typography> */}
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                        Giảng viên: {course.teacher?.first_name} {course.teacher?.last_name || course.teacher}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                        Môn học: {course.subject?.name} - {course.grade?.name}
                    </Typography>

                    {/* Progress */}
                    <Box sx={{ mt: 2, mb: 3 }}>
                        <Typography variant="body2" gutterBottom>
                            Tiến độ: {completedItems.size}/{courseContent.length} bài học
                        </Typography>
                        <LinearProgress
                            variant="determinate"
                            value={(completedItems.size / courseContent.length) * 100}
                            sx={{ height: 8, borderRadius: 4 }}
                        />
                    </Box>
                </Box>

                <Divider />

                <List sx={{ flexGrow: 1, overflow: 'auto' }}>
                    {courseContent
                        .sort((a, b) => a.order - b.order)
                        .map((content, index) => (
                            <ListItem
                                key={index}
                                button
                                selected={selectedContent?.id === content.id && selectedContent?.title === content.title}
                                onClick={() => handleSelectContent(content)}
                                sx={{
                                    cursor: "pointer",
                                    borderRadius: '10px',
                                    mx: 1,
                                    mb: 1,
                                    position: 'relative',
                                    border: selectedContent?.id === content.id && selectedContent?.title === content.title ? '2px solid #1976d2' : '2px solid transparent',
                                    boxShadow: selectedContent?.id === content.id && selectedContent?.title === content.title ? '0 4px 12px rgba(25, 118, 210, 0.2)' : 'none',
                                    transform: selectedContent?.id === content.id && selectedContent?.title === content.title ? 'scale(1.02)' : 'scale(1)',
                                    transition: 'all 0.2s ease-in-out',
                                    '&.Mui-selected': {
                                        bgcolor: '#e3f2fd',
                                        '&:hover': {
                                            bgcolor: '#bbdefb',
                                        },
                                    },
                                    '&:hover': {
                                        transform: 'scale(1.01)',
                                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                                    },
                                    '&::before': selectedContent?.id === content.id && selectedContent?.title === content.title ? {
                                        content: '""',
                                        position: 'absolute',
                                        left: '-8px',
                                        top: '50%',
                                        transform: 'translateY(-50%)',
                                        width: '4px',
                                        height: '60%',
                                        bgcolor: '#1976d2',
                                        borderRadius: '2px',
                                        transition: 'all 0.2s ease-in-out'
                                    } : {}
                                }}
                            >
                                <ListItemIcon>
                                    <Box sx={{
                                        p: 0.5,
                                        borderRadius: '8px',
                                        bgcolor: selectedContent?.id === content.id && selectedContent?.title === content.title ? 'rgba(25, 118, 210, 0.15)' : 'transparent',
                                        border: selectedContent?.id === content.id && selectedContent?.title === content.title ? '1px solid rgba(25, 118, 210, 0.3)' : '1px solid transparent',
                                        transition: 'all 0.2s ease-in-out',
                                        transform: selectedContent?.id === content.id && selectedContent?.title === content.title ? 'scale(1.1)' : 'scale(1)',
                                        '& svg': {
                                            filter: selectedContent?.id === content.id && selectedContent?.title === content.title ? 'brightness(1.2)' : 'none'
                                        }
                                    }}>
                                        {getContentIcon(content)}
                                    </Box>
                                </ListItemIcon>
                                <ListItemText
                                    primary={
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                fontWeight: selectedContent?.id === content.id && selectedContent?.title === content.title ? 600 : 400,
                                                color: selectedContent?.id === content.id && selectedContent?.title === content.title ? '#1976d2' : 'inherit',
                                                transition: 'all 0.2s ease-in-out'
                                            }}
                                        >
                                            {content.title}
                                        </Typography>
                                    }
                                    secondary={
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 0.5 }}>
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <ScheduleIcon sx={{
                                                    fontSize: 14,
                                                    mr: 0.5,
                                                    color: selectedContent?.id === content.id && selectedContent?.title === content.title ? '#1976d2' : 'inherit'
                                                }} />
                                                <Typography
                                                    variant="caption"
                                                    sx={{
                                                        color: selectedContent?.id === content.id && selectedContent?.title === content.title ? '#1976d2' : 'inherit',
                                                        fontWeight: selectedContent?.id === content.id && selectedContent?.title === content.title ? 500 : 400
                                                    }}
                                                >
                                                    {formatDuration(content.duration || 0)}
                                                </Typography>
                                            </Box>
                                            <Chip
                                                label={
                                                    content._type === 'quiz' ? 'Quiz' :
                                                        content._type === 'assignment' ? 'Bài tập' : 'Bài học'
                                                }
                                                size="small"
                                                variant="outlined"
                                                sx={{
                                                    fontSize: '10px',
                                                    height: '20px',
                                                    color: content._type === 'quiz' ? '#4caf50' :
                                                        content._type === 'assignment' ? '#9c27b0' : '#2196f3'
                                                }}
                                            />
                                            {completedItems.has(content) && (
                                                <CheckIcon sx={{
                                                    fontSize: 16,
                                                    ml: 1,
                                                    color: '#4caf50',
                                                    animation: selectedContent?.id === content.id && selectedContent?.title === content.title ? 'pulse 1.5s infinite' : 'none'
                                                }} />
                                            )}
                                        </Box>
                                    }
                                />
                            </ListItem>
                        ))}
                </List>

                {selectedContent && !completedItems.has(selectedContent.id) && (
                    <Box sx={{ p: 2 }}>
                        <Button
                            variant="contained"
                            startIcon={<CheckIcon />}
                            fullWidth
                            onClick={() => handleMarkComplete(selectedContent)}
                            sx={{ borderRadius: '10px', mb: 2 }}
                        >
                            Đánh dấu hoàn thành
                        </Button>

                        <Button
                            variant="outlined"
                            startIcon={<BackIcon />}
                            fullWidth
                            onClick={() => {
                                const targetPath = `/teacher/courses/${courseId}/builder`;

                                try {
                                    navigate(targetPath);
                                } catch (error) {
                                    console.error('Navigation error:', error);
                                    // Fallback: Use onClose callback
                                    if (onClose) {
                                        onClose();
                                    }
                                }
                            }}
                            sx={{
                                borderRadius: '10px',
                                borderColor: '#ccc',
                                color: '#666',
                                '&:hover': {
                                    borderColor: '#999',
                                    backgroundColor: '#f5f5f5'
                                }
                            }}
                        >
                            Trở về Course Builder
                        </Button>
                    </Box>
                )}
            </Drawer>

            {/* Main content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    // bgcolor: 'background.default',
                    overflow: 'auto',
                    // minHeight: 'calc(100vh - 500px)'
                }}
            >
                <Toolbar />
                <Paper sx={{ m: 2, borderRadius: '10px', minHeight: "400px" }}>
                    {renderContent()}
                </Paper>
            </Box>
        </Box>
    );
}

export default CoursePreviewPage;
