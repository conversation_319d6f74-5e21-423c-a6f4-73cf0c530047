import { useState, useEffect } from 'react';
import { elearningAPI } from '../../../../services';
import {
    Box,
    Typography,
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    IconButton,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Avatar,
    Tooltip,
    Alert,
    Snackbar,
    Card,
    CardContent,
    CardActions,
    Divider,
    Stack,
    alpha
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Edit as EditIcon,
    Delete as DeleteIcon,
    Search as SearchIcon,
    PersonAdd as PersonAddIcon,
    Email as EmailIcon,
    Phone as PhoneIcon,
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    TrendingUp as TrendingUpIcon,
    FilterList as FilterIcon,
    People as PeopleIcon,
    EmojiEvents as EmojiEventsIcon,
    Visibility as VisibilityIcon,
    Groups as GroupsIcon,
    ViewList as ViewListIcon,
    SpaceDashboard as SpaceDashboardIcon
} from '@mui/icons-material';
import CustomButton, { beeColors } from '../../../Common/CustomButton';
import CustomTextField from '../../../Common/CustomTextField';

function StudentManagement() {
    const [students, setStudents] = useState([]);
    const [filteredStudents, setFilteredStudents] = useState([]);
    const [courses, setCourses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(12);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingStudent, setEditingStudent] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterCourse, setFilterCourse] = useState('');
    const [filterStatus, setFilterStatus] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'

    // Form state
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        courseId: '',
        studentId: '',
        parentName: '',
        parentPhone: '',
        address: '',
        status: 'active'
    });

    useEffect(() => {
        loadData();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            // Load teacher's courses and their enrolled students
            const coursesData = await elearningAPI.teacherAPI.getCourses();
            setCourses(coursesData);

            // Get all students from all courses
            const allStudents = [];
            for (const course of coursesData) {
                if (course.enrolled_count > 0) {
                    // In a real implementation, you'd have an API to get course students
                    // For now, we'll use mock data based on course info
                    const courseStudents = generateMockStudentsForCourse(course);
                    allStudents.push(...courseStudents);
                }
            }

            setStudents(allStudents);
            setFilteredStudents(allStudents);
        } catch (error) {
            console.error('Error loading data:', error);
            showSnackbar('Không thể tải dữ liệu học sinh', 'error');
        } finally {
            setLoading(false);
        }
    };

    const generateMockStudentsForCourse = (course) => {
        // Generate mock students for demonstration
        const mockStudents = [];
        const studentCount = Math.min(course.enrolled_count || 0, 5); // Limit for demo

        for (let i = 1; i <= studentCount; i++) {
            mockStudents.push({
                id: `${course.id}-${i}`,
                firstName: `Học sinh ${i}`,
                lastName: `Khóa ${course.title}`,
                email: `student${i}@course${course.id}.com`,
                phone: `012345678${i}`,
                courseId: course.id,
                courseName: course.title,
                studentId: `HS${course.id}${String(i).padStart(3, '0')}`,
                parentName: `Phụ huynh ${i}`,
                parentPhone: `098765432${i}`,
                address: `Địa chỉ ${i}`,
                status: 'active',
                joinDate: '2024-01-05',
                assignments: Math.floor(Math.random() * 10),
                quizzes: Math.floor(Math.random() * 8),
                avgScore: (Math.random() * 4 + 6).toFixed(1) // 6.0 - 10.0
            });
        }

        return mockStudents;
    };

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({
            open: true,
            message,
            severity
        });
    };

    // Filter logic
    useEffect(() => {
        let filtered = students;

        if (searchTerm) {
            filtered = filtered.filter(s =>
                `${s.firstName} ${s.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
                s.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                s.studentId.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterCourse) {
            filtered = filtered.filter(s => s.courseId === parseInt(filterCourse));
        }

        if (filterStatus) {
            filtered = filtered.filter(s => s.status === filterStatus);
        }

        setFilteredStudents(filtered);
        setPage(0);
    }, [students, searchTerm, filterCourse, filterStatus]);

    const handleChangePage = (_, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleOpenDialog = (student = null) => {
        if (student) {
            setEditingStudent(student);
            setFormData({
                firstName: student.firstName,
                lastName: student.lastName,
                email: student.email,
                phone: student.phone,
                courseId: student.courseId,
                studentId: student.studentId,
                parentName: student.parentName,
                parentPhone: student.parentPhone,
                address: student.address,
                status: student.status
            });
        } else {
            setEditingStudent(null);
            setFormData({
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
                courseId: '',
                studentId: '',
                parentName: '',
                parentPhone: '',
                address: '',
                status: 'active'
            });
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingStudent(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSaveStudent = () => {
        // In a real implementation, this would call an API
        showSnackbar('Tính năng đang được phát triển', 'info');
        handleCloseDialog();
    };

    const handleDeleteStudent = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa học sinh này?')) {
            // In a real implementation, this would call an API
            showSnackbar('Tính năng đang được phát triển', 'info');
        }
    };

    const getStatusChip = (status) => {
        const statusConfig = {
            active: { label: 'Đang học', color: 'success' },
            inactive: { label: 'Tạm nghỉ', color: 'warning' },
            completed: { label: 'Hoàn thành', color: 'info' },
            dropped: { label: 'Bỏ học', color: 'error' }
        };

        const config = statusConfig[status] || statusConfig.active;
        return <Chip label={config.label} color={config.color} size="small" />;
    };

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
                <Typography>Đang tải dữ liệu...</Typography>
            </Box>
        );
    }

    return (
        <Box>
            {/* Header */}
            <Box sx={{
                background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.1)} 0%, ${alpha(beeColors.secondary.main, 0.1)} 100%)`,
                borderRadius: '20px',
                p: 4,
                mb: 4,
                position: 'relative',
                overflow: 'hidden'
            }}>
                <Box sx={{
                    position: 'absolute',
                    top: -20,
                    right: -20,
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: alpha(beeColors.primary.main, 0.1),
                    zIndex: 0
                }} />
                <Box sx={{
                    position: 'absolute',
                    bottom: -30,
                    left: -30,
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: alpha(beeColors.secondary.main, 0.1),
                    zIndex: 0
                }} />

                <Box sx={{ position: 'relative', zIndex: 1 }}>
                    <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
                        <Avatar sx={{
                            bgcolor: beeColors.primary.main,
                            width: 56,
                            height: 56
                        }}>
                            <PeopleIcon sx={{ fontSize: 28 }} />
                        </Avatar>
                        <Box>
                            <Typography variant="h4" component="h1" sx={{
                                fontWeight: 700,
                                color: beeColors.neutral.main,
                                mb: 0.5
                            }}>
                                Quản lý học sinh
                            </Typography>
                            <Typography variant="body1" sx={{
                                color: beeColors.neutral.light,
                                fontSize: '1.1rem'
                            }}>
                                Theo dõi và quản lý thông tin học sinh trong các khóa học
                            </Typography>
                        </Box>
                    </Stack>

                    <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
                        <Button
                            variant="contained"
                            startIcon={<PersonAddIcon />}
                            onClick={() => handleOpenDialog()}
                            sx={{
                                background: beeColors.background.gradient,
                                borderRadius: '12px',
                                px: 3,
                                py: 1.5,
                                textTransform: 'none',
                                fontSize: '1rem',
                                fontWeight: 600,
                                boxShadow: `0 4px 20px ${alpha(beeColors.primary.main, 0.3)}`,
                                '&:hover': {
                                    transform: 'translateY(-2px)',
                                    boxShadow: `0 6px 25px ${alpha(beeColors.primary.main, 0.4)}`
                                }
                            }}
                        >
                            Thêm học sinh mới
                        </Button>
                        <Button
                            variant="outlined"
                            startIcon={<FilterIcon />}
                            sx={{
                                borderRadius: '12px',
                                px: 3,
                                py: 1.5,
                                textTransform: 'none',
                                borderColor: beeColors.primary.main,
                                color: beeColors.primary.main,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.primary.main, 0.1),
                                    borderColor: beeColors.primary.main
                                }
                            }}
                        >
                            Bộ lọc nâng cao
                        </Button>
                    </Stack>
                </Box>
            </Box>

            {/* Statistics Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.1)} 0%, ${alpha(beeColors.primary.main, 0.05)} 100%)`,
                        border: `1px solid ${alpha(beeColors.primary.main, 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha(beeColors.primary.main, 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: beeColors.primary.main,
                                    width: 48,
                                    height: 48
                                }}>
                                    <GroupsIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Tổng học sinh
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: beeColors.primary.main
                                    }}>
                                        {students.length}
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha('#4CAF50', 0.1)} 0%, ${alpha('#4CAF50', 0.05)} 100%)`,
                        border: `1px solid ${alpha('#4CAF50', 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha('#4CAF50', 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: '#4CAF50',
                                    width: 48,
                                    height: 48
                                }}>
                                    <TrendingUpIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Đang học
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: '#4CAF50'
                                    }}>
                                        {students.filter(s => s.status === 'active').length}
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha(beeColors.secondary.main, 0.1)} 0%, ${alpha(beeColors.secondary.main, 0.05)} 100%)`,
                        border: `1px solid ${alpha(beeColors.secondary.main, 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha(beeColors.secondary.main, 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: beeColors.secondary.main,
                                    width: 48,
                                    height: 48
                                }}>
                                    <SchoolIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Khóa học
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: beeColors.secondary.main
                                    }}>
                                        {courses.length}
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha('#FF9800', 0.1)} 0%, ${alpha('#FF9800', 0.05)} 100%)`,
                        border: `1px solid ${alpha('#FF9800', 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha('#FF9800', 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: '#FF9800',
                                    width: 48,
                                    height: 48
                                }}>
                                    <EmojiEventsIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Điểm TB
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: '#FF9800'
                                    }}>
                                        {students.length > 0 ?
                                            (students.reduce((sum, s) => sum + parseFloat(s.avgScore), 0) / students.length).toFixed(1)
                                            : '0.0'
                                        }
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{
                p: 3,
                mb: 4,
                borderRadius: "16px",
                background: alpha(beeColors.background.paper, 0.8),
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                boxShadow: `0 4px 20px ${alpha(beeColors.neutral.main, 0.08)}`
            }}>
                <Typography variant="h6" sx={{
                    mb: 3,
                    fontWeight: 600,
                    color: beeColors.neutral.main
                }}>
                    Tìm kiếm và lọc học sinh
                </Typography>
                <Grid container spacing={3} alignItems="center">
                    <Grid size={{ xs: 12, md: 4 }}>
                        <TextField
                            fullWidth
                            size="small"
                            placeholder="Tìm kiếm học sinh..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            slotProps={{
                                input: {
                                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                                }
                            }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    borderRadius: '12px',
                                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.background.paper, 0.8)
                                    },
                                    '&.Mui-focused': {
                                        backgroundColor: beeColors.background.paper,
                                        boxShadow: `0 0 0 2px ${alpha(beeColors.primary.main, 0.2)}`
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Khóa học</InputLabel>
                            <Select
                                value={filterCourse}
                                onChange={(e) => setFilterCourse(e.target.value)}
                                label="Khóa học"
                                sx={{
                                    borderRadius: '12px',
                                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.background.paper, 0.8)
                                    },
                                    '&.Mui-focused': {
                                        backgroundColor: beeColors.background.paper
                                    }
                                }}
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {courses.map(course => (
                                    <MenuItem key={course.id} value={course.id}>{course.title}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Trạng thái</InputLabel>
                            <Select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value)}
                                label="Trạng thái"
                                sx={{
                                    borderRadius: '12px',
                                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.background.paper, 0.8)
                                    },
                                    '&.Mui-focused': {
                                        backgroundColor: beeColors.background.paper
                                    }
                                }}
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                <MenuItem value="active">Đang học</MenuItem>
                                <MenuItem value="inactive">Tạm nghỉ</MenuItem>
                                <MenuItem value="completed">Hoàn thành</MenuItem>
                                <MenuItem value="dropped">Bỏ học</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 2 }}>
                        <Button
                            fullWidth
                            // size="small"
                            variant="outlined"
                            startIcon={<FilterIcon />}
                            onClick={() => {
                                setSearchTerm('');
                                setFilterCourse('');
                                setFilterStatus('');
                            }}
                            sx={{
                                borderRadius: '12px',
                                // py: 1.5,
                                textTransform: 'none',
                                borderColor: beeColors.neutral.light,
                                color: beeColors.neutral.main,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.neutral.main, 0.1),
                                    borderColor: beeColors.neutral.main
                                }
                            }}
                        >
                            Xóa bộ lọc
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Students Display */}
            <Box sx={{ mb: 3 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                        fontWeight: 600,
                        color: beeColors.neutral.main
                    }}>
                        Danh sách học sinh ({filteredStudents.length})
                    </Typography>
                    <Stack direction="row" spacing={1}>
                        <Button
                            variant={viewMode === 'cards' ? 'contained' : 'outlined'}
                            size="small"
                            onClick={() => setViewMode('cards')}
                            sx={{ borderRadius: '8px' }}
                        >
                            <SpaceDashboardIcon />
                        </Button>
                        <Button
                            variant={viewMode === 'table' ? 'contained' : 'outlined'}
                            size="small"
                            onClick={() => setViewMode('table')}
                            sx={{ borderRadius: '8px' }}
                        >
                            <ViewListIcon />
                        </Button>
                    </Stack>
                </Stack>

                {viewMode === 'table' ? (
                    <TableContainer component={Paper} sx={{
                        borderRadius: "16px",
                        border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                        overflow: 'hidden'
                    }}>
                        <Table>
                            <TableHead sx={{
                                backgroundColor: alpha(beeColors.primary.main, 0.05)
                            }}>
                                <TableRow>
                                    <TableCell sx={{ fontWeight: 600 }}>Học sinh</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Khóa học</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Liên hệ</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Trạng thái</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Thống kê</TableCell>
                                    <TableCell align="center" sx={{ fontWeight: 600 }}>Thao tác</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {filteredStudents
                                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                    .map((student) => (
                                        <TableRow key={student.id} hover>
                                            <TableCell>
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                                                        {student.firstName.charAt(0)}
                                                    </Avatar>
                                                    <Box>
                                                        <Typography variant="subtitle2">
                                                            {student.firstName} {student.lastName}
                                                        </Typography>
                                                        <Typography variant="body2" color="text.secondary">
                                                            ID: {student.studentId}
                                                        </Typography>
                                                    </Box>
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2">
                                                    {student.courseName}
                                                </Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Box>
                                                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                                        <EmailIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                        {student.email}
                                                    </Typography>
                                                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                                        <PhoneIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                        {student.phone}
                                                    </Typography>
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusChip(student.status)}
                                            </TableCell>
                                            <TableCell>
                                                <Box>
                                                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                                                        <AssignmentIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                        {student.assignments} bài tập
                                                    </Typography>
                                                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                                                        <QuizIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                        {student.quizzes} bài kiểm tra
                                                    </Typography>
                                                    <Typography variant="body2">
                                                        <TrendingUpIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                        Điểm TB: {student.avgScore}
                                                    </Typography>
                                                </Box>
                                            </TableCell>
                                            <TableCell align="center">
                                                <Tooltip title="Chỉnh sửa">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleOpenDialog(student)}
                                                    >
                                                        <EditIcon />
                                                    </IconButton>
                                                </Tooltip>
                                                <Tooltip title="Xóa">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleDeleteStudent(student.id)}
                                                        color="error"
                                                    >
                                                        <DeleteIcon />
                                                    </IconButton>
                                                </Tooltip>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                ) : (
                    <Grid container spacing={3}>
                        {filteredStudents
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((student) => (
                                <Grid size={{ xs: 12, md: 6, lg: 4 }} key={student.id}>
                                    <Card sx={{
                                        borderRadius: "16px",
                                        border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                                        transition: 'all 0.3s ease',
                                        '&:hover': {
                                            transform: 'translateY(-4px)',
                                            boxShadow: `0 8px 25px ${alpha(beeColors.neutral.main, 0.15)}`,
                                            borderColor: alpha(beeColors.primary.main, 0.3)
                                        }
                                    }}>
                                        <CardContent sx={{ p: 3 }}>
                                            <Stack spacing={2}>
                                                <Stack direction="row" alignItems="center" spacing={2}>
                                                    <Avatar sx={{
                                                        bgcolor: beeColors.primary.main,
                                                        width: 48,
                                                        height: 48,
                                                        fontSize: '1.2rem',
                                                        fontWeight: 600
                                                    }}>
                                                        {student.firstName.charAt(0)}
                                                    </Avatar>
                                                    <Box sx={{ flex: 1 }}>
                                                        <Typography variant="h6" sx={{
                                                            fontWeight: 600,
                                                            color: beeColors.neutral.main,
                                                            mb: 0.5
                                                        }}>
                                                            {student.firstName} {student.lastName}
                                                        </Typography>
                                                        <Typography variant="body2" sx={{
                                                            color: beeColors.neutral.light
                                                        }}>
                                                            ID: {student.studentId}
                                                        </Typography>
                                                    </Box>
                                                    {getStatusChip(student.status)}
                                                </Stack>

                                                <Divider />

                                                <Box>
                                                    <Typography variant="body2" sx={{
                                                        color: beeColors.neutral.light,
                                                        mb: 1,
                                                        fontWeight: 500
                                                    }}>
                                                        Khóa học
                                                    </Typography>
                                                    <Chip
                                                        label={student.courseName}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: alpha(beeColors.secondary.main, 0.1),
                                                            color: beeColors.secondary.main,
                                                            fontWeight: 500
                                                        }}
                                                    />
                                                </Box>

                                                <Stack spacing={1}>
                                                    <Typography variant="body2" sx={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        color: beeColors.neutral.main
                                                    }}>
                                                        <EmailIcon sx={{ fontSize: 16, mr: 1, color: beeColors.neutral.light }} />
                                                        {student.email}
                                                    </Typography>
                                                    <Typography variant="body2" sx={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        color: beeColors.neutral.main
                                                    }}>
                                                        <PhoneIcon sx={{ fontSize: 16, mr: 1, color: beeColors.neutral.light }} />
                                                        {student.phone}
                                                    </Typography>
                                                </Stack>

                                                <Stack direction="row" spacing={2}>
                                                    <Box sx={{
                                                        flex: 1,
                                                        p: 2,
                                                        borderRadius: '8px',
                                                        backgroundColor: alpha(beeColors.primary.main, 0.05),
                                                        textAlign: 'center'
                                                    }}>
                                                        <Typography variant="body2" sx={{
                                                            color: beeColors.neutral.light,
                                                            mb: 0.5
                                                        }}>
                                                            Bài tập
                                                        </Typography>
                                                        <Typography variant="h6" sx={{
                                                            color: beeColors.primary.main,
                                                            fontWeight: 600
                                                        }}>
                                                            {student.assignments}
                                                        </Typography>
                                                    </Box>
                                                    <Box sx={{
                                                        flex: 1,
                                                        p: 2,
                                                        borderRadius: '8px',
                                                        backgroundColor: alpha('#FF9800', 0.05),
                                                        textAlign: 'center'
                                                    }}>
                                                        <Typography variant="body2" sx={{
                                                            color: beeColors.neutral.light,
                                                            mb: 0.5
                                                        }}>
                                                            Điểm TB
                                                        </Typography>
                                                        <Typography variant="h6" sx={{
                                                            color: '#FF9800',
                                                            fontWeight: 600
                                                        }}>
                                                            {student.avgScore}
                                                        </Typography>
                                                    </Box>
                                                </Stack>
                                            </Stack>
                                        </CardContent>
                                        <CardActions sx={{ px: 3, pb: 3, pt: 0 }}>
                                            <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
                                                <Button
                                                    size="small"
                                                    startIcon={<VisibilityIcon />}
                                                    sx={{
                                                        borderRadius: '8px',
                                                        textTransform: 'none',
                                                        flex: 1
                                                    }}
                                                >
                                                    Xem
                                                </Button>
                                                <Button
                                                    size="small"
                                                    startIcon={<EditIcon />}
                                                    onClick={() => handleOpenDialog(student)}
                                                    sx={{
                                                        borderRadius: '8px',
                                                        textTransform: 'none',
                                                        flex: 1
                                                    }}
                                                >
                                                    Sửa
                                                </Button>
                                                <IconButton
                                                    size="small"
                                                    color="error"
                                                    onClick={() => handleDeleteStudent(student.id)}
                                                    sx={{
                                                        borderRadius: '8px',
                                                        border: `1px solid ${alpha('#F44336', 0.3)}`,
                                                        '&:hover': {
                                                            backgroundColor: alpha('#F44336', 0.1)
                                                        }
                                                    }}
                                                >
                                                    <DeleteIcon fontSize="small" />
                                                </IconButton>
                                            </Stack>
                                        </CardActions>
                                    </Card>
                                </Grid>
                            ))}
                    </Grid>
                )}

                {/* Pagination */}
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    mt: 4,
                    p: 2,
                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                    borderRadius: '12px'
                }}>
                    <TablePagination
                        rowsPerPageOptions={[6, 12, 24]}
                        component="div"
                        count={filteredStudents.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={handleChangePage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        labelRowsPerPage="Số hàng mỗi trang:"
                        sx={{
                            '& .MuiTablePagination-toolbar': {
                                paddingLeft: 0,
                                paddingRight: 0
                            }
                        }}
                    />
                </Box>
            </Box>

            {/* Add/Edit Student Dialog */}
            <Dialog
                open={openDialog}
                onClose={handleCloseDialog}
                maxWidth="md"
                fullWidth
                slotProps={{
                    paper: {
                        sx: {
                            borderRadius: '16px',
                            boxShadow: `0 8px 32px ${alpha(beeColors.neutral.main, 0.15)}`
                        }
                    }
                }}
            >
                <DialogTitle sx={{
                    backgroundColor: beeColors.background.main,
                    color: beeColors.neutral.main,
                    fontWeight: 600
                }}>
                    {editingStudent ? 'Chỉnh sửa học sinh' : 'Thêm học sinh mới'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <CustomTextField
                                fullWidth
                                label="Họ *"
                                value={formData.firstName}
                                onChange={(e) => handleFormChange('firstName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <CustomTextField
                                fullWidth
                                label="Tên *"
                                value={formData.lastName}
                                onChange={(e) => handleFormChange('lastName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <CustomTextField
                                fullWidth
                                label="Email *"
                                type="email"
                                value={formData.email}
                                onChange={(e) => handleFormChange('email', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <CustomTextField
                                fullWidth
                                label="Số điện thoại"
                                value={formData.phone}
                                onChange={(e) => handleFormChange('phone', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Khóa học *</InputLabel>
                                <Select
                                    value={formData.courseId}
                                    onChange={(e) => handleFormChange('courseId', e.target.value)}
                                    label="Khóa học *"
                                    sx={{ borderRadius: '12px' }}
                                >
                                    {courses.map(course => (
                                        <MenuItem key={course.id} value={course.id}>{course.title}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <CustomTextField
                                fullWidth
                                label="Mã học sinh"
                                value={formData.studentId}
                                onChange={(e) => handleFormChange('studentId', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <CustomTextField
                                fullWidth
                                label="Tên phụ huynh"
                                value={formData.parentName}
                                onChange={(e) => handleFormChange('parentName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <CustomTextField
                                fullWidth
                                label="SĐT phụ huynh"
                                value={formData.parentPhone}
                                onChange={(e) => handleFormChange('parentPhone', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <CustomTextField
                                fullWidth
                                label="Địa chỉ"
                                multiline
                                rows={2}
                                value={formData.address}
                                onChange={(e) => handleFormChange('address', e.target.value)}
                                sx={{
                                    "& .MuiInputBase-inputMultiline": {
                                        resize: "vertical", // cho phép kéo resize chiều cao
                                    },
                                }}
                            />
                        </Grid>
                        <Grid size={{ xs: 12 }}>
                            <FormControl fullWidth>
                                <InputLabel>Trạng thái</InputLabel>
                                <Select
                                    value={formData.status}
                                    onChange={(e) => handleFormChange('status', e.target.value)}
                                    label="Trạng thái"
                                    sx={{ borderRadius: '12px' }}
                                >
                                    <MenuItem value="active">Đang học</MenuItem>
                                    <MenuItem value="inactive">Tạm nghỉ</MenuItem>
                                    <MenuItem value="completed">Hoàn thành</MenuItem>
                                    <MenuItem value="dropped">Bỏ học</MenuItem>
                                </Select>
                            </FormControl>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions sx={{
                    backgroundColor: beeColors.background.main,
                    p: 3,
                    gap: 2
                }}>
                    <CustomButton
                        variant="outlined"
                        onClick={handleCloseDialog}
                        sx={{
                            borderColor: alpha(beeColors.inherit.main, 0.3),
                            color: beeColors.inherit.main,
                            '&:hover': {
                                borderColor: beeColors.inherit.main,
                                backgroundColor: alpha(beeColors.inherit.main, 0.9)
                            }
                        }}
                    >
                        Hủy
                    </CustomButton>
                    <CustomButton
                        onClick={handleSaveStudent}
                        variant="contained"
                        startIcon={<PersonAddIcon />}
                    >
                        {editingStudent ? 'Cập nhật' : 'Thêm'}
                    </CustomButton>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box >
    );
}

export default StudentManagement;
