import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Paper,
    List,
    ListItem,
    ListItemText,
    Chip,
    LinearProgress,
    Avatar,
    IconButton,
    Button,
    alpha
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
    School as ClassIcon,
    People as StudentsIcon,
    Quiz as QuizIcon,
    Assignment as AssignmentIcon,
    TrendingUp as TrendingUpIcon,
    Notifications as NotificationIcon,
    Add as AddIcon,
    PlayArrow as PlayIcon,
    CheckCircle as CheckIcon,
    Schedule as ScheduleIcon
} from '@mui/icons-material';
import { DashboardCard, CourseCard } from '../../../Common/BeeCard';
import CustomButton, { beeColors } from '../../../Common/CustomButton';
import { useNavigate } from 'react-router-dom';

function TeacherDashboard({ user }) {
    const navigate = useNavigate();
    const [stats, setStats] = useState({
        totalCourses: 0,
        totalStudents: 0,
        publishedCourses: 0,
        totalContent: 0
    });

    const [recentActivities, setRecentActivities] = useState([]);
    const [upcomingDeadlines, setUpcomingDeadlines] = useState([]);

    useEffect(() => {
        // Giả lập dữ liệu - sau này sẽ thay bằng API calls
        setStats({
            totalCourses: 8,
            totalStudents: 120,
            publishedCourses: 5,
            totalContent: 45
        });

        setRecentActivities([
            { id: 1, type: 'course', title: 'Xuất bản khóa học Toán học nâng cao', time: '2 giờ trước', status: 'published' },
            { id: 2, type: 'lesson', title: 'Thêm bài học Phương trình bậc hai', time: '1 ngày trước', status: 'created' },
            { id: 3, type: 'quiz', title: 'Tạo bài kiểm tra Vật lý', time: '2 ngày trước', status: 'draft' },
        ]);

        setUpcomingDeadlines([
            { id: 1, title: 'Hoàn thành khóa học Hóa học', course: 'Hóa học cơ bản', deadline: '2024-01-15', priority: 'high' },
            { id: 2, title: 'Chấm bài kiểm tra Vật lý', course: 'Vật lý nâng cao', deadline: '2024-01-18', priority: 'medium' },
            { id: 3, title: 'Cập nhật nội dung Scratch', course: 'Lập trình Scratch', deadline: '2024-01-25', priority: 'low' },
        ]);
    }, []);

    const StatCard = ({ title, value, icon, color }) => (
        <Card sx={{ height: '100%', borderRadius: '10px' }}>
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                        <Typography color="textSecondary" gutterBottom variant="overline">
                            {title}
                        </Typography>
                        <Typography variant="h4" component="div" sx={{ color: color }}>
                            {value}
                        </Typography>
                    </Box>
                    <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
                        {icon}
                    </Avatar>
                </Box>
            </CardContent>
        </Card>
    );

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'success';
            case 'published': return 'info';
            case 'grading': return 'warning';
            default: return 'default';
        }
    };

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high': return 'error';
            case 'medium': return 'warning';
            case 'low': return 'success';
            default: return 'default';
        }
    };

    return (
        <Box>
            {/* Welcome Section */}
            <Box sx={{ mb: 4 }}>
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    flexWrap: 'wrap',
                    gap: 2
                }}>
                    <Box>
                        <Typography
                            variant="h3"
                            sx={{
                                fontWeight: 700,
                                color: beeColors.neutral.main,
                                mb: 1
                            }}
                        >
                            Chào mừng trở lại! 👋
                        </Typography>
                        <Typography
                            variant="h5"
                            sx={{
                                color: beeColors.neutral.light,
                                fontWeight: 400,
                                mb: 2
                            }}
                        >
                            {user?.first_name || 'Giáo viên'}
                        </Typography>
                        <Typography
                            variant="body1"
                            sx={{
                                color: beeColors.neutral.light,
                                fontSize: '1rem'
                            }}
                        >
                            Hôm nay là {new Date().toLocaleDateString('vi-VN', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            })}
                        </Typography>
                    </Box>
                    <CustomButton
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={() => navigate('/teacher/courses')}
                        sx={{
                            borderRadius: '12px',
                            px: 3,
                            py: 1.5
                        }}
                    >
                        Tạo khóa học mới
                    </CustomButton>
                </Box>
            </Box>

            {/* Statistics Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <DashboardCard
                        icon={<ClassIcon />}
                        title="Tổng khóa học"
                        value={stats.totalCourses}
                        subtitle="Khóa học đã tạo"
                        trend={{ value: "+12%", positive: true }}
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <DashboardCard
                        icon={<StudentsIcon />}
                        title="Tổng học sinh"
                        value={stats.totalStudents}
                        subtitle="Học sinh đang học"
                        trend={{ value: "+8%", positive: true }}
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <DashboardCard
                        icon={<QuizIcon />}
                        title="Đã xuất bản"
                        value={stats.publishedCourses}
                        subtitle="Khóa học công khai"
                        trend={{ value: "+5%", positive: true }}
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <DashboardCard
                        icon={<AssignmentIcon />}
                        title="Tổng nội dung"
                        value={stats.totalContent}
                        subtitle="Bài học & bài tập"
                        trend={{ value: "+15%", positive: true }}
                    />
                </Grid>
            </Grid>

            {/* Content Grid */}
            <Grid container spacing={3}>
                {/* Recent Activities */}
                <Grid item xs={12} md={8}>
                    <Paper sx={{ p: 3, borderRadius: '10px' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="h6">
                                Hoạt động gần đây
                            </Typography>
                            <IconButton>
                                <TrendingUpIcon />
                            </IconButton>
                        </Box>
                        <List>
                            {recentActivities.map((activity) => (
                                <ListItem key={activity.id} divider>
                                    <ListItemText
                                        primary={activity.title}
                                        secondary={activity.time}
                                    />
                                    <Chip
                                        label={activity.status}
                                        color={getStatusColor(activity.status)}
                                        size="small"
                                    />
                                </ListItem>
                            ))}
                        </List>
                        <Box sx={{ mt: 2, textAlign: 'center' }}>
                            <Button variant="outlined" size="small" sx={{ borderRadius: '10px' }}>
                                Xem tất cả hoạt động
                            </Button>
                        </Box>
                    </Paper>
                </Grid>

                {/* Upcoming Deadlines */}
                <Grid item xs={12} md={4}>
                    <Paper sx={{ p: 3, borderRadius: '10px' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="h6">
                                Deadline sắp tới
                            </Typography>
                            <IconButton>
                                <NotificationIcon />
                            </IconButton>
                        </Box>
                        <List>
                            {upcomingDeadlines.map((deadline) => (
                                <ListItem key={deadline.id} divider>
                                    <ListItemText
                                        primary={deadline.title}
                                        secondary={`${deadline.course} - ${deadline.deadline}`}
                                    />
                                    <Chip
                                        label={deadline.priority}
                                        color={getPriorityColor(deadline.priority)}
                                        size="small"
                                    />
                                </ListItem>
                            ))}
                        </List>
                        <Box sx={{ mt: 2, textAlign: 'center' }}>
                            <CustomButton
                                variant="outlined"
                                size="small"
                                sx={{ px: 3 }}
                            >
                                Xem lịch đầy đủ
                            </CustomButton>
                        </Box>
                    </Paper>
                </Grid>
            </Grid>

            {/* Quick Actions */}
            <Paper sx={{ p: 3, mt: 3, borderRadius: '10px' }}>
                <Typography variant="h6" gutterBottom>
                    Thao tác nhanh
                </Typography>
                <Grid container spacing={2}>
                    <Grid item>
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            sx={{ mr: 1, borderRadius: '10px' }}
                            onClick={() => navigate('/teacher/courses')}
                        >
                            Tạo khóa học mới
                        </Button>
                    </Grid>
                    <Grid item>
                        <Button
                            variant="outlined"
                            startIcon={<QuizIcon />}
                            sx={{ mr: 1, borderRadius: '10px' }}
                            onClick={() => navigate('/teacher/questions')}
                        >
                            Thêm câu hỏi
                        </Button>
                    </Grid>
                    <Grid item>
                        <Button
                            variant="outlined"
                            startIcon={<ClassIcon />}
                            sx={{ mr: 1, borderRadius: '10px' }}
                            onClick={() => navigate('/teacher/students')}
                        >
                            Quản lý học sinh
                        </Button>
                    </Grid>
                </Grid>
            </Paper>
        </Box>
    );
}

export default TeacherDashboard;
