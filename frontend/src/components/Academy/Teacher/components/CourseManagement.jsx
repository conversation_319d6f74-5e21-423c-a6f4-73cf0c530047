import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { elearningAPI, teacherAPI } from '../../../../services';
import {
    Box,
    Typography,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    Tabs,
    Tab,
    InputAdornment,
    alpha,
    Portal
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import CustomButton, { beeColors } from '../../../Common/CustomButton';
import CustomTextField from '../../../Common/CustomTextField';
import { CourseCard } from '../../../Common/BeeCard';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as ViewIcon,
    School as SchoolIcon,
    MenuBook as LessonIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    People as PeopleIcon,
    PlayArrow as PlayIcon,
    Publish as PublishIcon,
    Drafts as DraftIcon,
    MoreVert as MoreVertIcon,
    ContentCopy as CopyIcon,
    Search as SearchIcon,
    FilterList as FilterIcon,
    Sort as SortIcon,
    Schedule as ScheduleIcon,
    Star as StarIcon,
    TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import Loading from '../../../Common/Loading';

function CourseManagement({ user }) {
    const navigate = useNavigate();
    const [courses, setCourses] = useState([]);
    const [subjects, setSubjects] = useState([]);
    const [grades, setGrades] = useState([]);
    const [loading, setLoading] = useState(true);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingCourse, setEditingCourse] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // New states for enhanced UI
    const [searchTerm, setSearchTerm] = useState('');
    const [filterSubject, setFilterSubject] = useState('');
    const [filterGrade, setFilterGrade] = useState('');
    const [sortBy, setSortBy] = useState('created_at');

    // Form data for create/edit course
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        subject: '',
        grade: '',
        difficulty: 'beginner',
        price: 0,
        original_price: 0,
        duration: '',
        objectives: '',
        prerequisites: '',
        thumbnail: null
    });

    // Additional state for thumbnail preview
    const [thumbnailPreview, setThumbnailPreview] = useState(null);

    // States for create subject/grade dialogs
    const [openSubjectDialog, setOpenSubjectDialog] = useState(false);
    const [openGradeDialog, setOpenGradeDialog] = useState(false);
    const [newSubjectData, setNewSubjectData] = useState({
        name: '',
        description: ''
    });
    const [newGradeData, setNewGradeData] = useState({
        name: '',
        description: ''
    });

    useEffect(() => {
        loadData();

        // Cleanup function to revoke object URLs
        return () => {
            if (thumbnailPreview && thumbnailPreview.startsWith('blob:')) {
                URL.revokeObjectURL(thumbnailPreview);
            }
        };
    }, [thumbnailPreview]);

    const loadData = async () => {
        try {
            setLoading(true);

            // Load courses, subjects, and grades
            const [coursesRes, subjectsRes, gradesRes] = await Promise.all([
                elearningAPI.teacherAPI.getCourses(),
                elearningAPI.teacherAPI.getSubjects(),
                elearningAPI.teacherAPI.getGrades()
            ]);

            setCourses(coursesRes || []);
            setSubjects(subjectsRes || []);
            setGrades(gradesRes || []);
        } catch (error) {
            console.error('Error loading data:', error);

            // Try fallback to global functions
            try {
                const [subjectsRes, gradesRes] = await Promise.all([
                    elearningAPI.getSubjects(),
                    elearningAPI.getGrades()
                ]);
                setSubjects(subjectsRes || []);
                setGrades(gradesRes || []);
            } catch (fallbackError) {
                console.error('Fallback also failed:', fallbackError);
                setSubjects([]);
                setGrades([]);
            }

            setSnackbar({
                open: true,
                message: 'Lỗi khi tải dữ liệu',
                severity: 'error'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleOpenDialog = () => {
        setEditingCourse(null);
        setFormData({
            title: '',
            description: '',
            subject: '',
            grade: '',
            difficulty: 'beginner',
            price: 0,
            original_price: 0,
            duration: '',
            objectives: '',
            prerequisites: '',
            thumbnail: null
        });
        setThumbnailPreview(null);
        setOpenDialog(true);
        setTabValue(0);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingCourse(null);
        setTabValue(0);

        // Cleanup thumbnail preview URL to prevent memory leaks
        if (thumbnailPreview && thumbnailPreview.startsWith('blob:')) {
            URL.revokeObjectURL(thumbnailPreview);
        }
        setThumbnailPreview(null);
    };

    const handleEditCourse = (course) => {
        setEditingCourse(course);
        setFormData({
            title: course.title || '',
            description: course.description || '',
            subject: course.subject?.id || course.subject_id || '',
            grade: course.grade?.id || course.grade_id || '',
            difficulty: course.difficulty || 'beginner',
            price: course.price || 0,
            original_price: course.original_price || 0,
            duration: course.duration || '',
            objectives: Array.isArray(course.objectives) ? course.objectives.join('\n') : (course.objectives || ''),
            prerequisites: Array.isArray(course.prerequisites) ? course.prerequisites.join('\n') : (course.prerequisites || ''),
            thumbnail: null // Don't set existing thumbnail as file
        });

        // Set thumbnail preview if course has existing thumbnail
        if (course.thumbnail) {
            setThumbnailPreview(course.thumbnail);
        } else {
            setThumbnailPreview(null);
        }
        setOpenDialog(true);
        setTabValue(0);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleThumbnailChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                setSnackbar({
                    open: true,
                    message: 'Vui lòng chọn file ảnh hợp lệ',
                    severity: 'error'
                });
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                setSnackbar({
                    open: true,
                    message: 'Kích thước file không được vượt quá 5MB',
                    severity: 'error'
                });
                return;
            }

            setFormData(prev => ({
                ...prev,
                thumbnail: file
            }));

            // Create preview URL
            const previewUrl = URL.createObjectURL(file);
            setThumbnailPreview(previewUrl);
        }
    };

    const handleRemoveThumbnail = () => {
        setFormData(prev => ({
            ...prev,
            thumbnail: null
        }));
        setThumbnailPreview(null);
    };

    const handleSaveCourse = async () => {
        try {
            setLoading(true);

            // Prepare data for API
            const apiData = new FormData();

            // Add text fields
            apiData.append('title', formData.title);
            apiData.append('description', formData.description);
            apiData.append('subject', formData.subject);
            apiData.append('grade', formData.grade);
            apiData.append('difficulty', formData.difficulty);
            apiData.append('price', formData.price);
            apiData.append('original_price', formData.original_price);
            apiData.append('duration', formData.duration);

            // Convert arrays to JSON strings for objectives and prerequisites
            const objectives = formData.objectives ? formData.objectives.split('\n').filter(item => item.trim()) : [];
            const prerequisites = formData.prerequisites ? formData.prerequisites.split('\n').filter(item => item.trim()) : [];
            apiData.append('objectives', JSON.stringify(objectives));
            apiData.append('prerequisites', JSON.stringify(prerequisites));

            // Add thumbnail if selected
            if (formData.thumbnail) {
                apiData.append('thumbnail', formData.thumbnail);
            }

            if (editingCourse) {
                // Update existing course
                await elearningAPI.teacherAPI.updateCourse(editingCourse.id, apiData);

                // Clear filters if subject or grade was changed to ensure the updated course is visible
                const originalSubject = String(editingCourse.subject?.id || editingCourse.subject_id || '');
                const originalGrade = String(editingCourse.grade?.id || editingCourse.grade_id || '');
                const newSubject = String(formData.subject || '');
                const newGrade = String(formData.grade || '');

                if (newSubject !== originalSubject || newGrade !== originalGrade) {
                    setFilterSubject('');
                    setFilterGrade('');
                }
            } else {
                // Create new course
                await elearningAPI.teacherAPI.createCourse(apiData);
            }

            // Reload courses
            await loadData();

            handleCloseDialog();
            setSnackbar({
                open: true,
                message: editingCourse ? 'Cập nhật khóa học thành công' : 'Tạo khóa học thành công',
                severity: 'success'
            });
        } catch (error) {
            console.error('Error saving course:', error);
            setSnackbar({
                open: true,
                message: 'Lỗi khi lưu khóa học',
                severity: 'error'
            });
        } finally {
            setLoading(false);
        }
    };

    const handleOpenCourseBuilder = (courseId) => {
        navigate(`/teacher/courses/${courseId}/builder`);
    };

    const handleOpenCoursePreview = (courseId) => {
        navigate(`/teacher/courses/${courseId}/preview`);
    };

    // Handlers for creating new subject and grade
    const handleCreateSubject = async () => {
        try {
            if (!newSubjectData.name.trim()) {
                setSnackbar({
                    open: true,
                    message: 'Vui lòng nhập tên môn học',
                    severity: 'error'
                });
                return;
            }

            const response = await teacherAPI.createSubject(newSubjectData);

            // Reload subjects
            const subjectsRes = await teacherAPI.getSubjects();
            setSubjects(subjectsRes || []);

            // Auto-select the new subject
            setFormData(prev => ({
                ...prev,
                subject: response.id
            }));

            // Reset form and close dialog
            setNewSubjectData({ name: '', description: '' });
            setOpenSubjectDialog(false);

            setSnackbar({
                open: true,
                message: 'Tạo môn học thành công!',
                severity: 'success'
            });
        } catch (error) {
            console.error('Error creating subject:', error);
            setSnackbar({
                open: true,
                message: 'Lỗi khi tạo môn học',
                severity: 'error'
            });
        }
    };

    const handleCreateGrade = async () => {
        try {
            if (!newGradeData.name.trim()) {
                setSnackbar({
                    open: true,
                    message: 'Vui lòng nhập tên khối lớp',
                    severity: 'error'
                });
                return;
            }

            const response = await teacherAPI.createGrade(newGradeData);

            // Reload grades
            const gradesRes = await teacherAPI.getGrades();
            setGrades(gradesRes || []);

            // Auto-select the new grade
            setFormData(prev => ({
                ...prev,
                grade: response.id
            }));

            // Reset form and close dialog
            setNewGradeData({ name: '', description: '' });
            setOpenGradeDialog(false);

            setSnackbar({
                open: true,
                message: 'Tạo khối lớp thành công!',
                severity: 'success'
            });
        } catch (error) {
            console.error('Error creating grade:', error);
            setSnackbar({
                open: true,
                message: 'Lỗi khi tạo khối lớp',
                severity: 'error'
            });
        }
    };

    // Filter and sort courses
    const filteredCourses = courses.filter(course => {
        const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            course.description?.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesSubject = !filterSubject ||
            (course.subject?.id === filterSubject) ||
            (course.subject_id === filterSubject);
        const matchesGrade = !filterGrade ||
            (course.grade?.id === filterGrade) ||
            (course.grade_id === filterGrade);

        return matchesSearch && matchesSubject && matchesGrade;
    }).sort((a, b) => {
        switch (sortBy) {
            case 'title':
                return a.title.localeCompare(b.title);
            case 'created_at':
                return new Date(b.created_at) - new Date(a.created_at);
            case 'updated_at':
                return new Date(b.updated_at) - new Date(a.updated_at);
            default:
                return 0;
        }
    });

    const getStatusChip = (status) => {
        return status === 'published'
            ? <Chip label="Đã xuất bản" color="success" size="small" icon={<PublishIcon />} />
            : <Chip label="Bản nháp" color="warning" size="small" icon={<DraftIcon />} />;
    };

    if (loading) {
        return (
            <Loading />
        );
    }

    const formatPrice = (price) => {
        if (price === 0) return 'Miễn phí';
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    };

    return (
        <Box sx={{ p: { xs: 2, sm: 3 } }}>
            {/* Header Section */}
            <Box sx={{ mb: 4 }}>
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    flexWrap: 'wrap',
                    gap: 2,
                    mb: 3
                }}>
                    <Box>
                        <Typography
                            variant="h3"
                            sx={{
                                fontWeight: 700,
                                color: beeColors.neutral.main,
                                mb: 1
                            }}
                        >
                            Quản lý khóa học 📚
                        </Typography>
                        <Typography
                            variant="body1"
                            sx={{
                                color: beeColors.neutral.light,
                                fontSize: '1rem'
                            }}
                        >
                            Tạo và quản lý các khóa học STEM của bạn
                        </Typography>
                    </Box>
                    <CustomButton
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleOpenDialog}
                        sx={{
                            px: 3,
                            py: 1.5
                        }}
                    >
                        Tạo khóa học mới
                    </CustomButton>
                </Box>

                {/* Search and Filter Bar */}
                <Box sx={{
                    display: 'flex',
                    gap: 2,
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    p: 3,
                    backgroundColor: beeColors.background.paper,
                    borderRadius: '16px',
                    boxShadow: `0 4px 16px ${alpha(beeColors.neutral.main, 0.08)}`,
                    border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`
                }}>
                    <CustomTextField
                        placeholder="Tìm kiếm khóa học..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        size="small"
                        sx={{ minWidth: 300, flexGrow: 1 }}
                        slotProps={{
                            input: {
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon sx={{ color: beeColors.neutral.light }} />
                                    </InputAdornment>
                                ),
                            }
                        }}
                    />

                    <FormControl size="small" sx={{ minWidth: 150 }}>
                        <InputLabel>Môn học</InputLabel>
                        <Select
                            value={filterSubject}
                            onChange={(e) => setFilterSubject(e.target.value)}
                            label="Môn học"
                            sx={{ borderRadius: '12px' }}
                            disabled={subjects.length === 0}
                        >
                            <MenuItem value="">Tất cả</MenuItem>
                            {subjects.map((subject) => (
                                <MenuItem key={subject.id} value={subject.id}>
                                    {subject.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <FormControl size="small" sx={{ minWidth: 120 }}>
                        <InputLabel>Lớp</InputLabel>
                        <Select
                            value={filterGrade}
                            onChange={(e) => setFilterGrade(e.target.value)}
                            label="Lớp"
                            sx={{ borderRadius: '12px' }}
                            disabled={grades.length === 0}
                        >
                            <MenuItem value="">Tất cả</MenuItem>
                            {grades.map((grade) => (
                                <MenuItem key={grade.id} value={grade.id}>
                                    {grade.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <FormControl size="small" sx={{ minWidth: 150 }}>
                        <InputLabel>Sắp xếp</InputLabel>
                        <Select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value)}
                            label="Sắp xếp"
                            sx={{ borderRadius: '12px' }}
                        >
                            <MenuItem value="created_at">Mới nhất</MenuItem>
                            <MenuItem value="updated_at">Cập nhật gần đây</MenuItem>
                            <MenuItem value="title">Tên A-Z</MenuItem>
                        </Select>
                    </FormControl>

                    <Tooltip title="Lọc">
                        <IconButton
                            sx={{
                                color: beeColors.neutral.light,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.primary.main, 0.1),
                                    color: beeColors.primary.main
                                }
                            }}
                        >
                            <FilterIcon />
                        </IconButton>
                    </Tooltip>
                </Box>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Box sx={{
                        p: 3,
                        borderRadius: '16px',
                        background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.primary.light} 100%)`,
                        color: 'white',
                        textAlign: 'center',
                        boxShadow: `0 8px 32px ${alpha(beeColors.primary.main, 0.3)}`
                    }}>
                        <SchoolIcon sx={{ fontSize: 40, mb: 1 }} />
                        <Typography variant="h3" sx={{ fontWeight: 700, mb: 0.5 }}>
                            {courses.length}
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.9 }}>
                            Tổng khóa học
                        </Typography>
                    </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Box sx={{
                        p: 3,
                        borderRadius: '16px',
                        background: `linear-gradient(135deg, ${beeColors.secondary.main} 0%, ${beeColors.secondary.light} 100%)`,
                        color: 'white',
                        textAlign: 'center',
                        boxShadow: `0 8px 32px ${alpha(beeColors.secondary.main, 0.3)}`
                    }}>
                        <PublishIcon sx={{ fontSize: 40, mb: 1 }} />
                        <Typography variant="h3" sx={{ fontWeight: 700, mb: 0.5 }}>
                            {courses.filter(c => c.status === 'published').length}
                        </Typography>
                        <Typography variant="body2" sx={{ opacity: 0.9 }}>
                            Đã xuất bản
                        </Typography>
                    </Box>
                </Grid>
            </Grid>

            {/* Courses Grid */}
            <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography
                        variant="h5"
                        sx={{
                            fontWeight: 600,
                            color: beeColors.neutral.main
                        }}
                    >
                        Khóa học của bạn ({filteredCourses.length})
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                        <Chip
                            label={`${filteredCourses.filter(c => c.status === 'published').length} đã xuất bản`}
                            color="success"
                            size="small"
                        />
                        <Chip
                            label={`${filteredCourses.filter(c => c.status === 'draft').length} bản nháp`}
                            color="warning"
                            size="small"
                        />
                    </Box>
                </Box>

                {filteredCourses.length === 0 ? (
                    <Box sx={{
                        textAlign: 'center',
                        py: 8,
                        backgroundColor: beeColors.background.paper,
                        borderRadius: '16px',
                        border: `2px dashed ${alpha(beeColors.neutral.main, 0.2)}`
                    }}>
                        <SchoolIcon sx={{ fontSize: 64, color: beeColors.neutral.light, mb: 2 }} />
                        <Typography variant="h6" sx={{ color: beeColors.neutral.main, mb: 1 }}>
                            {searchTerm || filterSubject || filterGrade ? 'Không tìm thấy khóa học' : 'Chưa có khóa học nào'}
                        </Typography>
                        <Typography variant="body2" sx={{ color: beeColors.neutral.light, mb: 3 }}>
                            {searchTerm || filterSubject || filterGrade
                                ? 'Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm'
                                : 'Hãy tạo khóa học đầu tiên của bạn'
                            }
                        </Typography>
                        {!searchTerm && !filterSubject && !filterGrade && (
                            <CustomButton
                                variant="contained"
                                startIcon={<AddIcon />}
                                onClick={handleOpenDialog}
                            >
                                Tạo khóa học mới
                            </CustomButton>
                        )}
                    </Box>
                ) : (
                    <Grid container spacing={3}>
                        {filteredCourses.map((course) => (
                            <Grid size={{ xs: 12, md: 6, lg: 4 }} key={course.id}>
                                <CourseCard
                                    title={course.title}
                                    description={course.description.length > 100 ? course.description.slice(0, 100) + "..." : course.description}
                                    image={course.thumbnail}
                                    subject={course.subject?.name || course.subject}
                                    students={course.enrolledStudents || 0}
                                    actions={
                                        <CardActions sx={{
                                            justifyContent: 'space-between',
                                            px: 3,
                                            py: 2
                                        }}>
                                            <Box sx={{ display: 'flex', gap: 1 }}>
                                                {getStatusChip(course.status)}
                                                <Chip
                                                    label={course.grade?.name || course.grade}
                                                    variant="outlined"
                                                    size="small"
                                                    sx={{
                                                        borderColor: alpha(beeColors.primary.main, 0.3),
                                                        color: beeColors.primary.main
                                                    }}
                                                />
                                            </Box>
                                            <Box sx={{ display: 'flex', gap: 0.5 }}>
                                                <Tooltip title="Xem trước">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleOpenCoursePreview(course.id)}
                                                        sx={{
                                                            color: beeColors.secondary.main,
                                                            '&:hover': {
                                                                backgroundColor: alpha(beeColors.secondary.main, 0.1)
                                                            }
                                                        }}
                                                    >
                                                        <ViewIcon />
                                                    </IconButton>
                                                </Tooltip>
                                                <Tooltip title="Xây dựng khóa học">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleOpenCourseBuilder(course.id)}
                                                        sx={{
                                                            color: beeColors.primary.main,
                                                            '&:hover': {
                                                                backgroundColor: alpha(beeColors.primary.main, 0.1)
                                                            }
                                                        }}
                                                    >
                                                        <EditIcon />
                                                    </IconButton>
                                                </Tooltip>
                                                <Tooltip title="Chỉnh sửa">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleEditCourse(course)}
                                                        sx={{
                                                            color: beeColors.accent.main,
                                                            '&:hover': {
                                                                backgroundColor: alpha(beeColors.accent.main, 0.1)
                                                            }
                                                        }}
                                                    >
                                                        <MoreVertIcon />
                                                    </IconButton>
                                                </Tooltip>
                                            </Box>
                                        </CardActions>
                                    }
                                />
                            </Grid>
                        ))}
                    </Grid>
                )}
            </Box>

            {/* Create/Edit Course Dialog */}
            <Dialog
                open={openDialog}
                onClose={handleCloseDialog}
                maxWidth="md"
                fullWidth
                slotProps={{
                    paper: {
                        sx: {
                            borderRadius: '16px',
                            boxShadow: `0 8px 32px ${alpha(beeColors.neutral.main, 0.15)}`
                        }
                    }
                }}
            >
                <DialogTitle sx={{
                    backgroundColor: beeColors.background.main,
                    color: beeColors.neutral.main,
                    fontWeight: 600
                }}>
                    {editingCourse ? 'Chỉnh sửa khóa học' : 'Tạo khóa học mới'}
                </DialogTitle>
                <DialogContent sx={{ backgroundColor: beeColors.background.main }}>
                    <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)} sx={{ mb: 3 }}>
                        <Tab label="Thông tin cơ bản" />
                        <Tab label="Mục tiêu & Yêu cầu" />
                    </Tabs>

                    {tabValue === 0 && (
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
                            <CustomTextField
                                label="Tên khóa học"
                                value={formData.title}
                                onChange={(e) => handleFormChange('title', e.target.value)}
                                fullWidth
                                required
                            />

                            <CustomTextField
                                label="Mô tả khóa học"
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                                fullWidth
                                multiline
                                rows={3}
                                sx={{
                                    "& .MuiInputBase-inputMultiline": {
                                        resize: "vertical", // cho phép kéo resize chiều cao
                                    },
                                }}
                            />

                            {/* Thumbnail Upload */}
                            <Box>
                                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                                    Ảnh đại diện khóa học
                                </Typography>
                                <Box sx={{
                                    border: `2px dashed ${alpha(beeColors.primary.main, 0.3)}`,
                                    borderRadius: '12px',
                                    p: 3,
                                    textAlign: 'center',
                                    backgroundColor: alpha(beeColors.primary.main, 0.02),
                                    position: 'relative'
                                }}>
                                    {thumbnailPreview ? (
                                        <Box sx={{ position: 'relative' }}>
                                            <Box
                                                component="img"
                                                src={thumbnailPreview}
                                                alt="Thumbnail preview"
                                                sx={{
                                                    width: '100%',
                                                    maxWidth: 300,
                                                    height: 200,
                                                    objectFit: 'cover',
                                                    borderRadius: '8px',
                                                    mb: 2
                                                }}
                                            />
                                            <IconButton
                                                onClick={handleRemoveThumbnail}
                                                sx={{
                                                    position: 'absolute',
                                                    top: 8,
                                                    right: 8,
                                                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                                    '&:hover': {
                                                        backgroundColor: 'rgba(255, 255, 255, 1)'
                                                    }
                                                }}
                                                size="small"
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Box>
                                    ) : (
                                        <Box>
                                            <Box sx={{ mb: 2 }}>
                                                <SchoolIcon sx={{
                                                    fontSize: 48,
                                                    color: alpha(beeColors.primary.main, 0.5)
                                                }} />
                                            </Box>
                                            <Typography variant="body2" sx={{ mb: 1 }}>
                                                Kéo thả ảnh vào đây hoặc nhấn để chọn
                                            </Typography>
                                            <Typography variant="caption" sx={{ color: beeColors.neutral.light }}>
                                                Định dạng: JPG, PNG, GIF (tối đa 5MB)
                                            </Typography>
                                        </Box>
                                    )}
                                    <input
                                        type="file"
                                        accept="image/*"
                                        onChange={handleThumbnailChange}
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            width: '100%',
                                            height: '100%',
                                            opacity: 0,
                                            cursor: 'pointer'
                                        }}
                                    />
                                </Box>
                            </Box>

                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, flex: 1 }}>
                                    <FormControl fullWidth>
                                        <InputLabel>Môn học</InputLabel>
                                        <Select
                                            value={formData.subject}
                                            onChange={(e) => handleFormChange('subject', e.target.value)}
                                            label="Môn học"
                                            sx={{ borderRadius: '12px' }}
                                            disabled={subjects.length === 0}
                                        >
                                            {subjects.length === 0 ? (
                                                <MenuItem disabled>Đang tải môn học...</MenuItem>
                                            ) : (
                                                subjects.map((subject) => (
                                                    <MenuItem key={subject.id} value={subject.id}>
                                                        {subject.name}
                                                    </MenuItem>
                                                ))
                                            )}
                                        </Select>
                                    </FormControl>
                                    <Tooltip title="Tạo môn học mới">
                                        <IconButton
                                            onClick={() => setOpenSubjectDialog(true)}
                                            sx={{
                                                mt: 1,
                                                color: beeColors.primary.main,
                                                '&:hover': {
                                                    backgroundColor: alpha(beeColors.primary.main, 0.1)
                                                }
                                            }}
                                        >
                                            <AddIcon />
                                        </IconButton>
                                    </Tooltip>
                                </Box>

                                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, flex: 1 }}>
                                    <FormControl fullWidth>
                                        <InputLabel>Lớp</InputLabel>
                                        <Select
                                            value={formData.grade}
                                            onChange={(e) => handleFormChange('grade', e.target.value)}
                                            label="Lớp"
                                            sx={{ borderRadius: '12px' }}
                                            disabled={grades.length === 0}
                                        >
                                            {grades.length === 0 ? (
                                                <MenuItem disabled>Đang tải lớp học...</MenuItem>
                                            ) : (
                                                grades.map((grade) => (
                                                    <MenuItem key={grade.id} value={grade.id}>
                                                        {grade.name}
                                                    </MenuItem>
                                                ))
                                            )}
                                        </Select>
                                    </FormControl>
                                    <Tooltip title="Tạo lớp mới">
                                        <IconButton
                                            onClick={() => setOpenGradeDialog(true)}
                                            sx={{
                                                mt: 1,
                                                color: beeColors.primary.main,
                                                '&:hover': {
                                                    backgroundColor: alpha(beeColors.primary.main, 0.1)
                                                }
                                            }}
                                        >
                                            <AddIcon />
                                        </IconButton>
                                    </Tooltip>
                                </Box>
                            </Box>

                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <FormControl fullWidth>
                                    <InputLabel>Độ khó</InputLabel>
                                    <Select
                                        value={formData.difficulty}
                                        onChange={(e) => handleFormChange('difficulty', e.target.value)}
                                        label="Độ khó"
                                        sx={{ borderRadius: '12px' }}
                                    >
                                        <MenuItem value="beginner">Cơ bản</MenuItem>
                                        <MenuItem value="intermediate">Trung bình</MenuItem>
                                        <MenuItem value="advanced">Nâng cao</MenuItem>
                                    </Select>
                                </FormControl>

                                <CustomTextField
                                    label={"Giá gốc: " + formatPrice(formData.original_price)}
                                    type="number"
                                    value={formData.original_price}
                                    onChange={(e) => handleFormChange('original_price', Number(e.target.value))}
                                    fullWidth
                                />

                                <CustomTextField
                                    label={"Giá: " + formatPrice(formData.price)}
                                    type="number"
                                    value={formData.price}
                                    onChange={(e) => handleFormChange('price', Number(e.target.value))}
                                    fullWidth
                                />
                            </Box>

                            <CustomTextField
                                label="Thời lượng (ví dụ: 8 tuần, 20 giờ)"
                                value={formData.duration}
                                onChange={(e) => handleFormChange('duration', e.target.value)}
                                fullWidth
                            />
                        </Box>
                    )}

                    {tabValue === 1 && (
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 2 }}>
                            <CustomTextField
                                label="Mục tiêu khóa học"
                                value={formData.objectives}
                                onChange={(e) => handleFormChange('objectives', e.target.value)}
                                fullWidth
                                multiline
                                rows={4}
                                placeholder="Mỗi mục tiêu trên một dòng&#10;Ví dụ:&#10;- Hiểu được khái niệm cơ bản về lập trình&#10;- Có thể tạo ra các chương trình đơn giản"
                                helperText="Mỗi dòng sẽ là một mục tiêu riêng biệt"
                                sx={{
                                    "& .MuiInputBase-inputMultiline": {
                                        resize: "vertical", // cho phép kéo resize chiều cao
                                    },
                                }}
                            />

                            <CustomTextField
                                label="Yêu cầu tiên quyết"
                                value={formData.prerequisites}
                                onChange={(e) => handleFormChange('prerequisites', e.target.value)}
                                fullWidth
                                multiline
                                rows={3}
                                placeholder="Mỗi yêu cầu trên một dòng&#10;Ví dụ:&#10;- Biết sử dụng máy tính cơ bản&#10;- Có kiến thức toán học lớp 6"
                                helperText="Mỗi dòng sẽ là một yêu cầu riêng biệt"
                                sx={{
                                    "& .MuiInputBase-inputMultiline": {
                                        resize: "vertical", // cho phép kéo resize chiều cao
                                    },
                                }}
                            />
                        </Box>
                    )}
                </DialogContent>
                <DialogActions sx={{
                    backgroundColor: beeColors.background.main,
                    p: 3,
                    gap: 2
                }}>
                    <CustomButton
                        variant="outlined"
                        onClick={handleCloseDialog}
                        sx={{
                            borderColor: alpha(beeColors.inherit.main, 0.3),
                            color: beeColors.inherit.main,
                            '&:hover': {
                                borderColor: beeColors.inherit.main,
                                backgroundColor: alpha(beeColors.inherit.main, 0.9)
                            }
                        }}
                    >
                        Hủy
                    </CustomButton>
                    <CustomButton
                        variant="contained"
                        onClick={handleSaveCourse}
                        disabled={loading}
                    >
                        {loading ? 'Đang lưu...' : (editingCourse ? 'Cập nhật' : 'Tạo khóa học')}
                    </CustomButton>
                </DialogActions>
            </Dialog>

            {/* Create Subject Dialog */}
            <Dialog
                open={openSubjectDialog}
                onClose={() => setOpenSubjectDialog(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <SchoolIcon sx={{ color: beeColors.primary.main }} />
                        Tạo môn học mới
                    </Box>
                </DialogTitle>
                <DialogContent>
                    <Box sx={{ pt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <CustomTextField
                            label="Tên môn học"
                            value={newSubjectData.name}
                            onChange={(e) => setNewSubjectData(prev => ({ ...prev, name: e.target.value }))}
                            fullWidth
                            required
                            placeholder="Ví dụ: Toán học, Vật lý, Hóa học..."
                        />
                        <CustomTextField
                            label="Mô tả"
                            value={newSubjectData.description}
                            onChange={(e) => setNewSubjectData(prev => ({ ...prev, description: e.target.value }))}
                            fullWidth
                            multiline
                            rows={3}
                            placeholder="Mô tả ngắn về môn học này..."
                            sx={{
                                "& .MuiInputBase-inputMultiline": {
                                    resize: "vertical", // cho phép kéo resize chiều cao
                                },
                            }}
                        />
                    </Box>
                </DialogContent>
                <DialogActions sx={{ p: 3, pt: 1 }}>
                    <CustomButton
                        variant="outlined"
                        onClick={() => {
                            setNewSubjectData({ name: '', description: '' });
                            setOpenSubjectDialog(false);
                        }}
                        sx={{
                            borderColor: alpha(beeColors.inherit.main, 0.3),
                            color: beeColors.inherit.main,
                            '&:hover': {
                                borderColor: beeColors.inherit.main,
                                backgroundColor: alpha(beeColors.inherit.main, 0.9)
                            }
                        }}
                    >
                        Hủy
                    </CustomButton>
                    <CustomButton
                        variant="contained"
                        onClick={handleCreateSubject}
                        disabled={!newSubjectData.name.trim()}
                    >
                        Tạo môn học
                    </CustomButton>
                </DialogActions>
            </Dialog>

            {/* Create Grade Dialog */}
            <Dialog
                open={openGradeDialog}
                onClose={() => setOpenGradeDialog(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <SchoolIcon sx={{ color: beeColors.primary.main }} />
                        Tạo lớp mới
                    </Box>
                </DialogTitle>
                <DialogContent>
                    <Box sx={{ pt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <CustomTextField
                            label="Tên lớp"
                            value={newGradeData.name}
                            onChange={(e) => setNewGradeData(prev => ({ ...prev, name: e.target.value }))}
                            fullWidth
                            required
                            placeholder="Ví dụ: Lớp 1, Lớp 6, Lớp 10..."
                        />
                        <CustomTextField
                            label="Mô tả"
                            value={newGradeData.description}
                            onChange={(e) => setNewGradeData(prev => ({ ...prev, description: e.target.value }))}
                            fullWidth
                            multiline
                            rows={3}
                            placeholder="Mô tả ngắn về lớp học này..."
                            sx={{
                                "& .MuiInputBase-inputMultiline": {
                                    resize: "vertical", // cho phép kéo resize chiều cao
                                },
                            }}
                        />
                    </Box>
                </DialogContent>
                <DialogActions sx={{ p: 3, pt: 1 }}>
                    <CustomButton
                        variant="outlined"
                        onClick={() => {
                            setNewGradeData({ name: '', description: '' });
                            setOpenGradeDialog(false);
                        }}
                    >
                        Hủy
                    </CustomButton>
                    <CustomButton
                        variant="contained"
                        onClick={handleCreateGrade}
                        disabled={!newGradeData.name.trim()}
                    >
                        Tạo lớp
                    </CustomButton>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Portal>
                <Snackbar
                    open={snackbar.open}
                    autoHideDuration={6000}
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                    sx={{
                        zIndex: 1500, // Higher than Material-UI components
                        // position: 'fixed !important',
                        // top: '24px !important',
                        // right: '24px !important',
                        '& .MuiSnackbarContent-root': {
                            zIndex: 1500
                        }
                    }}
                >
                    <Alert
                        onClose={() => setSnackbar({ ...snackbar, open: false })}
                        severity={snackbar.severity}
                        sx={{
                            borderRadius: '12px',
                            zIndex: 1500,
                            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
                            minWidth: '300px',
                            position: 'relative'
                        }}
                    >
                        {snackbar.message}
                    </Alert>
                </Snackbar>
            </Portal>
        </Box>
    );
}

export default CourseManagement;
