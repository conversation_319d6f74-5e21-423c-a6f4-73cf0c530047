import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Box,
    Typography,
    Grid2 as Grid,
    Card,
    CardContent,
    CardMedia,
    Paper,
    List,
    ListItem,
    ListItemText,
    Chip,
    LinearProgress,
    Avatar,
    IconButton,
    Button,
    Alert,
    CircularProgress,
    Container,
    alpha,
    Divider,
    Stack,
    CardActions
} from '@mui/material';
import {
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    TrendingUp as TrendingUpIcon,
    Schedule as ScheduleIcon,
    PlayArrow as PlayIcon,
    Notifications as NotificationIcon,
    Star as StarIcon,
    BookmarkBorder as BookmarkIcon,
    ArrowForward as ArrowForwardIcon,
    EmojiEvents as TrophyIcon
} from '@mui/icons-material';
import { elearningAPI } from '../../../../services';
// import { beeColors } from '../../../../styles/colors';
import { beeColors } from '../../../Common/CustomButton';
import './StudentDashboard.css';

function StudentDashboard({ user }) {
    const navigate = useNavigate();
    const [stats, setStats] = useState({
        enrolledCourses: 0,
        completedAssignments: 0,
        pendingQuizzes: 0,
        turboWarpProjects: 0
    });

    const [recentActivities, setRecentActivities] = useState([]);
    const [upcomingDeadlines, setUpcomingDeadlines] = useState([]);
    const [enrolledCourses, setEnrolledCourses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        loadDashboardData();
    }, []);

    const loadDashboardData = async () => {
        try {
            setLoading(true);

            // Load enrolled courses
            const enrolledData = await elearningAPI.studentAPI.getEnrolledCourses();
            setEnrolledCourses(enrolledData);

            // Update stats
            setStats({
                enrolledCourses: enrolledData.length,
                completedAssignments: 12,
                pendingQuizzes: 2,
                turboWarpProjects: 5
            });

            // Load sample activities and deadlines
            setRecentActivities([
                { id: 1, type: 'quiz', title: 'Hoàn thành bài kiểm tra Toán học', time: '1 giờ trước', status: 'completed', score: 85 },
                { id: 2, type: 'lesson', title: 'Xem bài giảng Vật lý - Chuyển động', time: '2 giờ trước', status: 'viewed' },
                { id: 3, type: 'assignment', title: 'Nộp bài tập Scratch - Game Pong', time: '1 ngày trước', status: 'submitted' },
            ]);

            setUpcomingDeadlines([
                { id: 1, title: 'Bài kiểm tra Vật lý giữa kỳ', course: 'Vật lý 9B', deadline: '2024-01-18 14:00', priority: 'high', type: 'quiz' },
                { id: 2, title: 'Nộp bài tập Toán học', course: 'Toán 8A', deadline: '2024-01-20 23:59', priority: 'medium', type: 'assignment' },
                { id: 3, title: 'Dự án Scratch cuối kỳ', course: 'Tin học 7C', deadline: '2024-01-25 23:59', priority: 'low', type: 'turbowarp' },
            ]);

        } catch (err) {
            setError('Không thể tải dữ liệu dashboard');
            console.error('Dashboard data error:', err);
        } finally {
            setLoading(false);
        }
    };

    const StatCard = ({ title, value, icon, color, subtitle, trend }) => (
        <Card
            elevation={0}
            className="stat-card"
            sx={{
                height: '100%',
                borderRadius: '20px',
                border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                transition: 'all 0.3s ease',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: `0 12px 40px ${alpha(color, 0.15)}`,
                    borderColor: alpha(color, 0.3)
                }
            }}
        >
            <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                        <Typography
                            variant="body2"
                            sx={{
                                color: beeColors.neutral.light,
                                fontWeight: 600,
                                textTransform: 'uppercase',
                                letterSpacing: 0.5,
                                mb: 1
                            }}
                        >
                            {title}
                        </Typography>
                        <Typography
                            variant="h3"
                            component="div"
                            sx={{
                                color: color,
                                fontWeight: 700,
                                mb: 0.5
                            }}
                        >
                            {value}
                        </Typography>
                        {subtitle && (
                            <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                {subtitle}
                            </Typography>
                        )}
                        {trend && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                                <TrendingUpIcon sx={{ fontSize: 16, color: beeColors.success.main, mr: 0.5 }} />
                                <Typography variant="caption" sx={{ color: beeColors.success.main, fontWeight: 600 }}>
                                    {trend}
                                </Typography>
                            </Box>
                        )}
                    </Box>
                    <Box
                        sx={{
                            width: 64,
                            height: 64,
                            borderRadius: '16px',
                            background: `linear-gradient(135deg, ${color} 0%, ${alpha(color, 0.8)} 100%)`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: 28
                        }}
                    >
                        {icon}
                    </Box>
                </Box>
            </CardContent>
        </Card>
    );

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'success';
            case 'viewed': return 'info';
            case 'submitted': return 'warning';
            default: return 'default';
        }
    };

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high': return 'error';
            case 'medium': return 'warning';
            case 'low': return 'success';
            default: return 'default';
        }
    };

    const getTypeIcon = (type) => {
        switch (type) {
            case 'quiz': return <QuizIcon sx={{ fontSize: 16 }} />;
            case 'assignment': return <AssignmentIcon sx={{ fontSize: 16 }} />;
            case 'turbowarp': return <TurboWarpIcon sx={{ fontSize: 16 }} />;
            default: return <ScheduleIcon sx={{ fontSize: 16 }} />;
        }
    };

    return (
        <Container maxWidth="xl" sx={{ py: 4 }}>
            {/* Hero Welcome Section */}
            <Paper
                elevation={0}
                className="dashboard-hero"
                sx={{
                    background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`,
                    borderRadius: '24px',
                    p: 4,
                    mb: 4,
                    color: 'white',
                    position: 'relative',
                    overflow: 'hidden'
                }}
            >
                <Box sx={{ position: 'relative', zIndex: 2 }}>
                    <Typography variant="h3" gutterBottom sx={{ fontWeight: 700, mb: 1 }}>
                        Chào mừng trở lại, {user?.first_name || 'bạn'}! 🎯
                    </Typography>
                    <Typography variant="h6" sx={{ opacity: 0.9, mb: 3, fontWeight: 400 }}>
                        {new Date().toLocaleDateString('vi-VN', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        })}
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.8, maxWidth: 600 }}>
                        Hãy tiếp tục hành trình khám phá kiến thức STEM cùng BeE Learning.
                        Mỗi bài học là một bước tiến mới trong tương lai của bạn! 🚀
                    </Typography>
                </Box>

                {/* Decorative elements */}
                <Box
                    className="floating-element"
                    sx={{
                        position: 'absolute',
                        top: -50,
                        right: -50,
                        width: 200,
                        height: 200,
                        borderRadius: '50%',
                        background: alpha('#fff', 0.1),
                        zIndex: 1
                    }}
                />
                <Box
                    className="floating-element"
                    sx={{
                        position: 'absolute',
                        bottom: -30,
                        right: 100,
                        width: 100,
                        height: 100,
                        borderRadius: '50%',
                        background: alpha('#fff', 0.05),
                        zIndex: 1,
                        animationDelay: '2s'
                    }}
                />
            </Paper>

            {/* Statistics Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }} className="dashboard-stats">
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <StatCard
                        title="Khóa học đang học"
                        value={stats.enrolledCourses}
                        icon={<SchoolIcon />}
                        color={beeColors.primary.main}
                        subtitle="Đang tiến hành"
                        trend="+2 tuần này"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <StatCard
                        title="Bài tập hoàn thành"
                        value={stats.completedAssignments}
                        icon={<AssignmentIcon />}
                        color={beeColors.success.main}
                        subtitle="Tổng số bài"
                        trend="+5 tuần này"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <StatCard
                        title="Bài kiểm tra chờ"
                        value={stats.pendingQuizzes}
                        icon={<QuizIcon />}
                        color={beeColors.warning.main}
                        subtitle="Cần hoàn thành"
                    />
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <StatCard
                        title="Dự án sáng tạo"
                        value={stats.turboWarpProjects}
                        icon={<TurboWarpIcon />}
                        color={beeColors.secondary.main}
                        subtitle="TurboWarp"
                        trend="+1 tuần này"
                    />
                </Grid>
            </Grid>

            {/* Content Grid */}
            <Grid container spacing={4}>
                {/* Current Courses */}
                <Grid size={{ xs: 12, lg: 8 }} className="dashboard-courses">
                    <Paper
                        elevation={0}
                        sx={{
                            p: 4,
                            borderRadius: '24px',
                            border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                            background: 'linear-gradient(145deg, #ffffff 0%, #fafafa 100%)',
                            mb: 4
                        }}
                    >
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
                            <Box>
                                <Typography variant="h5" sx={{ fontWeight: 700, color: beeColors.neutral.dark, mb: 0.5 }}>
                                    Khóa học của tôi
                                </Typography>
                                <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                    {enrolledCourses.length} khóa học đang theo học
                                </Typography>
                            </Box>
                            <Button
                                variant="contained"
                                onClick={() => navigate('/e-learning')}
                                sx={{
                                    textTransform: 'none',
                                    borderRadius: '12px',
                                    px: 3,
                                    py: 1.5,
                                    background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`,
                                    boxShadow: `0 4px 20px ${alpha(beeColors.primary.main, 0.3)}`,
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: `0 8px 30px ${alpha(beeColors.primary.main, 0.4)}`
                                    }
                                }}
                                endIcon={<ArrowForwardIcon />}
                            >
                                Khám phá thêm
                            </Button>
                        </Box>

                        {loading ? (
                            <Box display="flex" justifyContent="center" py={4}>
                                <CircularProgress />
                            </Box>
                        ) : error ? (
                            <Alert severity="error" sx={{ mb: 2 }}>
                                {error}
                            </Alert>
                        ) : enrolledCourses.length === 0 ? (
                            <Box textAlign="center" py={6}>
                                <Box
                                    sx={{
                                        width: 120,
                                        height: 120,
                                        borderRadius: '50%',
                                        background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.1)} 0%, ${alpha(beeColors.secondary.main, 0.1)} 100%)`,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mx: 'auto',
                                        mb: 3
                                    }}
                                >
                                    <SchoolIcon sx={{ fontSize: 48, color: beeColors.primary.main }} />
                                </Box>
                                <Typography variant="h5" sx={{ fontWeight: 600, color: beeColors.neutral.dark, mb: 1 }}>
                                    Chưa có khóa học nào
                                </Typography>
                                <Typography variant="body1" sx={{ color: beeColors.neutral.light, mb: 4, maxWidth: 400, mx: 'auto' }}>
                                    Hãy bắt đầu hành trình học tập STEM cùng BeE Learning. Khám phá hàng trăm khóa học chất lượng cao!
                                </Typography>
                                <Button
                                    variant="contained"
                                    size="large"
                                    onClick={() => navigate('/e-learning')}
                                    sx={{
                                        borderRadius: '12px',
                                        px: 4,
                                        py: 1.5,
                                        background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`,
                                        boxShadow: `0 4px 20px ${alpha(beeColors.primary.main, 0.3)}`,
                                        '&:hover': {
                                            transform: 'translateY(-2px)',
                                            boxShadow: `0 8px 30px ${alpha(beeColors.primary.main, 0.4)}`
                                        }
                                    }}
                                    endIcon={<ArrowForwardIcon />}
                                >
                                    Khám phá khóa học
                                </Button>
                            </Box>
                        ) : (
                            <Grid container spacing={3}>
                                {enrolledCourses.map((enrollment) => (
                                    <Grid xs={12} md={6} key={enrollment.id}>
                                        <Card
                                            elevation={0}
                                            className="course-card"
                                            sx={{
                                                height: '100%',
                                                borderRadius: '20px',
                                                cursor: 'pointer',
                                                border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                                                transition: 'all 0.3s ease',
                                                overflow: 'hidden',
                                                '&:hover': {
                                                    transform: 'translateY(-8px)',
                                                    boxShadow: `0 20px 60px ${alpha(beeColors.primary.main, 0.15)}`,
                                                    borderColor: alpha(beeColors.primary.main, 0.3)
                                                }
                                            }}
                                            onClick={() => navigate(`/e-learning/course/${enrollment.course.id}/learn`)}
                                        >
                                            {/* Course Thumbnail */}
                                            <Box sx={{ position: 'relative' }}>
                                                {enrollment.course.thumbnail ? (
                                                    <CardMedia
                                                        component="img"
                                                        height="160"
                                                        image={enrollment.course.thumbnail}
                                                        alt={enrollment.course.title}
                                                        sx={{ objectFit: 'cover' }}
                                                    />
                                                ) : (
                                                    <Box
                                                        sx={{
                                                            height: 160,
                                                            background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`,
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        }}
                                                    >
                                                        <SchoolIcon sx={{ fontSize: 48, color: 'white' }} />
                                                    </Box>
                                                )}

                                                {/* Status Badge */}
                                                <Chip
                                                    label={enrollment.status === 'completed' ? 'Hoàn thành' : 'Đang học'}
                                                    size="small"
                                                    sx={{
                                                        position: 'absolute',
                                                        top: 12,
                                                        right: 12,
                                                        background: enrollment.status === 'completed'
                                                            ? beeColors.success.main
                                                            : beeColors.primary.main,
                                                        color: 'white',
                                                        fontWeight: 600
                                                    }}
                                                />
                                            </Box>

                                            <CardContent sx={{ p: 3 }}>
                                                <Typography
                                                    variant="h6"
                                                    gutterBottom
                                                    sx={{
                                                        fontWeight: 600,
                                                        color: beeColors.neutral.dark,
                                                        mb: 1,
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 2,
                                                        WebkitBoxOrient: 'vertical',
                                                        overflow: 'hidden'
                                                    }}
                                                >
                                                    {enrollment.course.title}
                                                </Typography>

                                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                                    <Avatar sx={{ width: 24, height: 24, mr: 1, bgcolor: beeColors.primary.main }}>
                                                        <Typography variant="caption" sx={{ fontSize: 10, fontWeight: 600 }}>
                                                            {(enrollment.course.instructor?.name || 'BeE')[0]}
                                                        </Typography>
                                                    </Avatar>
                                                    <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                                        {enrollment.course.instructor?.name || 'BeE Learning'}
                                                    </Typography>
                                                </Box>

                                                <Chip
                                                    label={enrollment.course.subject?.name}
                                                    size="small"
                                                    variant="outlined"
                                                    sx={{
                                                        mb: 2,
                                                        borderColor: alpha(beeColors.primary.main, 0.3),
                                                        color: beeColors.primary.main
                                                    }}
                                                />

                                                {/* Progress Section */}
                                                <Box sx={{ mb: 3 }}>
                                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.5 }}>
                                                        <Typography variant="body2" sx={{ fontWeight: 600, color: beeColors.neutral.dark }}>
                                                            Tiến độ học tập
                                                        </Typography>
                                                        <Typography variant="h6" sx={{ fontWeight: 700, color: beeColors.primary.main }}>
                                                            {Math.round(enrollment.actual_progress_percentage || enrollment.progress_percentage || 0)}%
                                                        </Typography>
                                                    </Box>

                                                    <LinearProgress
                                                        variant="determinate"
                                                        value={enrollment.actual_progress_percentage || enrollment.progress_percentage || 0}
                                                        sx={{
                                                            height: 10,
                                                            borderRadius: 5,
                                                            backgroundColor: alpha(beeColors.primary.main, 0.1),
                                                            mb: 1,
                                                            '& .MuiLinearProgress-bar': {
                                                                borderRadius: 5,
                                                                background: enrollment.status === 'completed'
                                                                    ? `linear-gradient(90deg, ${beeColors.success.main} 0%, ${beeColors.success.light} 100%)`
                                                                    : `linear-gradient(90deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`
                                                            }
                                                        }}
                                                    />

                                                    <Typography variant="caption" sx={{ color: beeColors.neutral.light }}>
                                                        {enrollment.actual_completed_lessons || enrollment.completed_lessons || 0} / {enrollment.total_lessons || enrollment.course.total_lessons || 0} bài học hoàn thành
                                                    </Typography>
                                                </Box>
                                            </CardContent>

                                            {/* Action Section */}
                                            <CardActions sx={{ p: 3, pt: 0 }}>
                                                <Button
                                                    fullWidth
                                                    variant="contained"
                                                    size="large"
                                                    startIcon={<PlayIcon />}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(`/e-learning/course/${enrollment.course.id}/learn`);
                                                    }}
                                                    sx={{
                                                        borderRadius: '12px',
                                                        py: 1.5,
                                                        background: enrollment.status === 'completed'
                                                            ? `linear-gradient(135deg, ${beeColors.success.main} 0%, ${beeColors.success.light} 100%)`
                                                            : `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`,
                                                        boxShadow: `0 4px 20px ${alpha(enrollment.status === 'completed' ? beeColors.success.main : beeColors.primary.main, 0.3)}`,
                                                        '&:hover': {
                                                            transform: 'translateY(-2px)',
                                                            boxShadow: `0 8px 30px ${alpha(enrollment.status === 'completed' ? beeColors.success.main : beeColors.primary.main, 0.4)}`
                                                        }
                                                    }}
                                                >
                                                    {enrollment.status === 'completed' ? 'Xem lại khóa học' : 'Tiếp tục học'}
                                                </Button>
                                            </CardActions>
                                        </Card>
                                    </Grid>
                                ))}
                            </Grid>
                        )}
                    </Paper>

                    {/* Recent Activities */}
                    <Paper sx={{ p: 3, borderRadius: '24px' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="h6">
                                Hoạt động gần đây
                            </Typography>
                            <IconButton>
                                <TrendingUpIcon />
                            </IconButton>
                        </Box>
                        <List>
                            {recentActivities.map((activity) => (
                                <ListItem key={activity.id} divider>
                                    <ListItemText
                                        primary={activity.title}
                                        secondary={activity.time}
                                    />
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        {activity.score && (
                                            <Chip
                                                label={`${activity.score} điểm`}
                                                color="success"
                                                size="small"
                                            />
                                        )}
                                        <Chip
                                            label={activity.status}
                                            color={getStatusColor(activity.status)}
                                            size="small"
                                        />
                                    </Box>
                                </ListItem>
                            ))}
                        </List>
                        <Box sx={{ mt: 2, textAlign: 'center' }}>
                            <Button variant="outlined" size="small">
                                Xem tất cả hoạt động
                            </Button>
                        </Box>
                    </Paper>
                </Grid>

                {/* Sidebar */}
                <Grid size={{ xs: 12, lg: 4 }} className="dashboard-sidebar">
                    {/* Upcoming Tasks */}
                    <Paper
                        elevation={0}
                        sx={{
                            p: 3,
                            mb: 3,
                            borderRadius: '20px',
                            border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                            background: 'linear-gradient(145deg, #ffffff 0%, #fafafa 100%)'
                        }}
                    >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                            <Box
                                sx={{
                                    width: 48,
                                    height: 48,
                                    borderRadius: '12px',
                                    background: `linear-gradient(135deg, ${beeColors.warning.main} 0%, ${beeColors.warning.light} 100%)`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mr: 2
                                }}
                            >
                                <NotificationIcon sx={{ color: 'white', fontSize: 24 }} />
                            </Box>
                            <Box>
                                <Typography variant="h6" sx={{ fontWeight: 700, color: beeColors.neutral.dark }}>
                                    Nhiệm vụ hôm nay
                                </Typography>
                                <Typography variant="body2" sx={{ color: beeColors.neutral.light }}>
                                    {upcomingDeadlines.length} việc cần hoàn thành
                                </Typography>
                            </Box>
                        </Box>

                        <Stack spacing={2}>
                            {upcomingDeadlines.slice(0, 3).map((deadline) => (
                                <Box
                                    key={deadline.id}
                                    sx={{
                                        p: 2,
                                        borderRadius: '12px',
                                        border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                                        background: 'white',
                                        transition: 'all 0.2s ease',
                                        '&:hover': {
                                            borderColor: alpha(beeColors.primary.main, 0.3),
                                            transform: 'translateY(-2px)'
                                        }
                                    }}
                                >
                                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                                        <Box
                                            sx={{
                                                width: 32,
                                                height: 32,
                                                borderRadius: '8px',
                                                background: alpha(beeColors.primary.main, 0.1),
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                flexShrink: 0
                                            }}
                                        >
                                            {getTypeIcon(deadline.type)}
                                        </Box>
                                        <Box sx={{ flex: 1, minWidth: 0 }}>
                                            <Typography
                                                variant="body2"
                                                sx={{
                                                    fontWeight: 600,
                                                    color: beeColors.neutral.dark,
                                                    mb: 0.5,
                                                    display: '-webkit-box',
                                                    WebkitLineClamp: 2,
                                                    WebkitBoxOrient: 'vertical',
                                                    overflow: 'hidden'
                                                }}
                                            >
                                                {deadline.title}
                                            </Typography>
                                            <Typography variant="caption" sx={{ color: beeColors.neutral.light, display: 'block', mb: 1 }}>
                                                {deadline.course}
                                            </Typography>
                                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                                <Typography variant="caption" sx={{ color: beeColors.neutral.light }}>
                                                    {deadline.deadline}
                                                </Typography>
                                                <Chip
                                                    label={deadline.priority}
                                                    size="small"
                                                    sx={{
                                                        height: 20,
                                                        fontSize: 10,
                                                        fontWeight: 600,
                                                        bgcolor: deadline.priority === 'high' ? alpha(beeColors.error.main, 0.1) :
                                                            deadline.priority === 'medium' ? alpha(beeColors.warning.main, 0.1) :
                                                                alpha(beeColors.success.main, 0.1),
                                                        color: deadline.priority === 'high' ? beeColors.error.main :
                                                            deadline.priority === 'medium' ? beeColors.warning.main :
                                                                beeColors.success.main
                                                    }}
                                                />
                                            </Box>
                                        </Box>
                                    </Box>
                                </Box>
                            ))}
                        </Stack>

                        <Button
                            fullWidth
                            variant="outlined"
                            sx={{
                                mt: 3,
                                borderRadius: '12px',
                                borderColor: alpha(beeColors.primary.main, 0.3),
                                color: beeColors.primary.main,
                                '&:hover': {
                                    borderColor: beeColors.primary.main,
                                    background: alpha(beeColors.primary.main, 0.05)
                                }
                            }}
                        >
                            Xem tất cả nhiệm vụ
                        </Button>
                    </Paper>
                </Grid>
            </Grid>
        </Container>
    );
}

export default StudentDashboard;
