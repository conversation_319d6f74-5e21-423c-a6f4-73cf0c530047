import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
	Box,
	Typography,
	Button,
	Card,
	CardContent,
	Stack,
	Chip,
	LinearProgress,
	Divider,
	Avatar
} from '@mui/material';
import {
	EmojiEvents as TrophyIcon,
	Star as StarIcon,
	Home as HomeIcon,
	Dashboard as DashboardIcon,
	School as SchoolIcon,
	CheckCircle as CheckIcon,
	Download as DownloadIcon,
	Share as ShareIcon
} from '@mui/icons-material';

const CourseCompletionPage = ({ course, user, completionStats }) => {
	const navigate = useNavigate();
	const { courseId } = useParams();
	const [showConfetti, setShowConfetti] = useState(true);

	useEffect(() => {
		// Hide confetti after 5 seconds
		const timer = setTimeout(() => {
			setShowConfetti(false);
		}, 5000);

		// Simulate celebration sound effect with visual feedback
		const celebrationTimer = setTimeout(() => {
			//🎉 CELEBRATION! Course completed successfully! 🎉
		}, 500);

		return () => {
			clearTimeout(timer);
			clearTimeout(celebrationTimer);
		};
	}, []);

	const handleBackToDashboard = () => {
		navigate('/e-learning/dashboard');
	};

	const handleBackToHome = () => {
		navigate('/e-learning');
	};

	const handleDownloadCertificate = () => {
		// TODO: Implement certificate download
		alert('🎓 Chức năng tải chứng chỉ sẽ được cập nhật sớm!');
	};

	const handleShareAchievement = () => {
		// TODO: Implement social sharing
		alert('📱 Chức năng chia sẻ thành tích sẽ được cập nhật sớm!');
	};

	return (
		<Box
			sx={{
				minHeight: '100vh',
				background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				p: 3,
				position: 'relative',
				overflow: 'hidden'
			}}
		>
			{/* Animated Background Elements */}
			<Box
				sx={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					opacity: 0.1,
					background: `
						radial-gradient(circle at 20% 80%, #ffd700 0%, transparent 50%),
						radial-gradient(circle at 80% 20%, #ff6b6b 0%, transparent 50%),
						radial-gradient(circle at 40% 40%, #4ecdc4 0%, transparent 50%)
					`
				}}
			/>

			{/* Floating Particles */}
			<Box
				sx={{
					position: 'absolute',
					top: 0,
					left: 0,
					right: 0,
					bottom: 0,
					pointerEvents: 'none',
					overflow: 'hidden'
				}}
			>
				{[...Array(20)].map((_, i) => (
					<Box
						key={`particle-${i}`}
						sx={{
							position: 'absolute',
							width: `${4 + Math.random() * 8}px`,
							height: `${4 + Math.random() * 8}px`,
							backgroundColor: ['#ffd700', '#ff6b6b', '#4ecdc4', '#45b7d1'][i % 4],
							borderRadius: '50%',
							left: `${Math.random() * 100}%`,
							top: `${Math.random() * 100}%`,
							animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
							animationDelay: `${Math.random() * 2}s`,
							opacity: 0.6
						}}
					/>
				))}
			</Box>

			{/* Confetti Effect */}
			{showConfetti && (
				<Box
					sx={{
						position: 'absolute',
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						pointerEvents: 'none',
						overflow: 'hidden'
					}}
				>
					{/* Create multiple confetti particles */}
					{[...Array(50)].map((_, i) => (
						<Box
							key={i}
							sx={{
								position: 'absolute',
								width: '10px',
								height: '10px',
								backgroundColor: ['#ffd700', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][i % 6],
								left: `${Math.random() * 100}%`,
								animationDelay: `${Math.random() * 3}s`,
								animation: 'confettiFall 3s linear infinite',
								borderRadius: i % 3 === 0 ? '50%' : '0',
								transform: `rotate(${Math.random() * 360}deg)`
							}}
						/>
					))}
				</Box>
			)}

			<Card
				className="completion-card"
				sx={{
					maxWidth: 800,
					width: '100%',
					borderRadius: 4,
					boxShadow: '0 20px 60px rgba(0,0,0,0.3)',
					background: 'rgba(255, 255, 255, 0.95)',
					backdropFilter: 'blur(10px)',
					position: 'relative',
					zIndex: 1
				}}
			>
				<CardContent sx={{ p: 6, textAlign: 'center' }}>
					{/* Trophy Icon */}
					<Box sx={{ mb: 4 }}>
						<Avatar
							className="trophy-icon"
							sx={{
								width: 120,
								height: 120,
								bgcolor: '#ffd700',
								mx: 'auto',
								mb: 2
							}}
						>
							<TrophyIcon sx={{ fontSize: 60, color: '#fff' }} />
						</Avatar>
					</Box>

					{/* Congratulations Message */}
					<Typography
						variant="h2"
						fontWeight="bold"
						gutterBottom
						sx={{
							background: 'linear-gradient(45deg, #667eea, #764ba2)',
							backgroundClip: 'text',
							WebkitBackgroundClip: 'text',
							WebkitTextFillColor: 'transparent',
							mb: 2,
							'& .emoji': {
								display: 'inline-block',
								animation: 'bounce 1s ease-in-out infinite'
							}
						}}
					>
						<span className="emoji">🎉</span> Chúc mừng! <span className="emoji">🎉</span>
					</Typography>

					<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#333', mb: 1 }}>
						Bạn đã hoàn thành khóa học
					</Typography>

					<Typography variant="h5" sx={{ color: '#1976d2', mb: 4, fontWeight: 'bold' }}>
						"{course?.title || 'STEM Course'}"
					</Typography>

					{/* User Info */}
					<Box sx={{ mb: 4 }}>
						<Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
							Học viên: <strong style={{ color: '#333' }}>{user?.first_name} {user?.last_name}</strong>
						</Typography>
						<Typography variant="body1" sx={{ color: '#666' }}>
							Hoàn thành vào: <strong>{new Date().toLocaleDateString('vi-VN')}</strong>
						</Typography>
					</Box>

					{/* Achievement Stats */}
					<Box sx={{ mb: 4 }}>
						<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: '#333', mb: 3 }}>
							📊 Thành tích của bạn
						</Typography>

						<Stack direction="row" spacing={3} justifyContent="center" sx={{ mb: 3 }}>
							<Box sx={{ textAlign: 'center' }}>
								<Typography variant="h4" fontWeight="bold" sx={{ color: '#4caf50' }}>
									{completionStats?.totalLessons || 0}
								</Typography>
								<Typography variant="body2" color="textSecondary">
									Bài học hoàn thành
								</Typography>
							</Box>
							<Box sx={{ textAlign: 'center' }}>
								<Typography variant="h4" fontWeight="bold" sx={{ color: '#2196f3' }}>
									{completionStats?.totalQuizzes || 0}
								</Typography>
								<Typography variant="body2" color="textSecondary">
									Quiz đã làm
								</Typography>
							</Box>
							<Box sx={{ textAlign: 'center' }}>
								<Typography variant="h4" fontWeight="bold" sx={{ color: '#ff9800' }}>
									{completionStats?.totalAssignments || 0}
								</Typography>
								<Typography variant="body2" color="textSecondary">
									Bài tập đã nộp
								</Typography>
							</Box>
						</Stack>

						{/* Progress Bar */}
						<Box sx={{ mb: 3 }}>
							<Typography variant="body1" fontWeight="bold" sx={{ mb: 1, color: '#333' }}>
								Tiến độ hoàn thành: 100%
							</Typography>
							<LinearProgress
								variant="determinate"
								value={100}
								sx={{
									height: 12,
									borderRadius: 6,
									bgcolor: 'rgba(0,0,0,0.1)',
									'& .MuiLinearProgress-bar': {
										background: 'linear-gradient(45deg, #4caf50, #8bc34a)',
										borderRadius: 6
									}
								}}
							/>
						</Box>

						{/* Achievement Badges */}
						<Stack direction="row" spacing={1} justifyContent="center" flexWrap="wrap" sx={{ gap: 1 }}>
							<Chip
								icon={<CheckIcon />}
								label="Hoàn thành khóa học"
								sx={{ bgcolor: '#4caf50', color: 'white', fontWeight: 'bold' }}
							/>
							<Chip
								icon={<StarIcon />}
								label="Học viên xuất sắc"
								sx={{ bgcolor: '#ffd700', color: '#333', fontWeight: 'bold' }}
							/>
							<Chip
								icon={<SchoolIcon />}
								label="STEM Master"
								sx={{ bgcolor: '#2196f3', color: 'white', fontWeight: 'bold' }}
							/>
						</Stack>
					</Box>

					<Divider sx={{ my: 4 }} />

					{/* Action Buttons */}
					<Stack direction="row" spacing={2} justifyContent="center" flexWrap="wrap" sx={{ gap: 2 }}>
						<Button
							variant="contained"
							size="large"
							startIcon={<DashboardIcon />}
							onClick={handleBackToDashboard}
							sx={{
								background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
								px: 4,
								py: 1.5,
								fontWeight: 'bold',
								'&:hover': {
									transform: 'translateY(-2px)',
									boxShadow: '0 8px 25px rgba(25, 118, 210, 0.3)'
								}
							}}
						>
							Về Dashboard
						</Button>

						<Button
							variant="outlined"
							size="large"
							startIcon={<HomeIcon />}
							onClick={handleBackToHome}
							sx={{
								borderColor: '#1976d2',
								color: '#1976d2',
								px: 4,
								py: 1.5,
								fontWeight: 'bold',
								'&:hover': {
									borderColor: '#1976d2',
									bgcolor: 'rgba(25, 118, 210, 0.1)',
									transform: 'translateY(-2px)'
								}
							}}
						>
							Trang chủ E-Learning
						</Button>

						<Button
							variant="outlined"
							size="large"
							startIcon={<DownloadIcon />}
							onClick={handleDownloadCertificate}
							sx={{
								borderColor: '#4caf50',
								color: '#4caf50',
								px: 4,
								py: 1.5,
								fontWeight: 'bold',
								'&:hover': {
									borderColor: '#4caf50',
									bgcolor: 'rgba(76, 175, 80, 0.1)',
									transform: 'translateY(-2px)'
								}
							}}
						>
							Tải chứng chỉ
						</Button>

						<Button
							variant="outlined"
							size="large"
							startIcon={<ShareIcon />}
							onClick={handleShareAchievement}
							sx={{
								borderColor: '#ff9800',
								color: '#ff9800',
								px: 4,
								py: 1.5,
								fontWeight: 'bold',
								'&:hover': {
									borderColor: '#ff9800',
									bgcolor: 'rgba(255, 152, 0, 0.1)',
									transform: 'translateY(-2px)'
								}
							}}
						>
							Chia sẻ thành tích
						</Button>
					</Stack>
				</CardContent>
			</Card>

			{/* CSS Animations */}
			<style>{`
				@keyframes bounce {
					0%, 20%, 50%, 80%, 100% {
						transform: translateY(0);
					}
					40% {
						transform: translateY(-10px);
					}
					60% {
						transform: translateY(-5px);
					}
				}

				@keyframes confettiFall {
					0% {
						transform: translateY(-100vh) rotate(0deg);
						opacity: 1;
					}
					100% {
						transform: translateY(100vh) rotate(720deg);
						opacity: 0;
					}
				}

				@keyframes pulse {
					0% {
						transform: scale(1);
					}
					50% {
						transform: scale(1.05);
					}
					100% {
						transform: scale(1);
					}
				}

				@keyframes float {
					0%, 100% {
						transform: translateY(0px) rotate(0deg);
					}
					50% {
						transform: translateY(-20px) rotate(180deg);
					}
				}

				@keyframes fadeInUp {
					from {
						opacity: 0;
						transform: translateY(30px);
					}
					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.completion-card {
					animation: fadeInUp 0.8s ease-out, pulse 2s ease-in-out infinite;
				}

				.trophy-icon {
					animation: bounce 2s ease-in-out infinite;
				}
			`}</style>
		</Box>
	);
};

export default CourseCompletionPage;
