import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import CourseCompletionPage from './CourseCompletionPage';
import {
	Box,
	Typography,
	Button,
	Card,
	CardContent,
	List,
	ListItem,
	ListItemIcon,
	ListItemText,
	ListItemButton,
	Chip,
	LinearProgress,
	IconButton,
	Drawer,
	AppBar,
	Toolbar,
	Container,
	Paper,
	Stack,
	Divider,
	Alert,
	CircularProgress,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	Checkbox,
	FormControlLabel
} from '@mui/material';
import {
	PlayArrow as PlayIcon,
	Pause as PauseIcon,
	SkipNext as NextIcon,
	SkipPrevious as PrevIcon,
	Assignment as AssignmentIcon,
	Quiz as QuizIcon,
	Code as CodeIcon,
	Videocam as VideocamIcon,
	CheckCircle as CheckIcon,
	Lock as LockIcon,
	Menu as MenuIcon,
	Close as CloseIcon,
	Timer as TimerIcon,
	School as SchoolIcon,
	Home as HomeIcon,
	Dashboard as DashboardIcon,
	Description as DescriptionIcon,
	Download as DownloadIcon,
	TextFields as TextIcon,
	DangerousRounded
} from '@mui/icons-material';
import PersonalVideoIcon from '@mui/icons-material/PersonalVideo';
import ArticleIcon from '@mui/icons-material/Article';
import AttachFileIcon from '@mui/icons-material/AttachFile';
// import QuizIcon from '@mui/icons-material/Quiz';
import PlayLessonIcon from '@mui/icons-material/PlayLesson';
import { axiosInstance, elearningAPI } from '../../../../services';

const CourseLearning = ({ user }) => {
	const { courseId } = useParams();
	const navigate = useNavigate();

	// States
	const [course, setCourse] = useState(null);
	const [currentLesson, setCurrentLesson] = useState(null);
	const [lessons, setLessons] = useState([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [sidebarOpen, setSidebarOpen] = useState(true);
	const [timeRemaining, setTimeRemaining] = useState(0);
	const [isTimerActive, setIsTimerActive] = useState(false);
	const [canProceed, setCanProceed] = useState(false);
	const [completedLessons, setCompletedLessons] = useState(new Set());
	const [showQuizDialog, setShowQuizDialog] = useState(false);
	const [quizAnswers, setQuizAnswers] = useState({}); // For single choice: {questionId: answerIndex}
	const [multipleChoiceAnswers, setMultipleChoiceAnswers] = useState({}); // For multiple choice: {questionId: [answerIndex1, answerIndex2]}
	const [isSubmittingQuiz, setIsSubmittingQuiz] = useState(false);
	const [lessonProgress, setLessonProgress] = useState({}); // Track progress for each lesson
	const [timeSpent, setTimeSpent] = useState(0); // Time spent on current lesson
	const [requiredTime, setRequiredTime] = useState(0); // Required time for current lesson
	const [assignmentFile, setAssignmentFile] = useState(null); // Selected assignment file
	const [assignmentSubmissions, setAssignmentSubmissions] = useState({}); // Track assignment submissions
	const [showCompletionPage, setShowCompletionPage] = useState(false); // Show course completion page

	// Check if user is authenticated
	useEffect(() => {
		if (!user) {
			navigate('/login', {
				state: { from: { pathname: `/e-learning/course/${courseId}` } }
			});
		}
	}, [user, navigate, courseId]);

	// Save lesson progress to backend (only for actual lessons, not quiz/assignment)
	const saveLessonProgress = async (lessonId, progressData) => {
		try {
			// Check if this is a real lesson (not quiz_X or assignment_X)
			if (lessonId.toString().startsWith('quiz_') || lessonId.toString().startsWith('assignment_')) {
				// Only save to localStorage for quiz/assignment
				saveProgressToLocalStorage(lessonId, progressData);
				return;
			}

			const result = await elearningAPI.studentAPI.updateLessonProgress(courseId, lessonId, progressData);
			// Also save to localStorage as backup
			saveProgressToLocalStorage(lessonId, progressData);
			return result;
		} catch (error) {
			console.error('Error saving lesson progress to API:', error);
			console.error('Error details:', {
				status: error.response?.status,
				statusText: error.response?.statusText,
				data: error.response?.data,
				message: error.message
			});

			// Save to localStorage as fallback
			saveProgressToLocalStorage(lessonId, progressData);
		}
	};

	// Handle quiz completion (when quiz is passed)
	const handleQuizCompletion = async (quizId, score) => {
		try {
			if (score >= 80) {
				// Save quiz result to localStorage
				const quizResult = {
					quiz_id: quizId,
					user_id: user?.id,
					score: score,
					passed: true,
					completed_at: new Date().toISOString()
				};

				const quizKey = `course_${courseId}_quiz_${quizId}_result`;
				localStorage.setItem(quizKey, JSON.stringify(quizResult));

				// Find quiz lesson and mark as completed
				const quizLesson = lessons.find(l => l.type === 'quiz' && l.quizData?.id === quizId);
				if (quizLesson) {
					setCompletedLessons(prev => new Set([...prev, quizLesson.globalIndex]));
					setLessonProgress(prev => ({
						...prev,
						[quizLesson.globalIndex]: {
							timeSpent: 0,
							progressPercentage: 100,
							isCompleted: true
						}
					}));
				}
			}
		} catch (error) {
			console.error('Error handling quiz completion:', error);
		}
	};

	// Handle assignment completion (when assignment is submitted)
	const handleAssignmentCompletion = async (assignmentId) => {
		try {
			// Save assignment submission to localStorage
			const assignmentSubmission = {
				assignment_id: assignmentId,
				user_id: user?.id,
				submitted: true,
				completed_at: new Date().toISOString()
			};

			const assignmentKey = `course_${courseId}_assignment_${assignmentId}_submission`;
			localStorage.setItem(assignmentKey, JSON.stringify(assignmentSubmission));

			// Find assignment lesson and mark as completed
			const assignmentLesson = lessons.find(l => l.type === 'assignment' && l.assignmentData?.id === assignmentId);
			if (assignmentLesson) {
				setCompletedLessons(prev => new Set([...prev, assignmentLesson.globalIndex]));
				setLessonProgress(prev => ({
					...prev,
					[assignmentLesson.globalIndex]: {
						timeSpent: 0,
						progressPercentage: 100,
						isCompleted: true
					}
				}));
			}
		} catch (error) {
			console.error('Error handling assignment completion:', error);
		}
	};

	// Save progress to localStorage as backup
	const saveProgressToLocalStorage = (lessonId, progressData) => {
		try {
			const key = `course_${courseId}_lesson_${lessonId}_progress`;
			const progressWithTimestamp = {
				...progressData,
				saved_at: new Date().toISOString(),
				user_id: user?.id
			};
			localStorage.setItem(key, JSON.stringify(progressWithTimestamp));
		} catch (error) {
			console.error('Error saving to localStorage:', error);
		}
	};

	// Load progress from localStorage
	const loadProgressFromLocalStorage = (lessons) => {
		try {
			const progressMap = {};
			const completedSet = new Set();

			lessons.forEach(lesson => {
				const key = `course_${courseId}_lesson_${lesson.id}_progress`;
				const savedProgress = localStorage.getItem(key);

				if (savedProgress) {
					const progress = JSON.parse(savedProgress);

					// Only load if it's for the current user
					if (progress.user_id === user?.id) {
						progressMap[lesson.globalIndex] = {
							timeSpent: progress.time_spent * 60, // Convert minutes to seconds
							progressPercentage: progress.completion_percentage,
							isCompleted: progress.is_completed
						};
					} else {
						console.error(`Progress user_id mismatch for lesson ${lesson.id}: ${progress.user_id} vs ${user?.id}`);
					}

					if (progress.is_completed) {
						completedSet.add(lesson.globalIndex);
					}
				}
			});


			// Also load quiz results from localStorage
			lessons.forEach(lesson => {
				if (lesson.type === 'quiz') {
					const quizKey = `course_${courseId}_quiz_${lesson.quizData?.id}_result`;
					const savedQuizResult = localStorage.getItem(quizKey);

					if (savedQuizResult) {
						const quizResult = JSON.parse(savedQuizResult);

						// Only load if it's for the current user and quiz was passed
						if (quizResult.user_id === user?.id && quizResult.score >= 80) {
							progressMap[lesson.globalIndex] = {
								timeSpent: 0,
								progressPercentage: 100,
								isCompleted: true
							};
							completedSet.add(lesson.globalIndex);
						}
					}
				}
			});

			// Also check for lesson progress from localStorage backup
			lessons.forEach(lesson => {
				if ((lesson.type === 'lesson' || lesson.type === 'video') && !completedSet.has(lesson.globalIndex)) {
					const progressKey = `course_${courseId}_lesson_${lesson.id}_progress`;
					const savedProgress = localStorage.getItem(progressKey);

					if (savedProgress) {
						try {
							const progressData = JSON.parse(savedProgress);

							// Only load if it's for the current user and lesson is completed
							if (progressData.user_id === user?.id && progressData.is_completed) {
								progressMap[lesson.globalIndex] = {
									timeSpent: progressData.time_spent * 60, // Convert minutes to seconds
									progressPercentage: progressData.completion_percentage,
									isCompleted: progressData.is_completed
								};
								completedSet.add(lesson.globalIndex);
							}
						} catch (error) {
							console.error('Error parsing lesson progress from localStorage:', error);
						}
					}
				}
			});

			setLessonProgress(progressMap);
			setCompletedLessons(completedSet);
		} catch (error) {
			console.error('Error loading from localStorage:', error);
		}
	};

	// Save quiz attempt to backend
	const saveQuizAttempt = async (quizId, answers, score) => {
		try {
			// Start quiz attempt
			const attempt = await elearningAPI.studentAPI.startQuizAttempt(courseId, quizId);

			// Submit answers
			const result = await elearningAPI.studentAPI.submitQuizAnswers(courseId, quizId, attempt.id, {
				answers: answers,
				score: score
			});

			// Save to localStorage as backup
			saveQuizToLocalStorage(quizId, answers, score, attempt);

			return attempt;
		} catch (error) {
			console.error('Error saving quiz attempt:', error);
			// Save to localStorage as fallback
			saveQuizToLocalStorage(quizId, answers, score, null);
			throw error;
		}
	};

	// Save quiz result to localStorage as backup
	const saveQuizToLocalStorage = (quizId, answers, score, attempt) => {
		try {
			const key = `course_${courseId}_quiz_${quizId}_result`;
			const quizResult = {
				quiz_id: quizId,
				answers: answers,
				score: score,
				attempt: attempt,
				submitted_at: new Date().toISOString(),
				user_id: user?.id,
				course_id: courseId
			};
			localStorage.setItem(key, JSON.stringify(quizResult));
		} catch (error) {
			console.error('Error saving quiz to localStorage:', error);
		}
	};

	// Save assignment submission to backend
	const saveAssignmentSubmission = async (assignmentId, file, notes = '') => {
		try {
			// Create FormData for file upload
			const formData = new FormData();
			formData.append('file', file);
			formData.append('notes', notes);
			formData.append('assignment_id', assignmentId);

			// Try to submit to backend API
			let submission;
			try {
				submission = await elearningAPI.studentAPI.submitAssignment(courseId, assignmentId, formData);
			} catch (apiError) {
				// Fallback: simulate successful submission
				submission = {
					id: Math.random().toString(36).substring(2, 11),
					assignment_id: assignmentId,
					file_name: file.name,
					file_size: file.size,
					submitted_at: new Date().toISOString(),
					status: 'submitted',
					grade: null,
					feedback: null
				};
			}
			// Save to localStorage as backup
			saveAssignmentToLocalStorage(assignmentId, file, submission);

			return submission;
		} catch (error) {
			console.error('Error submitting assignment:', error);
			// Save to localStorage as fallback
			saveAssignmentToLocalStorage(assignmentId, file, null);
			throw error;
		}
	};

	// Save assignment submission to localStorage as backup
	const saveAssignmentToLocalStorage = (assignmentId, file, submission) => {
		try {
			const key = `course_${courseId}_assignment_${assignmentId}_submission`;
			const assignmentSubmission = {
				assignment_id: assignmentId,
				file_name: file.name,
				file_size: file.size,
				file_type: file.type,
				submission: submission,
				submitted_at: new Date().toISOString(),
				user_id: user?.id,
				course_id: courseId
			};
			localStorage.setItem(key, JSON.stringify(assignmentSubmission));
		} catch (error) {
			console.error('Error saving assignment to localStorage:', error);
		}
	};

	// Load course data
	useEffect(() => {
		loadCourseData();
	}, [courseId]);

	// Timer effect for tracking lesson progress
	useEffect(() => {
		let interval = null;
		if (isTimerActive && currentLesson) {
			interval = setInterval(() => {
				setTimeSpent(prevTime => {
					const newTime = prevTime + 1;

					// Update lesson progress
					const progressPercentage = Math.min((newTime / requiredTime) * 100, 100);
					setLessonProgress(prev => ({
						...prev,
						[currentLesson.globalIndex]: {
							timeSpent: newTime,
							progressPercentage,
							isCompleted: progressPercentage >= 100
						}
					}));

					// Save progress to backend every 30 seconds
					if (newTime % 30 === 0) {
						saveLessonProgress(currentLesson.id, {
							time_spent: Math.floor(newTime / 60), // Convert to minutes
							completion_percentage: progressPercentage,
							is_completed: progressPercentage >= 100
						});
					}

					// Check if lesson is completed
					if (progressPercentage >= 100 && !canProceed) {
						setCanProceed(true);
						setIsTimerActive(false);

						// Mark lesson as completed
						setCompletedLessons(prev => {
							const newSet = new Set([...prev, currentLesson.globalIndex]);
							return newSet;
						});

						// Update lesson progress immediately
						setLessonProgress(prev => ({
							...prev,
							[currentLesson.globalIndex]: {
								timeSpent: newTime,
								progressPercentage: 100,
								isCompleted: true
							}
						}));

						// Save completion to backend
						saveLessonProgress(currentLesson.id, {
							time_spent: Math.floor(newTime / 60),
							completion_percentage: 100,
							is_completed: true
						});
					}

					return newTime;
				});
			}, 1000);
		}
		return () => clearInterval(interval);
	}, [isTimerActive, currentLesson, requiredTime, canProceed]);

	const loadCourseData = async () => {
		try {
			setLoading(true);

			// Load course details
			const courseData = await elearningAPI.studentAPI.getCourse(courseId);
			setCourse(courseData);

			// Load course content (lessons, quizzes, assignments)
			let allLessons = [];
			try {
				const contentData = await elearningAPI.studentAPI.getCourseContent(courseId);

				// Process real lessons from database
				if (contentData.lessons && contentData.lessons.length > 0) {
					allLessons = contentData.lessons.map((lesson, index) => ({
						id: lesson.id,
						title: lesson.title,
						description: lesson.description,
						video_url: lesson.video_url,
						content_type: lesson.content_type,
						content: lesson.content,
						duration: lesson.duration,
						order: lesson.order,
						type: lesson.content_type, // Map content_type to type for compatibility
						globalIndex: index,
						isLocked: index > 0 // First lesson is unlocked
					}));

					// Add quizzes and assignments as lessons
					if (contentData.quizzes) {
						contentData.quizzes.forEach((quiz, index) => {
							allLessons.push({
								id: `quiz_${quiz.id}`,
								title: quiz.title,
								description: quiz.description,
								duration: quiz.time_limit || 30,
								type: 'quiz',
								globalIndex: allLessons.length,
								isLocked: allLessons.length > 0,
								quizData: quiz
							});
						});
					}

					if (contentData.assignments) {
						contentData.assignments.forEach((assignment, index) => {
							allLessons.push({
								id: `assignment_${assignment.id}`,
								title: assignment.title,
								description: assignment.description,
								duration: 60, // Default 60 minutes for assignments
								type: 'assignment',
								globalIndex: allLessons.length,
								isLocked: allLessons.length > 0,
								assignmentData: assignment
							});
						});
					}
				}
			} catch (contentError) {
				console.error('Failed to load course content from backend:', contentError);
			}

			// Fallback to syllabus data if no real lessons
			if (allLessons.length === 0) {
				const syllabusData = courseData.syllabus || courseData.modules || [];
				syllabusData.forEach((module, moduleIndex) => {
					module.lessons?.forEach((lesson, lessonIndex) => {
						allLessons.push({
							...lesson,
							id: lesson.id || `syllabus_${moduleIndex}_${lessonIndex}`,
							moduleId: module.id || moduleIndex,
							moduleTitle: module.title,
							globalIndex: allLessons.length,
							isLocked: allLessons.length > 0 // First lesson is unlocked
						});
					});
				});
			}
			setLessons(allLessons);
			// Load existing progress first, then set current lesson
			await loadExistingProgress(allLessons);

		} catch (err) {
			setError('Không thể tải dữ liệu khóa học');
			console.error('Course learning error:', err);
		} finally {
			setLoading(false);
		}
	};

	// Load existing progress from backend
	const loadExistingProgress = async (lessons) => {
		try {
			const progressData = await elearningAPI.studentAPI.getCourseProgress(courseId);

			// Update lesson progress state
			const progressMap = {};
			const completedSet = new Set();

			if (progressData.lesson_progress && Array.isArray(progressData.lesson_progress)) {
				progressData.lesson_progress.forEach(progress => {
					const lesson = lessons.find(l => l.id === progress.lesson.id);
					if (lesson) {
						progressMap[lesson.globalIndex] = {
							timeSpent: progress.time_spent * 60, // Convert minutes to seconds
							progressPercentage: progress.completion_percentage,
							isCompleted: progress.is_completed
						};

						if (progress.is_completed) {
							completedSet.add(lesson.globalIndex);

						}
					} else {
						console.error(`Lesson not found for progress:`, progress.lesson);
					}
				});
			}

			// Also check for quiz results from backend and localStorage
			if (progressData.quiz_attempts && Array.isArray(progressData.quiz_attempts)) {
				progressData.quiz_attempts.forEach(attempt => {
					if (attempt.is_passed) {
						const lesson = lessons.find(l => l.type === 'quiz' && l.quizData?.id === attempt.quiz.id);
						if (lesson) {
							progressMap[lesson.globalIndex] = {
								timeSpent: 0,
								progressPercentage: 100,
								isCompleted: true
							};
							completedSet.add(lesson.globalIndex);
						}
					}
				});
			}

			// Fallback: check localStorage for quiz results
			lessons.forEach(lesson => {
				if (lesson.type === 'quiz' && lesson.quizData?.id && !completedSet.has(lesson.globalIndex)) {
					const quizKey = `course_${courseId}_quiz_${lesson.quizData.id}_result`;
					const savedQuizResult = localStorage.getItem(quizKey);

					if (savedQuizResult) {
						try {
							const quizResult = JSON.parse(savedQuizResult);

							// Only load if it's for the current user and quiz was passed
							if (quizResult.user_id === user?.id && quizResult.score >= 80) {
								progressMap[lesson.globalIndex] = {
									timeSpent: 0,
									progressPercentage: 100,
									isCompleted: true
								};
								completedSet.add(lesson.globalIndex);
							}
						} catch (error) {
							console.error('Error parsing quiz result from localStorage:', error);
						}
					}
				}
			});

			// Also check for assignment submissions
			lessons.forEach(lesson => {
				if (lesson.type === 'assignment' && lesson.assignmentData?.id && !completedSet.has(lesson.globalIndex)) {
					const assignmentKey = `course_${courseId}_assignment_${lesson.assignmentData.id}_submission`;
					const savedAssignmentSubmission = localStorage.getItem(assignmentKey);

					if (savedAssignmentSubmission) {
						try {
							const assignmentSubmission = JSON.parse(savedAssignmentSubmission);

							// Only load if it's for the current user
							if (assignmentSubmission.user_id === user?.id) {
								progressMap[lesson.globalIndex] = {
									timeSpent: 0,
									progressPercentage: 100,
									isCompleted: true
								};
								completedSet.add(lesson.globalIndex);

								// Store submission data for UI display
								setAssignmentSubmissions(prev => ({
									...prev,
									[lesson.assignmentData.id]: assignmentSubmission
								}));
							}
						} catch (error) {
							console.error('Error parsing assignment submission from localStorage:', error);
						}
					}
				}
			});
			// Update state and pass the data directly to avoid timing issues
			setLessonProgress(progressMap);
			setCompletedLessons(completedSet);

			// Auto-navigate to first incomplete lesson, or first lesson if all completed
			let targetLesson = lessons.find(lesson => !completedSet.has(lesson.globalIndex));
			if (!targetLesson && lessons.length > 0) {
				// If all lessons completed, go to first lesson
				targetLesson = lessons[0];
			}

			if (targetLesson) {
				setCurrentLesson(targetLesson);
				// Pass the progress data directly to avoid state timing issues
				startLessonTimerWithProgress(targetLesson, progressMap, completedSet);
			}
		} catch (error) {
			console.error('Error loading existing progress from API:', error);
			console.error('Error details:', {
				status: error.response?.status,
				statusText: error.response?.statusText,
				data: error.response?.data,
				message: error.message
			});
			loadProgressFromLocalStorage(lessons);
		}
	};

	// Version that uses current state (may have timing issues)
	const startLessonTimer = (lesson) => {
		startLessonTimerWithProgress(lesson, lessonProgress, completedLessons);
	};

	// Version that accepts progress data directly (avoids timing issues)
	const startLessonTimerWithProgress = (lesson, progressMap, completedSet) => {
		const duration = lesson.duration || 10; // Default 10 minutes
		const requiredTimeInSeconds = duration * 60; // Convert to seconds

		setRequiredTime(requiredTimeInSeconds);
		setIsTimerActive(false); // Start as inactive

		// Check if lesson was already completed FIRST - use passed data
		const existingProgress = progressMap[lesson.globalIndex];
		const isInCompletedSet = completedSet.has(lesson.globalIndex);
		const isProgressCompleted = existingProgress?.isCompleted;
		const isCompleted = isInCompletedSet || isProgressCompleted;

		if (isCompleted) {
			setCanProceed(true);
			setTimeSpent(existingProgress?.timeSpent || requiredTimeInSeconds);
			setIsTimerActive(false);
		} else if (existingProgress?.timeSpent > 0) {
			setCanProceed(false);
			setTimeSpent(existingProgress.timeSpent);
			// Don't auto-start timer, let user click "Bắt đầu học"
		} else {
			setCanProceed(false);
			setTimeSpent(0);
		}
	};

	const handleLessonSelect = (lesson) => {
		// Check if lesson is locked (only for non-completed lessons)
		if (lesson.isLocked && !completedLessons.has(lesson.globalIndex - 1)) {
			alert('Bạn cần hoàn thành bài học trước đó để mở khóa bài này!');
			return;
		}

		// Check if previous lesson is completed (except for first lesson)
		// Allow access if current lesson is already completed
		const isCurrentLessonCompleted = completedLessons.has(lesson.globalIndex);

		if (lesson.globalIndex > 0 && !isCurrentLessonCompleted) {
			const prevLessonCompleted = completedLessons.has(lesson.globalIndex - 1);
			const prevLessonProgress = lessonProgress[lesson.globalIndex - 1];

			if (!prevLessonCompleted && !prevLessonProgress?.isCompleted) {
				alert('Bạn cần hoàn thành bài học trước đó trước khi chuyển sang bài này!');
				return;
			}
		}

		setCurrentLesson(lesson);
		startLessonTimer(lesson);
		// setSidebarOpen(false); // Close sidebar on mobile
	};

	const handleCompleteLesson = async () => {
		if (!canProceed) return;

		const newCompleted = new Set(completedLessons);
		newCompleted.add(currentLesson.globalIndex);
		setCompletedLessons(newCompleted);

		// Update lesson progress immediately
		setLessonProgress(prev => ({
			...prev,
			[currentLesson.globalIndex]: {
				timeSpent: timeSpent,
				progressPercentage: 100,
				isCompleted: true
			}
		}));

		// Save current lesson progress
		saveLessonProgress(currentLesson.id, {
			time_spent: Math.floor(timeSpent / 60),
			completion_percentage: 100,
			is_completed: true
		});

		// Check if this is the last lesson (course completion)
		if (currentLesson.globalIndex === lessons.length - 1) {
			try {
				// Wait a bit to ensure lesson progress is saved
				await new Promise(resolve => setTimeout(resolve, 1000));

				// Check current enrollment status before completion
				try {
					const enrollmentData = await elearningAPI.studentAPI.getEnrolledCourses();
					const currentEnrollment = enrollmentData.find(e => e.course.id === parseInt(courseId));
				} catch (error) {
					console.error('Error checking enrollment status:', error);
				}

				// Complete course via API
				const completionResult = await studentAPI.completeCourse(courseId);

				// Save completion data to localStorage as backup
				const completionData = {
					course_id: courseId,
					user_id: user?.id,
					completed_at: new Date().toISOString(),
					total_lessons: lessons.filter(l => l.type === 'lesson' || l.type === 'video').length,
					total_quizzes: lessons.filter(l => l.type === 'quiz').length,
					total_assignments: lessons.filter(l => l.type === 'assignment').length,
					completion_percentage: 100,
					certificate: completionResult.certificate,
					final_score: completionResult.completion_stats?.final_score
				};

				localStorage.setItem(`course_${courseId}_completion`, JSON.stringify(completionData));

				// Show completion page
				setShowCompletionPage(true);
				return;
			} catch (error) {
				console.error('Error completing course:', error);
				console.error('Error details:', error.response?.data);

				// Fallback to localStorage only
				const completionData = {
					course_id: courseId,
					user_id: user?.id,
					completed_at: new Date().toISOString(),
					total_lessons: lessons.filter(l => l.type === 'lesson' || l.type === 'video').length,
					total_quizzes: lessons.filter(l => l.type === 'quiz').length,
					total_assignments: lessons.filter(l => l.type === 'assignment').length,
					completion_percentage: 100
				};

				localStorage.setItem(`course_${courseId}_completion`, JSON.stringify(completionData));
				setShowCompletionPage(true);
				return;
			}
		}

		// Unlock next lesson
		const nextLessonIndex = currentLesson.globalIndex + 1;
		if (nextLessonIndex < lessons.length) {
			const updatedLessons = lessons.map(lesson =>
				lesson.globalIndex === nextLessonIndex
					? { ...lesson, isLocked: false }
					: lesson
			);
			setLessons(updatedLessons);
		}

		handleNextLesson();
	};

	// Calculate completion statistics
	const getCompletionStats = () => {
		const totalLessons = lessons.filter(l => l.type === 'lesson' || l.type === 'video').length;
		const totalQuizzes = lessons.filter(l => l.type === 'quiz').length;
		const totalAssignments = lessons.filter(l => l.type === 'assignment').length;

		const completedLessonsCount = lessons.filter(l =>
			(l.type === 'lesson' || l.type === 'video') &&
			completedLessons.has(l.globalIndex)
		).length;

		const completedQuizzesCount = lessons.filter(l =>
			l.type === 'quiz' &&
			completedLessons.has(l.globalIndex)
		).length;

		const completedAssignmentsCount = lessons.filter(l =>
			l.type === 'assignment' &&
			completedLessons.has(l.globalIndex)
		).length;

		return {
			totalLessons: completedLessonsCount,
			totalQuizzes: completedQuizzesCount,
			totalAssignments: completedAssignmentsCount,
			totalItems: totalLessons + totalQuizzes + totalAssignments,
			completedItems: completedLessonsCount + completedQuizzesCount + completedAssignmentsCount
		};
	};

	const handleNextLesson = () => {
		// Check if current lesson is completed
		if (!canProceed) {
			alert('Bạn cần hoàn thành bài học hiện tại trước khi chuyển sang bài tiếp theo!');
			return;
		}

		const nextIndex = currentLesson.globalIndex + 1;
		if (nextIndex < lessons.length) {
			const nextLesson = lessons[nextIndex];
			setCurrentLesson(nextLesson);
			startLessonTimer(nextLesson);
		}
	};

	const handlePrevLesson = () => {
		const prevIndex = currentLesson.globalIndex - 1;
		if (prevIndex >= 0) {
			const prevLesson = lessons[prevIndex];
			setCurrentLesson(prevLesson);
			startLessonTimer(prevLesson);
		}
	};

	const formatTime = (seconds) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins}:${secs.toString().padStart(2, '0')}`;
	};

	// Helper function to detect and convert video URLs from various platforms
	const getVideoEmbedInfo = (url) => {

		if (!url) return null;

		// YouTube URL patterns
		const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
		const youtubeMatch = url.match(youtubeRegex);

		if (youtubeMatch) {
			const videoId = youtubeMatch[1];
			return {
				type: 'youtube',
				platform: 'YouTube',
				embedUrl: `https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1`,
				originalUrl: url
			};
		}

		// Vimeo URL patterns
		const vimeoRegex = /(?:vimeo\.com\/)(?:.*\/)?(\d+)/;
		const vimeoMatch = url.match(vimeoRegex);

		if (vimeoMatch) {
			const videoId = vimeoMatch[1];
			return {
				type: 'vimeo',
				platform: 'Vimeo',
				embedUrl: `https://player.vimeo.com/video/${videoId}`,
				originalUrl: url
			};
		}

		// Dailymotion URL patterns
		const dailymotionRegex = /(?:dailymotion\.com\/video\/)([^_]+)/;
		const dailymotionMatch = url.match(dailymotionRegex);

		if (dailymotionMatch) {
			const videoId = dailymotionMatch[1];
			return {
				type: 'dailymotion',
				platform: 'Dailymotion',
				embedUrl: `https://www.dailymotion.com/embed/video/${videoId}`,
				originalUrl: url
			};
		}

		// Check if it's a direct video file (mp4, webm, etc.)
		if (url.match(/\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i)) {
			return {
				type: 'direct',
				platform: 'Video trực tiếp',
				embedUrl: url,
				originalUrl: url
			};
		}

		// Default to iframe for other video platforms
		return {
			type: 'iframe',
			platform: 'Video nhúng',
			embedUrl: url,
			originalUrl: url
		};
	};

	// Lesson Progress Icon Component - wraps lesson icon with circular progress
	const LessonProgressIcon = ({ lesson, isCompleted, isLocked, isCurrent }) => {
		const progress = lessonProgress[lesson.globalIndex] || {};
		const progressPercentage = progress.progressPercentage || 0;
		const showProgress = isCurrent && progressPercentage > 0 && progressPercentage < 100;

		// Get base icon with content_type from database
		const baseIcon = getLessonIcon(lesson.type, isCompleted, isLocked, lesson.content_type);

		// If no progress to show, return base icon
		if (!showProgress && !isCompleted) {
			return baseIcon;
		}

		return (
			<Box sx={{ position: 'relative', display: 'inline-flex' }}>
				{/* Circular Progress Ring */}
				{(showProgress || isCompleted) && (
					<CircularProgress
						variant="determinate"
						value={isCompleted ? 100 : progressPercentage}
						size={32}
						thickness={3}
						sx={{
							position: 'absolute',
							top: -4,
							left: -4,
							color: isCompleted ? '#4caf50' : '#1976d2',
							'& .MuiCircularProgress-circle': {
								strokeLinecap: 'round',
							},
						}}
					/>
				)}

				{/* Base Icon */}
				<Box sx={{
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					width: 24,
					height: 24
				}}>
					{baseIcon}
				</Box>
			</Box>
		);
	};

	const getLessonIcon = (type, isCompleted, isLocked, contentType = null) => {
		if (isLocked) return <LockIcon sx={{ color: '#bdbdbd' }} />;
		if (isCompleted) return <CheckIcon sx={{ color: '#4caf50' }} />;

		// Use content_type from database if available, otherwise fall back to type
		const iconType = contentType || type;

		switch (iconType) {
			case 'text': return <DescriptionIcon sx={{ color: '#1976d2' }} />;
			case 'video': return <VideocamIcon sx={{ color: '#1a237e' }} />;
			case 'file': return <DescriptionIcon sx={{ color: '#ff9800' }} />;
			case 'assignment': return <AssignmentIcon sx={{ color: '#2e7d32' }} />;
			case 'quiz': return <QuizIcon sx={{ color: '#ed6c02' }} />;
			case 'project': return <CodeIcon sx={{ color: '#9c27b0' }} />;
			default: return <DescriptionIcon sx={{ color: '#1976d2' }} />; // Default to text icon
		}
	};

	const renderLessonContent = () => {
		if (!currentLesson) return null;

		// Handle different content types from database
		const contentType = currentLesson.content_type || currentLesson.type;

		switch (contentType) {
			case 'text':
				return renderTextLesson();
			case 'video':
				return renderVideoLesson();
			case 'file':
				return renderFileLesson();
			case 'quiz':
				return renderQuizLesson();
			case 'assignment':
				return renderAssignmentLesson();
			case 'project':
				return renderTurboWarpLesson();
			default:
				// Fallback based on legacy type field
				if (currentLesson.type === 'video') return renderVideoLesson();
				if (currentLesson.type === 'quiz') return renderQuizLesson();
				if (currentLesson.type === 'assignment') return renderAssignmentLesson();
				return renderTextLesson(); // Default to text
		}
	};

	// Render Text Lesson
	const renderTextLesson = () => (
		<Box>
			<Box sx={{ display: "flex", alignItems: "center" }}>
				<ArticleIcon sx={{ fontSize: "32px", color: '#1a237e', mr: 1, mb: 1 }} />
				<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
					{currentLesson.title}
				</Typography>
			</Box>

			{/* Show lesson description */}
			{currentLesson.description && (
				<Alert severity="info" sx={{ mb: 4 }}>
					{currentLesson.description}
				</Alert>
			)}

			{/* Completion Status */}
			{canProceed && (
				<Alert severity="success" sx={{ mb: 4 }}>
					<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
						<CheckIcon sx={{ color: '#4caf50' }} />
						<Typography variant="body1" fontWeight="bold">
							🎉 Bài học đã hoàn thành! Bạn có thể chuyển sang bài tiếp theo.
						</Typography>
					</Box>
				</Alert>
			)}

			{/* Timer Control */}
			{!canProceed && (
				<Card sx={{ p: 3, mb: 4, bgcolor: 'rgba(25, 118, 210, 0.05)', border: '1px solid rgba(25, 118, 210, 0.2)' }}>
					<Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
						{!isTimerActive ? (
							<Button
								variant="contained"
								startIcon={<PlayIcon />}
								onClick={() => setIsTimerActive(true)}
								sx={{
									background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
									'&:hover': { transform: 'translateY(-2px)' }
								}}
							>
								{timeSpent > 0 ? 'Tiếp tục học' : 'Bắt đầu học'}
							</Button>
						) : (
							<Button
								variant="outlined"
								startIcon={<PauseIcon />}
								onClick={() => setIsTimerActive(false)}
								sx={{
									borderColor: '#1976d2',
									color: '#1976d2',
									'&:hover': { borderColor: '#1976d2', bgcolor: 'rgba(25, 118, 210, 0.1)' }
								}}
							>
								Tạm dừng
							</Button>
						)}

						<Typography variant="body2" color="textSecondary">
							{isTimerActive ? '⏳ Đang học...' : '⏸️ Đã tạm dừng'}
						</Typography>
					</Box>
				</Card>
			)}

			{/* Debug Controls - Remove in production */}
			<Card sx={{ p: 2, mb: 2, bgcolor: 'rgba(255, 152, 0, 0.05)', border: '1px solid rgba(255, 152, 0, 0.2)' }}>
				<Typography variant="body2" fontWeight="bold" gutterBottom>🧪 Debug Controls</Typography>
				<Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>

				</Box>
			</Card>

			{/* Main text content */}
			<Card sx={{ p: 4, mb: 4, bgcolor: 'rgba(26, 35, 126, 0.02)' }}>
				{currentLesson.content ? (
					<Box
						sx={{
							'& h1, & h2, & h3, & h4, & h5, & h6': {
								color: '#1a237e',
								fontWeight: 'bold',
								mb: 2
							},
							'& p': {
								lineHeight: 1.8,
								mb: 2,
								fontSize: '1.1rem'
							},
							'& ul, & ol': {
								pl: 3,
								mb: 2
							},
							'& li': {
								mb: 1,
								lineHeight: 1.6
							},
							'& strong': {
								color: '#1a237e',
								fontWeight: 'bold'
							},
							'& code': {
								bgcolor: '#f5f5f5',
								p: 0.5,
								borderRadius: 1,
								fontFamily: 'monospace'
							},
							'& pre': {
								bgcolor: '#f5f5f5',
								p: 2,
								borderRadius: 2,
								overflow: 'auto',
								mb: 2
							}
						}}
						dangerouslySetInnerHTML={{ __html: currentLesson.content }}
					/>
				) : (
					<Typography variant="body1" sx={{ lineHeight: 1.8, fontStyle: 'italic', color: 'text.secondary' }}>
						Nội dung bài học đang được cập nhật...
					</Typography>
				)}
			</Card>
		</Box>
	);

	// Render Video Lesson
	const renderVideoLesson = () => (
		<Box>
			<Box sx={{ display: "flex", alignItems: "center" }}>
				<PersonalVideoIcon sx={{ fontSize: "32px", color: '#1a237e', mr: 1, mb: 1 }} />
				<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e', alignItems: "center" }}>
					{currentLesson.title}
				</Typography>
			</Box>

			{/* Timer Control */}
			{!canProceed && (
				<Card sx={{ p: 3, mb: 4, bgcolor: 'rgba(25, 118, 210, 0.05)', border: '1px solid rgba(25, 118, 210, 0.2)' }}>
					<Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
						{!isTimerActive ? (
							<Button
								variant="contained"
								startIcon={<PlayIcon />}
								onClick={() => setIsTimerActive(true)}
								sx={{
									background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
									'&:hover': { transform: 'translateY(-2px)' }
								}}
							>
								{timeSpent > 0 ? 'Tiếp tục học' : 'Bắt đầu học'}
							</Button>
						) : (
							<Button
								variant="outlined"
								startIcon={<PauseIcon />}
								onClick={() => setIsTimerActive(false)}
								sx={{
									borderColor: '#1976d2',
									color: '#1976d2',
									'&:hover': { borderColor: '#1976d2', bgcolor: 'rgba(25, 118, 210, 0.1)' }
								}}
							>
								Tạm dừng
							</Button>
						)}

						<Typography variant="body2" color="textSecondary">
							{isTimerActive ? '⏳ Đang học...' : '⏸️ Đã tạm dừng'}
						</Typography>
					</Box>
				</Card>
			)}
			{/* Completion Status */}
			{canProceed && (
				<Alert severity="success" sx={{ mb: 2 }}>
					<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
						{/* <CheckIcon sx={{ color: '#4caf50' }} /> */}
						<Typography variant="body1" fontWeight="bold">
							🎉 Video đã xem hoàn thành! Bạn có thể chuyển sang bài tiếp theo.
						</Typography>
					</Box>
				</Alert>
			)}

			<Box sx={{
				height: 400,
				bgcolor: '#000',
				borderRadius: 3,
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				mb: 4,
				position: 'relative'
			}}>
				{/* Smart video player that handles YouTube, direct videos, and other platforms */}
				{(() => {
					const videoUrl = currentLesson.video_url || currentLesson.content;
					const videoInfo = getVideoEmbedInfo(videoUrl);

					if (!videoInfo) {
						return (
							<>
								<PlayIcon sx={{ fontSize: 80, color: 'white' }} />
								<Typography variant="h6" sx={{ color: 'white', position: 'absolute', bottom: 16, left: 16 }}>
									Video: {currentLesson.title}
								</Typography>
							</>
						);
					}

					if (videoInfo.type === 'youtube' || videoInfo.type === 'vimeo' || videoInfo.type === 'dailymotion') {
						return (
							<iframe
								width="100%"
								height="100%"
								src={videoInfo.embedUrl}
								title={currentLesson.title}
								allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
								allowFullScreen
								style={{ borderRadius: '12px', border: 'none' }}
							/>
						);
					}

					if (videoInfo.type === 'direct') {
						return (
							<video
								controls
								style={{ width: '100%', height: '100%', borderRadius: '12px' }}
								src={videoInfo.embedUrl}
							>
								Your browser does not support the video tag.
							</video>
						);
					}

					// For other platforms, use iframe
					return (
						<iframe
							width="100%"
							height="100%"
							src={videoInfo.embedUrl}
							title={currentLesson.title}
							allowFullScreen
							style={{ borderRadius: '12px', border: 'none' }}
						/>
					);
				})()}
			</Box>
			<Divider sx={{ mb: 4 }} />
			<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
				Nội dung bài học:
			</Typography>
			<Typography variant="body1" sx={{ lineHeight: 1.8, mb: 4 }}>
				{currentLesson.description || currentLesson.content ||
					'Nội dung bài học video sẽ được hiển thị ở đây. Học viên cần xem hết video trong thời gian quy định để có thể chuyển sang bài học tiếp theo.'}
			</Typography>

			{/* Show lesson details from database */}
			{/* {currentLesson.content && !currentLesson.content.includes('http') && (
				<Card sx={{ p: 3, mb: 4, bgcolor: 'rgba(26, 35, 126, 0.05)' }}>
					<Typography variant="h6" fontWeight="bold" gutterBottom>
						📝 Nội dung bài học:
					</Typography>
					<div dangerouslySetInnerHTML={{ __html: currentLesson.content }} />
				</Card>
			)} */}
		</Box>
	);

	// Render File Lesson
	const renderFileLesson = () => (
		<Box>
			<Box sx={{ display: "flex", alignItems: "center" }}>
				<AttachFileIcon sx={{ fontSize: "32px", color: '#1a237e', mr: 1, mb: 1 }} />
				<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
					{currentLesson.title}
				</Typography>
			</Box>

			{/* Show lesson description */}
			{currentLesson.description && (
				<Alert severity="info" sx={{ mb: 4 }}>
					{currentLesson.description}
				</Alert>
			)}

			{/* File download/view section */}
			<Card sx={{ p: 4, mb: 4, bgcolor: 'rgba(26, 35, 126, 0.02)' }}>
				{currentLesson.file ? (
					<Box sx={{ textAlign: 'center' }}>
						<Box sx={{
							display: 'flex',
							flexDirection: 'column',
							alignItems: 'center',
							p: 4,
							border: '2px dashed #1976d2',
							borderRadius: 3,
							bgcolor: 'rgba(25, 118, 210, 0.05)',
							mb: 3
						}}>
							<DescriptionIcon sx={{ fontSize: 64, color: '#1976d2', mb: 2 }} />
							<Typography variant="h6" fontWeight="bold" gutterBottom>
								Tài liệu bài học
							</Typography>
							<Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
								Click để tải xuống hoặc xem tài liệu
							</Typography>
							<Button
								variant="contained"
								startIcon={<DownloadIcon />}
								href={currentLesson.file}
								target="_blank"
								sx={{
									background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
									'&:hover': { transform: 'translateY(-2px)' }
								}}
							>
								Tải xuống tài liệu
							</Button>
						</Box>

						{/* Additional text content if available */}
						{currentLesson.content && (
							<Box sx={{ textAlign: 'left', mt: 3 }}>
								<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
									📝 Ghi chú bổ sung:
								</Typography>
								<Box
									sx={{
										'& p': { lineHeight: 1.8, mb: 2 },
										'& ul, & ol': { pl: 3, mb: 2 },
										'& li': { mb: 1 }
									}}
									dangerouslySetInnerHTML={{ __html: currentLesson.content }}
								/>
							</Box>
						)}
					</Box>
				) : (
					<Box sx={{ textAlign: 'center', py: 4 }}>
						<DescriptionIcon sx={{ fontSize: 64, color: '#bdbdbd', mb: 2 }} />
						<Typography variant="h6" color="textSecondary" gutterBottom>
							Chưa có tài liệu
						</Typography>
						<Typography variant="body2" color="textSecondary">
							Tài liệu cho bài học này đang được cập nhật
						</Typography>
					</Box>
				)}
			</Card>
		</Box>
	);

	// Handle single choice answer
	const handleSingleChoiceAnswer = (questionId, answerIndex) => {
		setQuizAnswers(prev => ({
			...prev,
			[questionId]: answerIndex
		}));
	};

	// Handle multiple choice answer
	const handleMultipleChoiceAnswer = (questionId, answerIndex) => {
		setMultipleChoiceAnswers(prev => {
			const currentAnswers = prev[questionId] || [];
			const isSelected = currentAnswers.includes(answerIndex);

			if (isSelected) {
				// Remove answer if already selected
				return {
					...prev,
					[questionId]: currentAnswers.filter(index => index !== answerIndex)
				};
			} else {
				// Add answer if not selected
				return {
					...prev,
					[questionId]: [...currentAnswers, answerIndex]
				};
			}
		});
	};

	// Check if answer is selected
	const isAnswerSelected = (questionId, answerIndex, isMultiple) => {
		if (isMultiple) {
			const answers = multipleChoiceAnswers[questionId] || [];
			return answers.includes(answerIndex);
		} else {
			return quizAnswers[questionId] === answerIndex;
		}
	};

	// Calculate how many questions have been answered
	const getAnsweredQuestionsCount = (questions) => {
		let answeredCount = 0;
		questions.forEach(q => {
			if (q.correct_answers.length > 1) {
				// Multiple choice - check if any answers selected
				if (multipleChoiceAnswers[q.id] && multipleChoiceAnswers[q.id].length > 0) {
					answeredCount++;
				}
			} else {
				// Single choice - check if answer selected
				if (quizAnswers[q.id] !== undefined) {
					answeredCount++;
				}
			}
		});
		return answeredCount;
	};

	const renderQuizLesson = () => {
		// Check if quiz is already completed
		const isQuizCompleted = canProceed || completedLessons.has(currentLesson.globalIndex);

		// Use quiz data from database if available, otherwise use sample questions
		const quizData = currentLesson.quizData;
		const questions = quizData?.questions || [];

		return (
			<Box>
				<Box sx={{ display: "flex", alignItems: "center" }}>
					<QuizIcon sx={{ fontSize: "32px", color: '#1a237e', mr: 1, mb: 1 }} />
					<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
						{currentLesson.title}
					</Typography>
				</Box>

				{/* Show quiz description from database */}
				{currentLesson.description && (
					<Card sx={{ p: 3, mb: 4, bgcolor: 'rgba(26, 35, 126, 0.05)' }}>
						<Typography variant="body1" sx={{ lineHeight: 1.8 }}>
							{currentLesson.description}
						</Typography>
					</Card>
				)}

				{/* Quiz Completion Status */}
				{isQuizCompleted && (
					<Alert severity="success" sx={{ mb: 2 }}>
						<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
							{/* <CheckIcon sx={{ color: '#4caf50' }} /> */}
							<Typography variant="body1" fontWeight="bold">
								🎉 Quiz đã hoàn thành! Bạn có thể chuyển sang bài tiếp theo.
							</Typography>
						</Box>
					</Alert>
				)}
				{!isQuizCompleted && (
					<Alert severity="info" sx={{ mb: 4 }}>
						Hoàn thành quiz để tiếp tục bài học tiếp theo.
						{quizData?.passing_score ? ` Bạn cần đạt ít nhất ${quizData.passing_score}% để pass.` : ' Bạn cần đạt ít nhất 80% để pass.'}
						{quizData?.time_limit && ` Thời gian làm bài: ${quizData.time_limit} phút.`}
					</Alert>
				)}

				{questions.map((q, index) => (
					<Card key={q.id} sx={{ mb: 3, p: 3, border: '1px solid rgba(26, 35, 126, 0.1)' }}>
						<Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
							<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
								Câu {index + 1}: {q.question_text || q.question}
							</Typography>
							{q.correct_answers.length > 1 && (
								<Chip
									label="Chọn nhiều đáp án"
									size="small"
									color="primary"
									variant="outlined"
								/>
							)}
						</Box>

						<Stack spacing={1}>
							{q.options.map((option, optIndex) => {
								const isSelected = isAnswerSelected(q.id, optIndex, q.correct_answers.length > 1);

								return q.correct_answers.length > 1 ? (
									// Multiple choice - use FormControlLabel with Checkbox
									<FormControlLabel
										key={optIndex}
										control={
											<Checkbox
												checked={isSelected}
												onChange={() => handleMultipleChoiceAnswer(q.id, optIndex)}
												sx={{ color: '#1a237e' }}
											/>
										}
										label={
											<Typography variant="body1" sx={{ ml: 1 }}>
												{option}
											</Typography>
										}
										sx={{
											m: 0,
											p: 2,
											border: '1px solid',
											borderColor: isSelected ? '#1a237e' : 'rgba(26, 35, 126, 0.3)',
											borderRadius: 2,
											bgcolor: isSelected ? 'rgba(26, 35, 126, 0.05)' : 'transparent',
											'&:hover': {
												bgcolor: 'rgba(26, 35, 126, 0.05)'
											}
										}}
									/>
								) : (
									// Single choice - use Button
									<Button
										key={optIndex}
										variant={isSelected ? "contained" : "outlined"}
										onClick={() => handleSingleChoiceAnswer(q.id, optIndex)}
										sx={{
											justifyContent: 'flex-start',
											textAlign: 'left',
											p: 2,
											borderColor: '#1a237e',
											color: isSelected ? 'white' : '#1a237e',
											bgcolor: isSelected ? '#1a237e' : 'transparent',
											'&:hover': {
												bgcolor: isSelected ? '#1a237e' : 'rgba(26, 35, 126, 0.1)'
											}
										}}
									>
										{String.fromCharCode(65 + optIndex)}. {option}
									</Button>
								);
							})}
						</Stack>
					</Card>
				))}

				{/* Quiz Progress */}
				<Card sx={{ mb: 3, p: 3, bgcolor: 'rgba(26, 35, 126, 0.05)' }}>
					<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
						Tiến độ làm bài
					</Typography>

					<Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
						<Typography variant="body2" fontWeight="bold">
							Đã trả lời: {getAnsweredQuestionsCount(questions)} / {questions.length} câu
						</Typography>
						<LinearProgress
							variant="determinate"
							value={(getAnsweredQuestionsCount(questions) / questions.length) * 100}
							sx={{
								flexGrow: 1,
								height: 8,
								borderRadius: 4,
								bgcolor: '#e0e0e0',
								'& .MuiLinearProgress-bar': {
									bgcolor: getAnsweredQuestionsCount(questions) === questions.length ? '#4caf50' : '#1976d2'
								}
							}}
						/>
						<Typography variant="caption" fontWeight="bold" sx={{
							color: getAnsweredQuestionsCount(questions) === questions.length ? '#4caf50' : '#1976d2'
						}}>
							{Math.round((getAnsweredQuestionsCount(questions) / questions.length) * 100)}%
						</Typography>
					</Box>

					{/* <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
						{getAnsweredQuestionsCount(questions) === questions.length ? (
							<Typography variant="caption" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
								Đã trả lời đầy đủ - Có thể nộp bài
							</Typography>
						) : (
							<Typography variant="caption" color="textSecondary">
								Còn {questions.length - getAnsweredQuestionsCount(questions)} câu chưa trả lời
							</Typography>
						)}
					</Box> */}

					<Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 1 }}>
						*Câu hỏi có nhãn "Chọn nhiều đáp án" có thể chọn nhiều hơn 1 đáp án đúng
					</Typography>
				</Card>

				<Button
					variant="contained"
					size="large"
					onClick={async () => {
						// Check if all questions are answered
						const answeredCount = getAnsweredQuestionsCount(questions);
						if (answeredCount < questions.length) {
							alert(`⚠️ Vui lòng trả lời tất cả câu hỏi!\n\nĐã trả lời: ${answeredCount}/${questions.length} câu`);
							return;
						}

						setIsSubmittingQuiz(true);

						try {
							// Check answers for both single and multiple choice questions
							let totalCorrect = 0;

							questions.forEach(q => {
								if (q.correct_answers.length > 1) {
									// Multiple choice question
									const userAnswers = multipleChoiceAnswers[q.id] || [];
									const correctAnswers = q.correct_answers || [];

									// Check if user selected exactly the correct answers
									const isCorrect = userAnswers.length === correctAnswers.length &&
										userAnswers.every(answer => correctAnswers.includes(answer)) &&
										correctAnswers.every(answer => userAnswers.includes(answer));

									if (isCorrect) totalCorrect++;
								} else {
									// Single choice question
									const userAnswer = quizAnswers[q.id];
									const correctAnswers = q.correct_answers || [q.correct]; // Support both formats

									if (correctAnswers.includes(userAnswer)) {
										totalCorrect++;
									}
								}
							});

							const score = Math.round((totalCorrect / questions.length) * 100);
							const passingScore = quizData?.passing_score || 80;

							// Prepare answers for backend
							const allAnswers = {};
							questions.forEach(q => {
								if (q.correct_answers.length > 1) {
									// Multiple choice
									allAnswers[q.id] = multipleChoiceAnswers[q.id] || [];
								} else {
									// Single choice
									allAnswers[q.id] = quizAnswers[q.id] !== undefined ? [quizAnswers[q.id]] : [];
								}
							});

							if (currentLesson.type === 'quiz' && currentLesson.quizData?.id) {
								try {
									const result = await saveQuizAttempt(currentLesson.quizData.id, allAnswers, score);
								} catch (error) {
									console.error('Failed to save quiz attempt:', error);
									console.error('Error details:', {
										status: error.response?.status,
										statusText: error.response?.statusText,
										data: error.response?.data,
										message: error.message
									});
								}
							} else {
								console.error('Quiz not saved - missing quiz data:', {
									type: currentLesson.type,
									quizData: currentLesson.quizData
								});
							}

							if (score >= passingScore) {
								setCanProceed(true);

								// Use the new handleQuizCompletion function
								await handleQuizCompletion(currentLesson.quizData?.id, score);

								alert(`🎉 Chúc mừng! Bạn đạt ${score}% - Pass quiz!\n\nĐáp án đúng: ${totalCorrect}/${questions.length}`);
							} else {
								alert(`📝 Bạn đạt ${score}%. Cần ít nhất ${passingScore}% để pass.\n\nĐáp án đúng: ${totalCorrect}/${questions.length}\n\nHãy xem lại và thử lại!`);
							}
						} catch (error) {
							console.error('Error submitting quiz:', error);
							alert('Có lỗi xảy ra khi nộp bài. Vui lòng thử lại!');
						} finally {
							setIsSubmittingQuiz(false);
						}
					}}
					disabled={isQuizCompleted || getAnsweredQuestionsCount(questions) < questions.length || isSubmittingQuiz}
					sx={{
						borderRadius: "10px",
						background: getAnsweredQuestionsCount(questions) === questions.length && !isSubmittingQuiz
							? 'linear-gradient(45deg, #1a237e, #3949ab)'
							: 'linear-gradient(45deg, #bdbdbd, #9e9e9e)',
						'&:hover': {
							transform: getAnsweredQuestionsCount(questions) === questions.length && !isSubmittingQuiz ? 'translateY(-2px)' : 'none'
						},
						'&.Mui-disabled': {
							color: 'white'
						}
					}}
				>
					{isQuizCompleted ? (
						'Quiz đã hoàn thành'
					) : isSubmittingQuiz ? (
						<>
							<CircularProgress size={20} sx={{ mr: 1, color: 'white' }} />
							Đang nộp bài...
						</>
					) : getAnsweredQuestionsCount(questions) === questions.length ? (
						'Nộp bài Quiz'
					) : (
						`Trả lời ${questions.length - getAnsweredQuestionsCount(questions)} câu còn lại`
					)}
				</Button>
			</Box>
		);
	};

	const renderAssignmentLesson = () => {
		const assignmentData = currentLesson.assignmentData;
		const isAssignmentCompleted = canProceed || completedLessons.has(currentLesson.globalIndex);
		const existingSubmission = assignmentSubmissions[assignmentData?.id];

		return (
			<Box>
				<Box sx={{ display: "flex", alignItems: "center" }}>
					<PlayLessonIcon sx={{ fontSize: "32px", color: '#1a237e', mr: 1, mb: 1 }} />
					<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
						{currentLesson.title}
					</Typography>
				</Box>

				{/* Show assignment description from database */}
				{currentLesson.description && (
					<Card sx={{ p: 3, mb: 4, bgcolor: 'rgba(26, 35, 126, 0.05)' }}>
						<Typography variant="body1" sx={{ lineHeight: 1.8 }}>
							{currentLesson.description}
						</Typography>
					</Card>
				)}

				{/* Assignment Completion Status */}
				{isAssignmentCompleted && (
					<Alert severity="success" sx={{ mb: 2 }}>
						<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
							{/* <CheckIcon sx={{ color: '#4caf50' }} /> */}
							<Typography variant="body1" fontWeight="bold">
								🎉 Bài tập đã nộp thành công! Bạn có thể chuyển sang bài tiếp theo.
							</Typography>
						</Box>
						{existingSubmission && (
							<Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
								File đã nộp: {existingSubmission.file_name} ({(existingSubmission.file_size / 1024 / 1024).toFixed(2)} MB)
							</Typography>
						)}
					</Alert>
				)}

				{!isAssignmentCompleted && (
					<Alert severity="warning" sx={{ mb: 2 }}>
						Hoàn thành bài tập và upload file để tiếp tục.
						{assignmentData?.due_date && ` Hạn nộp: ${new Date(assignmentData.due_date).toLocaleDateString('vi-VN')}`}
					</Alert>
				)}

				<Card sx={{ p: 3, mb: 4, bgcolor: 'rgba(26, 35, 126, 0.05)' }}>
					<Typography variant="h6" fontWeight="bold" gutterBottom>
						Yêu cầu bài tập:
					</Typography>
					{assignmentData?.instructions ? (
						<Typography variant="body1" sx={{ lineHeight: 1.8, whiteSpace: 'pre-wrap' }}>
							{assignmentData.instructions}
						</Typography>
					) : (
						<>
							<Typography variant="body1" sx={{ lineHeight: 1.8, mb: 2 }}>
								1. Tạo một dự án STEM đơn giản sử dụng kiến thức đã học
							</Typography>
							<Typography variant="body1" sx={{ lineHeight: 1.8, mb: 2 }}>
								2. Viết báo cáo ngắn (500-1000 từ) mô tả quá trình thực hiện
							</Typography>
							<Typography variant="body1" sx={{ lineHeight: 1.8, mb: 2 }}>
								3. Upload file báo cáo dưới định dạng PDF hoặc Word
							</Typography>
						</>
					)}
				</Card>

				{/* File Upload Section */}
				{!isAssignmentCompleted && (
					<Card sx={{ p: 3, mb: 4 }}>
						<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: '#1976d2' }}>
							📁 Upload bài tập:
						</Typography>

						<Box sx={{
							border: '2px dashed #1976d2',
							borderRadius: 2,
							p: 3,
							textAlign: 'center',
							bgcolor: assignmentFile ? 'rgba(25, 118, 210, 0.05)' : 'rgba(0, 0, 0, 0.02)',
							cursor: 'pointer',
							transition: 'all 0.3s ease',
							mb: 2,
							'&:hover': {
								bgcolor: 'rgba(25, 118, 210, 0.05)',
								borderColor: '#1565c0'
							}
						}}>
							<input
								type="file"
								id="assignment-file-input"
								style={{ display: 'none' }}
								accept=".pdf,.doc,.docx,.txt,.zip,.rar"
								onChange={(e) => {
									const file = e.target.files[0];
									if (file) {
										// Check file size (10MB limit)
										if (file.size > 10 * 1024 * 1024) {
											alert('File quá lớn! Vui lòng chọn file nhỏ hơn 10MB.');
											return;
										}
										setAssignmentFile(file);
									}
								}}
							/>
							<label htmlFor="assignment-file-input" style={{ cursor: 'pointer', width: '100%', display: 'block' }}>
								{assignmentFile ? (
									<Box>
										<Typography variant="h6" sx={{ color: '#1976d2', mb: 1 }}>
											✅ {assignmentFile.name}
										</Typography>
										<Typography variant="body2" color="textSecondary">
											Kích thước: {(assignmentFile.size / 1024 / 1024).toFixed(2)} MB
										</Typography>
										<Typography variant="body2" sx={{ color: '#1976d2', mt: 1 }}>
											Nhấp để chọn file khác
										</Typography>
									</Box>
								) : (
									<Box>
										<Typography variant="h6" sx={{ color: '#666', mb: 1 }}>
											📁 Nhấp để chọn file
										</Typography>
										<Typography variant="body2" color="textSecondary">
											Hỗ trợ: PDF, DOC, DOCX, TXT, ZIP, RAR (tối đa 10MB)
										</Typography>
									</Box>
								)}
							</label>
						</Box>

						<Typography variant="body2" color="textSecondary">
							Chấp nhận file: PDF, DOC, DOCX, TXT, ZIP, RAR (tối đa 10MB)
						</Typography>
					</Card>
				)}

				{/* Submit Assignment Button */}
				{!isAssignmentCompleted ? (
					<Button
						variant="contained"
						size="large"
						disabled={!assignmentFile}
						onClick={async () => {
							if (!assignmentFile) {
								alert('Vui lòng chọn file để nộp bài!');
								return;
							}

							try {
								// Save assignment submission
								await saveAssignmentSubmission(assignmentData?.id || currentLesson.id, assignmentFile);

								// Mark as completed using the new function
								setCanProceed(true);
								await handleAssignmentCompletion(assignmentData?.id || currentLesson.id);

								alert(`🎉 Nộp bài thành công!\n\nFile: ${assignmentFile.name}\nKích thước: ${(assignmentFile.size / 1024 / 1024).toFixed(2)} MB`);
							} catch (error) {
								console.error('Error submitting assignment:', error);
								alert('Có lỗi xảy ra khi nộp bài. Vui lòng thử lại!');
							}
						}}
						sx={{
							borderRadius: "10px",
							background: assignmentFile ? 'linear-gradient(45deg, #2e7d32, #4caf50)' : 'linear-gradient(45deg, #ccc, #ddd)',
							color: 'white',
							fontWeight: 'bold',
							py: 1.5,
							px: 4,
							'&:hover': {
								background: assignmentFile ? 'linear-gradient(45deg, #1b5e20, #2e7d32)' : 'linear-gradient(45deg, #ccc, #ddd)',
								transform: assignmentFile ? 'translateY(-2px)' : 'none',
								boxShadow: assignmentFile ? '0 8px 25px rgba(46, 125, 50, 0.3)' : 'none'
							},
							'&:disabled': {
								background: 'linear-gradient(45deg, #ccc, #ddd)',
								color: '#999'
							}
						}}
					>
						{assignmentFile ? 'Nộp bài tập' : 'Chọn file trước'}
					</Button>
				) : (
					<Button
						variant="outlined"
						size="large"
						disabled
						sx={{
							borderRadius: "10px",
							borderColor: '#4caf50',
							color: '#4caf50',
							fontWeight: 'bold',
							py: 1.5,
							px: 4
						}}
						startIcon={<CheckIcon />}
					>
						Đã nộp bài
					</Button>
				)}
			</Box>
		);
	};

	const renderTurboWarpLesson = () => (
		<Box>
			<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
				🤖 {currentLesson.title}
			</Typography>

			<Alert severity="info" sx={{ mb: 4 }}>
				Sử dụng TurboWarp để tạo dự án Scratch. Hoàn thành dự án để tiếp tục.
			</Alert>

			<Card sx={{ p: 3, mb: 4, bgcolor: 'rgba(156, 39, 176, 0.05)' }}>
				<Typography variant="h6" fontWeight="bold" gutterBottom>
					Nhiệm vụ dự án:
				</Typography>
				<Typography variant="body1" sx={{ lineHeight: 1.8, mb: 2 }}>
					• Tạo một game hoặc animation đơn giản
				</Typography>
				<Typography variant="body1" sx={{ lineHeight: 1.8, mb: 2 }}>
					• Sử dụng ít nhất 3 sprites và 5 blocks khác nhau
				</Typography>
				<Typography variant="body1" sx={{ lineHeight: 1.8, mb: 2 }}>
					• Thêm âm thanh và hiệu ứng
				</Typography>
			</Card>

			<Card sx={{
				height: 500,
				bgcolor: '#f0f0f0',
				borderRadius: 3,
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				mb: 4,
				border: '2px dashed #9c27b0'
			}}>
				<Box sx={{ textAlign: 'center' }}>
					<CodeIcon sx={{ fontSize: 80, color: '#9c27b0', mb: 2 }} />
					<Typography variant="h6" color="#9c27b0" gutterBottom>
						TurboWarp Editor
					</Typography>
					<Typography variant="body2" color="textSecondary">
						Giao diện TurboWarp sẽ được nhúng ở đây
					</Typography>
					<Button
						variant="contained"
						sx={{
							mt: 2,
							bgcolor: '#9c27b0',
							'&:hover': { bgcolor: '#7b1fa2' }
						}}
						onClick={() => window.open('https://turbowarp.org/', '_blank')}
					>
						Mở TurboWarp
					</Button>
				</Box>
			</Card>

			<Button
				variant="contained"
				size="large"
				onClick={() => {
					setCanProceed(true);
					alert('Dự án TurboWarp đã được lưu thành công!');
				}}
				sx={{
					background: 'linear-gradient(45deg, #9c27b0, #e91e63)',
					'&:hover': { transform: 'translateY(-2px)' }
				}}
			>
				Lưu dự án
			</Button>
		</Box>
	);

	if (loading) {
		return (
			<Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
				<CircularProgress size={60} sx={{ color: '#1a237e' }} />
			</Box>
		);
	}

	if (error) {
		return (
			<Container maxWidth="md" sx={{ py: 8 }}>
				<Alert severity="error">{error}</Alert>
			</Container>
		);
	}

	// Show completion page if course is completed
	if (showCompletionPage) {
		return (
			<CourseCompletionPage
				course={course}
				user={user}
				completionStats={getCompletionStats()}
			/>
		);
	}

	return (
		<Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: '#f8fafc' }}>
			{/* BeE STEM Header */}
			<AppBar
				position="fixed"
				sx={{
					bgcolor: '#1a237e',
					zIndex: (theme) => theme.zIndex.drawer + 1
				}}
			>
				<Toolbar>
					<IconButton
						color="inherit"
						onClick={() => setSidebarOpen(!sidebarOpen)}
						sx={{ mr: 2 }}
					>
						<MenuIcon />
					</IconButton>

					<Box sx={{ display: 'flex', alignItems: 'center', mr: 4 }} onClick={() => navigate('/e-learning')}>
						<SchoolIcon sx={{ mr: 1, color: '#ffc107' }} />
						<Typography variant="h6" fontWeight="bold">
							BeE STEM Learning
						</Typography>
					</Box>

					<Typography variant="h6" sx={{ flexGrow: 1 }}>
						{course?.title}
					</Typography>

					<Stack direction="row" spacing={2} alignItems="center">
						<Button
							color="inherit"
							startIcon={<DashboardIcon />}
							onClick={() => navigate('/e-learning/dashboard')}
						>
							Dashboard
						</Button>
						{/* <Button
							color="inherit"
							startIcon={<HomeIcon />}
							onClick={() => navigate('/e-learning')}
						>
							Trang chủ
						</Button> */}

						{/* User Info */}
						{user && (
							<Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2 }}>
								<Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
									Xin chào,
								</Typography>
								<Typography variant="body2" fontWeight="bold" sx={{ color: '#ffc107' }}>
									{user.first_name} {user.last_name}
								</Typography>
							</Box>
						)}
					</Stack>
				</Toolbar>
			</AppBar>

			{/* Sidebar - Course Navigation */}
			<Drawer
				variant="persistent"
				anchor="left"
				open={sidebarOpen}
				sx={{
					width: { xs: '100%', md: 360 },
					flexShrink: 0,
					'& .MuiDrawer-paper': {
						width: { xs: '100%', md: 360 },
						boxSizing: 'border-box',
						bgcolor: 'white',
						borderRight: '1px solid rgba(26, 35, 126, 0.1)',
						mt: 8,
						maxHeight: 'calc(100vh - 64px)',
						overflowY: 'auto'
					},
				}}
			>
				<Box sx={{ p: 3 }}>
					<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e', mb: 2 }}>
						Nội dung khóa học
					</Typography>

					{/* Overall Progress */}
					<Card sx={{ mb: 3, p: 2, bgcolor: 'rgba(26, 35, 126, 0.05)' }}>
						<Typography variant="body2" color="textSecondary" gutterBottom>
							Tiến độ tổng thể
						</Typography>
						<Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
							<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
								{Math.round((completedLessons.size / lessons.length) * 100)}%
							</Typography>
							<Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
								({completedLessons.size}/{lessons.length} bài)
							</Typography>
						</Box>
						<LinearProgress
							variant="determinate"
							value={(completedLessons.size / lessons.length) * 100}
							sx={{
								height: 8,
								borderRadius: 4,
								bgcolor: '#e0e0e0',
								'& .MuiLinearProgress-bar': {
									bgcolor: '#ffc107',
									borderRadius: 4
								}
							}}
						/>
					</Card>

					{/* Current Lesson Progress */}
					{currentLesson && isTimerActive && (
						<Card sx={{ mb: 1, p: 2, bgcolor: 'rgba(25, 118, 210, 0.05)', border: '1px solid rgba(25, 118, 210, 0.2)' }}>
							<Typography variant="body2" color="primary" gutterBottom fontWeight="bold">
								⏱️ Bài học hiện tại
							</Typography>
							<Typography variant="caption" color="textSecondary" sx={{ display: 'block', mb: 1 }}>
								{currentLesson.title}
							</Typography>
							<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
								<Typography variant="body2" fontWeight="bold" color="primary">
									{formatTime(timeSpent)} / {formatTime(requiredTime)}
								</Typography>
								<Typography variant="caption" color="textSecondary">
									{Math.round(((timeSpent / requiredTime) * 100) || 0)}%
								</Typography>
							</Box>
							<LinearProgress
								variant="determinate"
								value={((timeSpent / requiredTime) * 100) || 0}
								sx={{
									height: 6,
									borderRadius: 3,
									mt: 1,
									bgcolor: 'rgba(25, 118, 210, 0.1)',
									'& .MuiLinearProgress-bar': {
										bgcolor: '#1976d2',
										borderRadius: 3
									}
								}}
							/>
						</Card>
					)}
				</Box>

				{/* Lessons List */}
				<List sx={{ px: 2 }}>
					{lessons.map((lesson) => {
						const isCompleted = completedLessons.has(lesson.globalIndex);
						const isCurrent = currentLesson?.globalIndex === lesson.globalIndex;
						const isLocked = lesson.isLocked && !completedLessons.has(lesson.globalIndex - 1);

						return (
							<ListItemButton
								key={lesson.id}
								onClick={() => handleLessonSelect(lesson)}
								disabled={isLocked}
								sx={{
									mb: 1,
									borderRadius: 2,
									border: isCurrent ? '2px solid #1a237e' : '1px solid rgba(26, 35, 126, 0.1)',
									bgcolor: isCurrent ? 'rgba(26, 35, 126, 0.05)' : 'white',
									'&:hover': {
										bgcolor: isLocked ? 'transparent' : 'rgba(26, 35, 126, 0.05)'
									},
									'&.Mui-disabled': {
										opacity: 0.5
									}
								}}
							>
								<ListItemIcon sx={{ minWidth: 48 }}>
									<LessonProgressIcon
										lesson={lesson}
										isCompleted={isCompleted}
										isLocked={isLocked}
										isCurrent={isCurrent}
									/>
								</ListItemIcon>
								<ListItemText
									primary={
										<Typography
											variant="body2"
											fontWeight={isCurrent ? 'bold' : 'medium'}
											sx={{ color: isLocked ? '#bdbdbd' : '#1a237e' }}
										>
											{lesson.title}
										</Typography>
									}
									secondary={
										<Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
											<Chip
												label={
													// Use content_type from database if available
													(lesson.content_type === 'text') ? 'Văn bản' :
														(lesson.content_type === 'video') ? 'Video' :
															(lesson.content_type === 'file') ? 'Tài liệu' :
																// Fallback to legacy type
																(lesson.type === 'video') ? 'Video' :
																	(lesson.type === 'quiz') ? 'Quiz' :
																		(lesson.type === 'assignment') ? 'Bài tập' : 'Dự án'
												}
												size="small"
												sx={{
													mr: 1,
													bgcolor:
														(lesson.content_type === 'text') ? 'rgba(25, 118, 210, 0.1)' :
															(lesson.content_type === 'video') ? 'rgba(26, 35, 126, 0.1)' :
																(lesson.content_type === 'file') ? 'rgba(255, 152, 0, 0.1)' :
																	'rgba(26, 35, 126, 0.1)',
													color:
														(lesson.content_type === 'text') ? '#1976d2' :
															(lesson.content_type === 'video') ? '#1a237e' :
																(lesson.content_type === 'file') ? '#ff9800' :
																	'#1a237e',
													fontSize: '0.7rem'
												}}
											/>
											<Typography variant="caption" color="textSecondary">
												{lesson.duration} phút
											</Typography>
										</Box>
									}
								/>
								{isCurrent && (
									<Box sx={{
										width: 8,
										height: 8,
										borderRadius: '50%',
										bgcolor: '#ffc107',
										ml: 1
									}} />
								)}
							</ListItemButton>
						);
					})}
				</List>
			</Drawer>

			{/* Main Content */}
			<Box
				component="main"
				sx={{
					flexGrow: 1,
					p: 3,
					mt: 2,
					ml: sidebarOpen ? 0 : '-360px',
					transition: 'margin-left 0.3s ease'
				}}
			>
				{currentLesson && (
					<>
						{/* Lesson Header */}
						{/* <Card sx={{
							mb: 4,
							p: 3,
							background: 'linear-gradient(135deg, #1a237e, #3949ab)',
							color: 'white'
						}}>
							<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
								<Box>
									<Typography variant="h4" fontWeight="bold" gutterBottom>
										{currentLesson.title}
									</Typography>
									<Typography variant="body1" sx={{ opacity: 0.9 }}>
										{currentLesson.description || 'Bài học trong khóa học'}
									</Typography>
								</Box>

								<Chip
									label={canProceed ? '✅ Hoàn thành' : '⏳ Đang học...'}
									sx={{
										bgcolor: canProceed ? '#4caf50' : '#ffc107',
										color: 'white',
										fontWeight: 'bold',
										fontSize: '0.9rem',
										px: 2
									}}
								/>
							</Box>

							{currentLesson.type === 'video' && (
								<LinearProgress
									variant="determinate"
									value={((currentLesson.duration * 60 - timeRemaining) / (currentLesson.duration * 60)) * 100}
									sx={{
										mt: 2,
										height: 8,
										borderRadius: 4,
										bgcolor: 'rgba(255,255,255,0.2)',
										'& .MuiLinearProgress-bar': {
											bgcolor: '#ffc107',
											borderRadius: 4
										}
									}}
								/>
							)}
						</Card> */}

						{/* Lesson Content */}
						<Card sx={{ mb: 4, borderRadius: 3, minHeight: "600px" }}>
							<CardContent sx={{ p: 4 }}>
								{renderLessonContent()}
							</CardContent>
						</Card>

						{/* Navigation Controls */}
						<Card sx={{ p: 3, borderRadius: 3 }}>
							<Stack direction="row" spacing={2} justifyContent="space-between" alignItems="center">
								<Button
									variant="outlined"
									startIcon={<PrevIcon />}
									onClick={handlePrevLesson}
									disabled={currentLesson.globalIndex === 0}
									sx={{
										borderRadius: "10px",
										borderColor: '#1a237e',
										color: '#1a237e',
										'&:hover': { borderColor: '#1a237e', bgcolor: 'rgba(26, 35, 126, 0.1)' }
									}}
								>
									Bài trước
								</Button>

								<Box sx={{ textAlign: 'center' }}>
									<Typography variant="body2" color="textSecondary">
										Bài {currentLesson.globalIndex + 1} / {lessons.length}
									</Typography>
								</Box>

								{canProceed ? (
									<Button
										variant="contained"
										endIcon={<NextIcon />}
										onClick={handleCompleteLesson}
										sx={{
											borderRadius: "10px",
											background: 'linear-gradient(45deg, #1a237e, #3949ab)',
											'&:hover': { transform: 'translateY(-2px)' }
										}}
									>
										{currentLesson.globalIndex === lessons.length - 1 ? 'Hoàn thành khóa học' : 'Bài tiếp theo'}
									</Button>
								) : (
									<Button
										variant="contained"
										disabled
										sx={{ bgcolor: '#bdbdbd', borderRadius: "10px" }}
									>
										Chờ hoàn thành...
									</Button>
								)}
							</Stack>
						</Card>
					</>
				)}
			</Box>
		</Box >
	);
};

export default CourseLearning;
