import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
	Box,
	Container,
	Typography,
	Button,
	Card,
	CardContent,
	Avatar,
	Rating,
	Chip,
	Divider,
	List,
	ListItem,
	ListItemIcon,
	ListItemText,
	Accordion,
	AccordionSummary,
	AccordionDetails,
	LinearProgress,
	Stack,
	Paper,
	IconButton,
	Breadcrumbs,
	Link
} from '@mui/material';
import CrisisAlertIcon from '@mui/icons-material/CrisisAlert';
import {
	PlayArrow as PlayIcon,
	ShoppingCart as CartIcon,
	FavoriteBorder as WishlistIcon,
	Share as ShareIcon,
	CheckCircle as CheckIcon,
	ExpandMore as ExpandMoreIcon,
	OndemandVideo as VideoIcon,
	Assignment as AssignmentIcon,
	Quiz as QuizIcon,
	Download as DownloadIcon,
	Language as LanguageIcon,
	AccessTime as TimeIcon,
	People as PeopleIcon,
	Star as StarIcon,
	NavigateNext as NavigateNextIcon,
	School as SchoolIcon,
	ThumbUp as ThumbUpIcon
} from '@mui/icons-material';
import Grid from '@mui/material/Grid2';
import Loading from '../../../Common/Loading';
import { elearningAPI } from '../../../../services';

const CourseDetail = () => {
	const { courseId } = useParams();
	const navigate = useNavigate();
	const [course, setCourse] = useState(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [isEnrolled, setIsEnrolled] = useState(false);
	const [enrollmentLoading, setEnrollmentLoading] = useState(true);
	const [expandedModule, setExpandedModule] = useState(false);

	useEffect(() => {
		loadCourseDetail();
		checkEnrollmentStatus();
	}, [courseId]);

	const loadCourseDetail = async () => {
		try {
			setLoading(true);
			const data = await elearningAPI.studentAPI.getCourse(courseId);
			setCourse(data);
		} catch (err) {
			setError('Không thể tải thông tin khóa học');
			console.error('Course detail error:', err);
		} finally {
			setLoading(false);
		}
	};

	// Check if user is enrolled in this course
	const checkEnrollmentStatus = async () => {
		try {
			setEnrollmentLoading(true);

			// Try to get user's enrolled courses
			let enrolledCourses = [];
			try {
				enrolledCourses = await elearningAPI.studentAPI.getEnrolledCourses();
			} catch (apiError) {
				// Fallback: check localStorage for enrollment
				const enrollmentKey = `course_${courseId}_enrollment`;
				const localEnrollment = localStorage.getItem(enrollmentKey);
				if (localEnrollment) {
					const enrollmentData = JSON.parse(localEnrollment);
					if (enrollmentData.user_id === user?.id) {
						setIsEnrolled(true);
						return;
					}
				}
			}

			// Check if current course is in enrolled courses
			const enrolled = enrolledCourses.some(enrolledCourse =>
				enrolledCourse.course?.id === parseInt(courseId) || enrolledCourse.id === parseInt(courseId)
			);

			setIsEnrolled(enrolled);
		} catch (err) {
			console.error('Error checking enrollment status:', err);
			// If error, assume not enrolled
			setIsEnrolled(false);
		} finally {
			setEnrollmentLoading(false);
		}
	};

	const handleEnroll = async () => {
		try {
			setLoading(true);

			const result = await elearningAPI.studentAPI.purchaseCourse(courseId, {
				payment_method: parseInt(course.price) === 0 ? 'free' : 'credit_card'
			});

			// Save enrollment to localStorage
			const enrollmentData = {
				course_id: courseId,
				user_id: user?.id,
				enrolled_at: new Date().toISOString(),
				status: 'active'
			};
			localStorage.setItem(`course_${courseId}_enrollment`, JSON.stringify(enrollmentData));

			// Update enrollment status
			setIsEnrolled(true);

			// Show success message and redirect to learning page
			const message = parseInt(course.price) === 0
				? 'Đăng ký khóa học miễn phí thành công! Đang chuyển đến trang học...'
				: 'Mua khóa học thành công! Đang chuyển đến trang học...';

			alert(message); // You can replace this with a proper notification

			// Navigate to learning page
			setTimeout(() => {
				navigate(`/e-learning/course/${courseId}/learn`);
			}, 1500);

		} catch (err) {
			const errorMessage = parseInt(course.price) === 0
				? 'Không thể đăng ký khóa học miễn phí. Vui lòng thử lại!'
				: 'Không thể mua khóa học. Vui lòng kiểm tra thông tin thanh toán!';

			setError(errorMessage);
			console.error('Enroll error:', err);
		} finally {
			setLoading(false);
		}
	};

	// Navigate to learning page for enrolled course
	const handleStartLearning = () => {
		navigate(`/e-learning/course/${courseId}/learn`);
	};

	const formatPrice = (price) => {
		if (price === 0) return 'Miễn phí';
		return new Intl.NumberFormat('vi-VN', {
			style: 'currency',
			currency: 'VND'
		}).format(price);
	};

	const formatDuration = (minutes) => {
		if (minutes < 60) return `${minutes} phút`;
		const hours = Math.floor(minutes / 60);
		const remainingMinutes = minutes % 60;
		if (remainingMinutes === 0) return `${hours} giờ`;
		return `${hours}h ${remainingMinutes}m`;
	};

	if (loading) {
		return (
			<Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
				<Loading />
			</Box>
		);
	}

	if (error || !course) {
		return (
			<Container maxWidth="lg" sx={{ py: 4 }}>
				<Typography variant="h6" color="error">
					{error || 'Không tìm thấy khóa học'}
				</Typography>
			</Container>
		);
	}

	return (
		<Box sx={{ bgcolor: '#f8fafc', minHeight: '100vh' }}>
			{/* BeE STEM Course Header Section */}
			<Box sx={{
				background: 'linear-gradient(135deg, #1a237e 0%, #3949ab 50%, #5c6bc0 100%)',
				color: 'white',
				py: 6,
				position: 'relative',
				overflow: 'hidden'
			}}>
				{/* Background Pattern */}
				<Box
					sx={{
						position: 'absolute',
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
						opacity: 0.3
					}}
				/>

				<Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
					{/* Breadcrumbs */}
					<Breadcrumbs
						separator={<NavigateNextIcon fontSize="small" />}
						sx={{ mb: 4, '& .MuiBreadcrumbs-separator': { color: 'rgba(255,255,255,0.7)' } }}
					>
						<Link
							color="inherit"
							href="/e-learning"
							sx={{
								textDecoration: 'none',
								color: 'rgba(255,255,255,0.8)',
								'&:hover': { color: '#ffc107' }
							}}
						>
							🏠 Trang chủ STEM
						</Link>
						<Link
							color="inherit"
							href="#"
							sx={{
								textDecoration: 'none',
								color: 'rgba(255,255,255,0.8)',
								'&:hover': { color: '#ffc107' }
							}}
						>
							📚 {course.subject?.name}
						</Link>
						<Typography sx={{ color: '#ffc107', fontWeight: 'medium' }}>
							{course.title}
						</Typography>
					</Breadcrumbs>

					<Grid container spacing={6}>
						{/* Left Content */}
						<Grid size={{ xs: 12, md: 8 }}>
							{/* STEM Subject Badge */}
							<Box sx={{ mb: 3 }}>
								<Chip
									label={`🔬 ${course.subject?.name || 'STEM'}`}
									sx={{
										bgcolor: 'rgba(255,193,7,0.2)',
										color: '#ffc107',
										fontWeight: 'bold',
										fontSize: '0.9rem',
										px: 2,
										py: 1
									}}
								/>
							</Box>

							<Typography variant="h2" fontWeight="bold" gutterBottom sx={{
								fontSize: { xs: '2rem', md: '2.5rem' },
								lineHeight: 1.2,
								mb: 3
							}}>
								{course.title}
							</Typography>

							<Typography variant="h6" sx={{
								mb: 4,
								opacity: 0.9,
								fontSize: '1.2rem',
								lineHeight: 1.6
							}}>
								{course.description}
							</Typography>

							{/* Enhanced Course Stats */}
							<Stack direction={{ xs: 'column', sm: 'row' }} spacing={4} alignItems="flex-start" sx={{ mb: 4 }}>
								<Box sx={{ display: 'flex', alignItems: 'center' }}>
									<Box sx={{
										bgcolor: 'rgba(255,193,7,0.2)',
										borderRadius: 2,
										p: 1.5,
										mr: 2,
										display: 'flex',
										alignItems: 'center'
									}}>
										<StarIcon sx={{ color: '#ffc107', fontSize: 20 }} />
									</Box>
									<Box>
										<Typography variant="h6" fontWeight="bold">
											{course.average_rating || 4.5}
										</Typography>
										<Rating value={course.average_rating || 4.5} readOnly size="small" />
										<Typography variant="body2" sx={{ opacity: 0.8 }}>
											({course.total_reviews || 0} đánh giá)
										</Typography>
									</Box>
								</Box>

								<Box sx={{ display: 'flex', alignItems: 'center' }}>
									<Box sx={{
										bgcolor: 'rgba(255,193,7,0.2)',
										borderRadius: 2,
										p: 1.5,
										mr: 2,
										display: 'flex',
										alignItems: 'center'
									}}>
										<PeopleIcon sx={{ color: '#ffc107', fontSize: 20 }} />
									</Box>
									<Box>
										<Typography variant="h6" fontWeight="bold">
											{course.enrolled_count || 0}
										</Typography>
										<Typography variant="body2" sx={{ opacity: 0.8 }}>
											học viên đã đăng ký
										</Typography>
									</Box>
								</Box>
							</Stack>

							{/* Enhanced Instructor Info */}
							<Box sx={{
								display: 'flex',
								alignItems: 'center',
								bgcolor: 'rgba(255,255,255,0.1)',
								borderRadius: 3,
								p: 2,
								mb: 4
							}}>
								<Avatar sx={{
									width: 48,
									height: 48,
									mr: 2,
									bgcolor: '#ffc107',
									color: '#1a237e',
									fontWeight: 'bold'
								}}>
									{course.instructor?.name?.charAt(0) || 'B'}
								</Avatar>
								<Box>
									<Typography variant="body2" sx={{ opacity: 0.8, mb: 0.5 }}>
										Chuyên gia STEM
									</Typography>
									<Typography variant="h6" fontWeight="bold">
										{course.instructor?.name || 'BeE Learning'}
									</Typography>
								</Box>
							</Box>

							{/* Enhanced Course Info */}
							<Grid container spacing={3} sx={{ mb: 4 }}>
								<Grid size={{ xs: 12, sm: 4 }}>
									<Box sx={{
										display: 'flex',
										alignItems: 'center',
										bgcolor: 'rgba(255,255,255,0.1)',
										borderRadius: 2,
										p: 2
									}}>
										<TimeIcon sx={{ mr: 2, fontSize: 24, color: '#ffc107' }} />
										<Box>
											<Typography variant="body2" sx={{ opacity: 0.8 }}>
												Thời lượng
											</Typography>
											<Typography variant="h6" fontWeight="bold">
												{course.duration || formatDuration(course.estimated_time || 120)}
											</Typography>
										</Box>
									</Box>
								</Grid>
								<Grid size={{ xs: 12, sm: 4 }}>
									<Box sx={{
										display: 'flex',
										alignItems: 'center',
										bgcolor: 'rgba(255,255,255,0.1)',
										borderRadius: 2,
										p: 2
									}}>
										<VideoIcon sx={{ mr: 2, fontSize: 24, color: '#ffc107' }} />
										<Box>
											<Typography variant="body2" sx={{ opacity: 0.8 }}>
												Bài học
											</Typography>
											<Typography variant="h6" fontWeight="bold">
												{course.total_lessons || 0} bài
											</Typography>
										</Box>
									</Box>
								</Grid>
								<Grid size={{ xs: 12, sm: 4 }}>
									<Box sx={{
										display: 'flex',
										alignItems: 'center',
										bgcolor: 'rgba(255,255,255,0.1)',
										borderRadius: 2,
										p: 2
									}}>
										<LanguageIcon sx={{ mr: 2, fontSize: 24, color: '#ffc107' }} />
										<Box>
											<Typography variant="body2" sx={{ opacity: 0.8 }}>
												Ngôn ngữ
											</Typography>
											<Typography variant="h6" fontWeight="bold">
												Tiếng Việt
											</Typography>
										</Box>
									</Box>
								</Grid>
							</Grid>

							{/* Enhanced What you'll learn */}
							<Box sx={{
								bgcolor: 'rgba(255,255,255,0.1)',
								borderRadius: 3,
								p: 2,
								mb: 4
							}}>
								<Box sx={{ display: "flex", alignItems: "center" }}>
									<CrisisAlertIcon sx={{ color: '#ffc107', fontSize: 30, mr: 2, mb: 1 }} />
									<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#ffc107' }}>
										Bạn sẽ học được gì
									</Typography>
								</Box>
								<Grid container spacing={2}>
									{(course.objectives || [
										'Hiểu các khái niệm cơ bản của STEM',
										'Áp dụng kiến thức vào các dự án thực tế',
										'Phát triển kỹ năng tư duy logic và sáng tạo',
										'Hoàn thành các thử thách STEM thú vị',
										'Xây dựng portfolio dự án ấn tượng',
										'Chuẩn bị cho sự nghiệp trong lĩnh vực STEM'
									]).map((objective, index) => (
										<Grid size={{ xs: 12, sm: 6 }} key={index}>
											<Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
												<CheckIcon sx={{ mr: 2, mt: 0.5, color: '#ffc107', fontSize: 20 }} />
												<Typography variant="body1" sx={{ lineHeight: 1.6 }}>
													{objective}
												</Typography>
											</Box>
										</Grid>
									))}
								</Grid>
							</Box>
						</Grid>

						{/* Right Sidebar - STEM Course Card */}
						<Grid size={{ xs: 12, md: 4 }}>
							<Card
								sx={{
									position: 'sticky',
									top: 100,
									bgcolor: 'white',
									color: '#1a237e',
									borderRadius: 4,
									boxShadow: '0 12px 40px rgba(26, 35, 126, 0.15)',
									border: '1px solid rgba(26, 35, 126, 0.1)',
									overflow: 'hidden'
								}}
							>
								{/* Enhanced Video Preview */}
								<Box
									sx={{
										height: 220,
										background: 'linear-gradient(135deg, #1a237e, #3949ab)',
										display: 'flex',
										alignItems: 'center',
										justifyContent: 'center',
										position: 'relative',
										overflow: 'hidden'
									}}
								>
									{course.thumbnail ? (
										<>
											<img
												src={course.thumbnail}
												alt={course.title}
												style={{ width: '100%', height: '100%', objectFit: 'cover' }}
											/>
											<Box sx={{
												position: 'absolute',
												top: 0,
												left: 0,
												right: 0,
												bottom: 0,
												background: 'linear-gradient(45deg, rgba(26, 35, 126, 0.3), rgba(57, 73, 171, 0.3))'
											}} />
										</>
									) : (
										<Box sx={{ textAlign: 'center', color: 'white' }}>
											<PlayIcon sx={{ fontSize: 60, mb: 2 }} />
											<Typography variant="h6">Preview khóa học</Typography>
										</Box>
									)}
									<IconButton
										sx={{
											position: 'absolute',
											bgcolor: 'rgba(255,193,7,0.9)',
											color: '#1a237e',
											width: 64,
											height: 64,
											'&:hover': {
												bgcolor: '#ffc107',
												transform: 'scale(1.1)'
											},
											transition: 'all 0.3s ease'
										}}
									>
										<PlayIcon sx={{ fontSize: 32 }} />
									</IconButton>

									{/* STEM Badge */}
									<Chip
										label="🔬 STEM"
										sx={{
											position: 'absolute',
											top: 16,
											right: 16,
											bgcolor: '#ffc107',
											color: '#1a237e',
											fontWeight: 'bold'
										}}
									/>
								</Box>

								<CardContent sx={{ p: 4 }}>
									{/* Enrollment Status */}
									{/* {!enrollmentLoading && isEnrolled && (
                    <Box sx={{ mb: 3, textAlign: 'center' }}>
                      <Chip
                        icon={<CheckIcon />}
                        label="✅ Đã đăng ký"
                        sx={{
                          bgcolor: '#4caf50',
                          color: 'white',
                          fontWeight: 'bold',
                          fontSize: '0.9rem',
                          px: 2,
                          py: 1
                        }}
                      />
                    </Box>
                  )} */}

									{/* Enhanced Price Section */}
									<Box sx={{ mb: 4, textAlign: 'center' }}>
										{course.discount_percentage > 0 && (
											<Chip
												label={`🔥 Giảm ${course.discount_percentage}%`}
												sx={{
													bgcolor: '#ff5722',
													color: 'white',
													fontWeight: 'bold',
													mb: 2
												}}
											/>
										)}
										<Typography variant="h3" fontWeight="bold" sx={{ color: '#1a237e', mb: 1 }}>
											{formatPrice(course.price || 0)}
										</Typography>
										{course.discount_percentage > 0 && (
											<Typography
												variant="h6"
												sx={{ textDecoration: 'line-through', color: 'text.secondary' }}
											>
												{formatPrice(course.original_price || course.price * 1.5)}
											</Typography>
										)}
									</Box>

									{/* Enhanced Action Buttons */}
									<Stack spacing={2} sx={{ mb: 4 }}>
										{!enrollmentLoading && (
											<Button
												fullWidth
												variant="contained"
												size="large"
												onClick={isEnrolled ? handleStartLearning : handleEnroll}
												startIcon={isEnrolled ? <PlayIcon /> : (course.price === 0 ? <PlayIcon /> : <CartIcon />)}
												sx={{
													background: isEnrolled
														? 'linear-gradient(45deg, #1976d2, #42a5f5)'
														: course.price === 0
															? 'linear-gradient(45deg, #2e7d32, #4caf50)'
															: 'linear-gradient(45deg, #1a237e, #3949ab)',
													py: 2,
													fontSize: '1.1rem',
													fontWeight: 'bold',
													borderRadius: 3,
													'&:hover': {
														transform: 'translateY(-2px)',
														boxShadow: isEnrolled
															? '0 8px 25px rgba(25, 118, 210, 0.3)'
															: '0 8px 25px rgba(26, 35, 126, 0.3)'
													},
													transition: 'all 0.3s ease'
												}}
											>
												{isEnrolled
													? 'Vào học ngay'
													: (parseInt(course.price) === 0 ? 'Học miễn phí ngay' : 'Đăng ký khóa học')
												}
											</Button>
										)}

										{enrollmentLoading && (
											<Button
												fullWidth
												variant="contained"
												size="large"
												disabled
												sx={{
													background: 'linear-gradient(45deg, #ccc, #ddd)',
													py: 2,
													fontSize: '1.1rem',
													fontWeight: 'bold',
													borderRadius: 3
												}}
											>
												🔄 Đang kiểm tra...
											</Button>
										)}

										<Stack direction="row" spacing={1}>
											<Button
												fullWidth
												variant="outlined"
												size="large"
												startIcon={<WishlistIcon />}
												sx={{
													py: 1.5,
													borderRadius: "10px",
													borderColor: '#1a237e',
													color: '#1a237e',
													'&:hover': {
														borderColor: '#1a237e',
														bgcolor: 'rgba(26, 35, 126, 0.1)'
													}
												}}
											>
												Thích
											</Button>
											<Button
												fullWidth
												variant="outlined"
												size="large"
												startIcon={<ShareIcon />}
												sx={{
													py: 1.5,
													borderRadius: "10px",
													borderColor: '#1a237e',
													color: '#1a237e',
													'&:hover': {
														borderColor: '#1a237e',
														bgcolor: 'rgba(26, 35, 126, 0.1)'
													}
												}}
											>
												Chia sẻ
											</Button>
										</Stack>
									</Stack>

									{/* Enhanced Course Includes */}
									<Box sx={{
										bgcolor: 'rgba(26, 35, 126, 0.05)',
										borderRadius: "10px",
										p: 3
									}}>
										<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
											📦 Khóa học bao gồm:
										</Typography>
										<List dense>
											<ListItem disablePadding sx={{ mb: 1 }}>
												<ListItemIcon sx={{ minWidth: 36 }}>
													<VideoIcon sx={{ color: '#1a237e', fontSize: 20 }} />
												</ListItemIcon>
												<ListItemText
													primary={`${course.total_lessons || 0} bài học video HD`}
													primaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
												/>
											</ListItem>
											<ListItem disablePadding sx={{ mb: 1 }}>
												<ListItemIcon sx={{ minWidth: 36 }}>
													<AssignmentIcon sx={{ color: '#1a237e', fontSize: 20 }} />
												</ListItemIcon>
												<ListItemText
													primary="Bài tập thực hành STEM"
													primaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
												/>
											</ListItem>
											<ListItem disablePadding sx={{ mb: 1 }}>
												<ListItemIcon sx={{ minWidth: 36 }}>
													<QuizIcon sx={{ color: '#1a237e', fontSize: 20 }} />
												</ListItemIcon>
												<ListItemText
													primary="Quiz và đánh giá kiến thức"
													primaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
												/>
											</ListItem>
											<ListItem disablePadding sx={{ mb: 1 }}>
												<ListItemIcon sx={{ minWidth: 36 }}>
													<DownloadIcon sx={{ color: '#1a237e', fontSize: 20 }} />
												</ListItemIcon>
												<ListItemText
													primary="Tài liệu và code mẫu"
													primaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
												/>
											</ListItem>
											<ListItem disablePadding>
												<ListItemIcon sx={{ minWidth: 36 }}>
													<CheckIcon sx={{ color: '#1a237e', fontSize: 20 }} />
												</ListItemIcon>
												<ListItemText
													primary="Chứng chỉ hoàn thành"
													primaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
												/>
											</ListItem>
										</List>
									</Box>
								</CardContent>
							</Card>
						</Grid>
					</Grid>
				</Container>
			</Box>

			{/* Enhanced Course Content Section */}
			<Box sx={{ bgcolor: '#f8fafc', py: 8 }}>
				<Container maxWidth="lg">
					<Grid container spacing={6}>
						{/* Left Content */}
						<Grid size={{ xs: 12, md: 8 }}>
							{/* Enhanced Course Curriculum */}
							<Box sx={{ mb: 8 }}>
								<Box sx={{ textAlign: 'center', mb: 6 }}>
									<Typography variant="h3" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
										📚 Nội dung khóa học STEM
									</Typography>
									<Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
										{course.total_lessons || 0} bài học • {formatDuration(course.estimated_time || 120)} tổng thời lượng
									</Typography>
									<Box sx={{
										width: 80,
										height: 4,
										bgcolor: '#ffc107',
										mx: 'auto',
										borderRadius: 2
									}} />
								</Box>

								{(course.syllabus || course.modules || [
									{
										id: 1,
										title: '🚀 Giới thiệu khóa học STEM',
										lessons: [
											{ id: 1, title: 'Chào mừng đến với thế giới STEM', duration: 5, type: 'video' },
											{ id: 2, title: 'Cài đặt môi trường học tập', duration: 10, type: 'video' },
											{ id: 3, title: 'Tổng quan về phương pháp STEM', duration: 8, type: 'video' }
										]
									},
									{
										id: 2,
										title: '🔬 Kiến thức cơ bản',
										lessons: [
											{ id: 4, title: 'Khái niệm cơ bản trong STEM', duration: 15, type: 'video' },
											{ id: 5, title: 'Bài tập thực hành đầu tiên', duration: 20, type: 'assignment' },
											{ id: 6, title: 'Quiz kiểm tra kiến thức', duration: 10, type: 'quiz' }
										]
									},
									{
										id: 3,
										title: '⚙️ Ứng dụng thực tế',
										lessons: [
											{ id: 7, title: 'Dự án STEM thực tế', duration: 30, type: 'project' },
											{ id: 8, title: 'Thảo luận và chia sẻ', duration: 15, type: 'discussion' }
										]
									}
								]).map((module, index) => (
									<Accordion
										key={module.id}
										expanded={expandedModule === index}
										onChange={() => setExpandedModule(expandedModule === index ? false : index)}
										sx={{
											mb: 2,
											borderRadius: 3,
											border: '1px solid rgba(26, 35, 126, 0.1)',
											boxShadow: '0 2px 8px rgba(26, 35, 126, 0.1)',
											'&:before': { display: 'none' },
											'&.Mui-expanded': {
												boxShadow: '0 4px 16px rgba(26, 35, 126, 0.15)'
											}
										}}
									>
										<AccordionSummary
											expandIcon={<ExpandMoreIcon sx={{ color: '#1a237e' }} />}
											sx={{
												bgcolor: expandedModule === index ? 'rgba(26, 35, 126, 0.05)' : 'white',
												borderRadius: '12px 12px 0 0',
												'&.Mui-expanded': {
													borderRadius: '12px 12px 0 0'
												}
											}}
										>
											<Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', mr: 2 }}>
												<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
													{module.title}
												</Typography>
												<Chip
													label={`${module.lessons?.length || 0} bài học`}
													size="small"
													sx={{
														bgcolor: '#ffc107',
														color: '#1a237e',
														fontWeight: 'bold'
													}}
												/>
											</Box>
										</AccordionSummary>
										<AccordionDetails sx={{ bgcolor: '#f8fafc' }}>
											<List>
												{(module.lessons || []).map((lesson) => {
													const getIcon = (type) => {
														switch (type) {
															case 'video': return <VideoIcon sx={{ color: '#1a237e' }} />;
															case 'assignment': return <AssignmentIcon sx={{ color: '#2e7d32' }} />;
															case 'quiz': return <QuizIcon sx={{ color: '#ed6c02' }} />;
															case 'project': return <CheckIcon sx={{ color: '#9c27b0' }} />;
															case 'discussion': return <PeopleIcon sx={{ color: '#d32f2f' }} />;
															default: return <VideoIcon sx={{ color: '#1a237e' }} />;
														}
													};

													const getTypeLabel = (type) => {
														switch (type) {
															case 'video': return 'Video';
															case 'assignment': return 'Bài tập';
															case 'quiz': return 'Quiz';
															case 'project': return 'Dự án';
															case 'discussion': return 'Thảo luận';
															default: return 'Video';
														}
													};

													return (
														<ListItem
															key={lesson.id}
															sx={{
																py: 1.5,
																borderRadius: 2,
																mb: 1,
																bgcolor: 'white',
																border: '1px solid rgba(26, 35, 126, 0.1)',
																'&:hover': {
																	bgcolor: 'rgba(26, 35, 126, 0.05)',
																	cursor: 'pointer'
																}
															}}
														>
															<ListItemIcon sx={{ minWidth: 40 }}>
																{getIcon(lesson.type)}
															</ListItemIcon>
															<ListItemText
																primary={
																	<Typography variant="body1" fontWeight="medium">
																		{lesson.title}
																	</Typography>
																}
																secondary={
																	<Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
																		<Chip
																			label={getTypeLabel(lesson.type)}
																			size="small"
																			sx={{
																				mr: 1,
																				bgcolor: 'rgba(26, 35, 126, 0.1)',
																				color: '#1a237e',
																				fontSize: '0.75rem'
																			}}
																		/>
																		<Typography variant="body2" color="textSecondary">
																			{lesson.duration} phút
																		</Typography>
																	</Box>
																}
															/>
															<PlayIcon sx={{ color: '#1a237e', opacity: 0.7 }} />
														</ListItem>
													);
												})}
											</List>
										</AccordionDetails>
									</Accordion>
								))}
							</Box>

							{/* Enhanced Requirements */}
							<Box sx={{ mb: 8 }}>
								<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
									📋 Yêu cầu tham gia
								</Typography>
								<Card sx={{
									p: 4,
									borderRadius: 3,
									border: '1px solid rgba(26, 35, 126, 0.1)',
									bgcolor: '#f8fafc'
								}}>
									<List>
										{(course.prerequisites || [
											'Không cần kiến thức chuyên môn trước',
											'Máy tính/laptop có kết nối internet ổn định',
											'Tinh thần học hỏi và khám phá STEM',
											'Sẵn sàng thực hành và làm dự án'
										]).map((requirement, index) => (
											<ListItem key={index} sx={{ py: 1 }}>
												<ListItemIcon sx={{ minWidth: 36 }}>
													<CheckIcon sx={{ color: '#2e7d32', fontSize: 20 }} />
												</ListItemIcon>
												<ListItemText
													primary={
														<Typography variant="body1" fontWeight="medium">
															{requirement}
														</Typography>
													}
												/>
											</ListItem>
										))}
									</List>
								</Card>
							</Box>

							{/* Enhanced Description */}
							<Box sx={{ mb: 8 }}>
								<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
									📖 Mô tả chi tiết
								</Typography>
								<Card sx={{
									p: 4,
									borderRadius: 3,
									border: '1px solid rgba(26, 35, 126, 0.1)',
									bgcolor: 'white'
								}}>
									<Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem' }}>
										{course.detailed_description || course.description ||
											`Khóa học STEM này được thiết kế đặc biệt để cung cấp cho bạn những kiến thức và kỹ năng cần thiết trong thế giới công nghệ hiện đại.

                      Với phương pháp giảng dạy tích hợp STEM (Science, Technology, Engineering, Mathematics), bạn sẽ không chỉ học lý thuyết mà còn được thực hành qua các dự án thực tế, giúp phát triển tư duy logic, khả năng giải quyết vấn đề và sáng tạo.

                      Khóa học kết hợp giữa video bài giảng chất lượng cao, bài tập thực hành phong phú và các dự án thực tế, đảm bảo bạn có thể áp dụng ngay kiến thức đã học vào công việc và cuộc sống.`}
									</Typography>
								</Card>
							</Box>

							{/* Enhanced Instructor */}
							<Box sx={{ mb: 8 }}>
								<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
									Chuyên gia STEM
								</Typography>
								<Card sx={{
									p: 4,
									borderRadius: 3,
									border: '1px solid rgba(26, 35, 126, 0.1)',
									background: 'linear-gradient(135deg, #f8fafc 0%, #ffffff 100%)',
									boxShadow: '0 4px 16px rgba(26, 35, 126, 0.1)'
								}}>
									<Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
										<Avatar sx={{
											width: 100,
											height: 100,
											mr: 4,
											bgcolor: '#1a237e',
											fontSize: '2rem',
											fontWeight: 'bold'
										}}>
											{course.instructor?.name?.charAt(0) || 'B'}
										</Avatar>
										<Box sx={{ flex: 1 }}>
											<Typography variant="h4" fontWeight="bold" sx={{ color: '#1a237e', mb: 1 }}>
												{course.instructor?.name || 'BeE Learning'}
											</Typography>
											<Typography variant="h6" sx={{ color: '#ffc107', mb: 2, fontWeight: 'medium' }}>
												{course.instructor?.title || '🔬 Chuyên gia STEM & Giáo dục'}
											</Typography>

											<Grid container spacing={3} sx={{ mb: 3 }}>
												<Grid item xs={6} sm={3}>
													<Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(26, 35, 126, 0.05)', borderRadius: 2 }}>
														<StarIcon sx={{ color: '#ffc107', fontSize: 24, mb: 1 }} />
														<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
															{course.instructor?.rating || 4.9}
														</Typography>
														<Typography variant="body2" color="textSecondary">
															Đánh giá
														</Typography>
													</Box>
												</Grid>
												<Grid item xs={6} sm={3}>
													<Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(26, 35, 126, 0.05)', borderRadius: 2 }}>
														<PeopleIcon sx={{ color: '#1a237e', fontSize: 24, mb: 1 }} />
														<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
															{course.instructor?.students_count || '5.2K'}
														</Typography>
														<Typography variant="body2" color="textSecondary">
															Học viên
														</Typography>
													</Box>
												</Grid>
												<Grid item xs={6} sm={3}>
													<Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(26, 35, 126, 0.05)', borderRadius: 2 }}>
														<SchoolIcon sx={{ color: '#2e7d32', fontSize: 24, mb: 1 }} />
														<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
															25
														</Typography>
														<Typography variant="body2" color="textSecondary">
															Khóa học
														</Typography>
													</Box>
												</Grid>
												<Grid item xs={6} sm={3}>
													<Box sx={{ textAlign: 'center', p: 2, bgcolor: 'rgba(26, 35, 126, 0.05)', borderRadius: 2 }}>
														<TimeIcon sx={{ color: '#ed6c02', fontSize: 24, mb: 1 }} />
														<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
															5+
														</Typography>
														<Typography variant="body2" color="textSecondary">
															Năm kinh nghiệm
														</Typography>
													</Box>
												</Grid>
											</Grid>
										</Box>
									</Box>

									<Typography variant="body1" sx={{ lineHeight: 1.8, fontSize: '1.1rem' }}>
										{course.instructor?.bio ||
											`Chuyên gia hàng đầu trong lĩnh vực STEM với hơn 5 năm kinh nghiệm giảng dạy và nghiên cứu. Tốt nghiệp xuất sắc từ các trường đại học danh tiếng, có chuyên môn sâu về Khoa học, Công nghệ, Kỹ thuật và Toán học.

                      Đã đào tạo thành công hàng nghìn học viên, giúp họ phát triển tư duy STEM và đạt được thành công trong sự nghiệp. Luôn cập nhật những xu hướng mới nhất trong giáo dục và công nghệ để mang đến trải nghiệm học tập tốt nhất cho học viên.`}
									</Typography>
								</Card>
							</Box>

							{/* Enhanced Reviews */}
							<Box sx={{ mb: 8 }}>
								<Typography variant="h4" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
									⭐ Đánh giá từ học viên
								</Typography>
								<Card sx={{
									p: 4,
									mb: 4,
									borderRadius: 3,
									border: '1px solid rgba(26, 35, 126, 0.1)',
									background: 'linear-gradient(135deg, #f8fafc 0%, #ffffff 100%)'
								}}>
									<Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
										<Box sx={{
											textAlign: 'center',
											p: 3,
											bgcolor: 'rgba(26, 35, 126, 0.05)',
											borderRadius: 3,
											mr: 4
										}}>
											<Typography variant="h1" fontWeight="bold" sx={{ color: '#1a237e', mb: 1 }}>
												{course.average_rating || 4.9}
											</Typography>
											<Rating value={course.average_rating || 4.9} readOnly size="large" sx={{ mb: 1 }} />
											<Typography variant="body1" color="textSecondary">
												{course.total_reviews || 156} đánh giá
											</Typography>
										</Box>
										<Box sx={{ flex: 1 }}>
											<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e', mb: 2 }}>
												Phân bố đánh giá
											</Typography>
											{[5, 4, 3, 2, 1].map((star) => (
												<Box key={star} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
													<Typography variant="body2" sx={{ minWidth: 20 }}>
														{star}
													</Typography>
													<StarIcon sx={{ color: '#ffc107', fontSize: 16, mx: 1 }} />
													<Box sx={{
														flex: 1,
														height: 8,
														bgcolor: '#e0e0e0',
														borderRadius: 4,
														mr: 2
													}}>
														<Box sx={{
															width: `${star === 5 ? 70 : star === 4 ? 20 : star === 3 ? 8 : star === 2 ? 2 : 0}%`,
															height: '100%',
															bgcolor: '#ffc107',
															borderRadius: 4
														}} />
													</Box>
													<Typography variant="body2" color="textSecondary">
														{star === 5 ? '70%' : star === 4 ? '20%' : star === 3 ? '8%' : star === 2 ? '2%' : '0%'}
													</Typography>
												</Box>
											))}
										</Box>
									</Box>
								</Card>

								{/* Enhanced Sample Reviews */}
								{[
									{
										id: 1,
										user: 'Nguyễn Văn A',
										title: 'Sinh viên Công nghệ thông tin',
										rating: 5,
										comment: 'Khóa học STEM này thực sự tuyệt vời! Cách giảng dạy rất dễ hiểu, kết hợp lý thuyết và thực hành một cách hoàn hảo. Tôi đã học được rất nhiều kiến thức bổ ích cho ngành học của mình.',
										date: '2 tuần trước',
										helpful: 24
									},
									{
										id: 2,
										user: 'Trần Thị B',
										title: 'Kỹ sư phần mềm',
										rating: 5,
										comment: 'Nội dung khóa học rất phong phú và cập nhật. Bài tập thực hành giúp tôi áp dụng ngay kiến thức vào công việc. Giảng viên nhiệt tình và hỗ trợ học viên rất tốt.',
										date: '1 tháng trước',
										helpful: 18
									},
									{
										id: 3,
										user: 'Lê Minh C',
										title: 'Học sinh lớp 12',
										rating: 4,
										comment: 'Khóa học giúp tôi hiểu rõ hơn về STEM và định hướng nghề nghiệp tương lai. Phương pháp giảng dạy hiện đại, dễ tiếp thu. Chỉ mong có thêm nhiều dự án thực tế hơn.',
										date: '3 tuần trước',
										helpful: 12
									}
								].map((review) => (
									<Card key={review.id} sx={{
										mb: 3,
										p: 4,
										borderRadius: 3,
										border: '1px solid rgba(26, 35, 126, 0.1)',
										'&:hover': {
											boxShadow: '0 4px 16px rgba(26, 35, 126, 0.1)'
										},
										transition: 'all 0.3s ease'
									}}>
										<Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
											<Avatar sx={{
												mr: 3,
												width: 48,
												height: 48,
												bgcolor: '#1a237e',
												fontWeight: 'bold'
											}}>
												{review.user.charAt(0)}
											</Avatar>
											<Box sx={{ flex: 1 }}>
												<Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e' }}>
													{review.user}
												</Typography>
												<Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
													{review.title}
												</Typography>
												<Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
													<Rating value={review.rating} readOnly size="small" />
													<Typography variant="body2" color="textSecondary" sx={{ ml: 2 }}>
														{review.date}
													</Typography>
												</Box>
											</Box>
										</Box>
										<Typography variant="body1" sx={{ lineHeight: 1.6, mb: 3 }}>
											{review.comment}
										</Typography>
										<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
											<Button
												size="small"
												startIcon={<ThumbUpIcon />}
												sx={{
													color: '#1a237e',
													'&:hover': { bgcolor: 'rgba(26, 35, 126, 0.1)' }
												}}
											>
												Hữu ích ({review.helpful})
											</Button>
											<Button
												size="small"
												sx={{
													color: 'text.secondary',
													'&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
												}}
											>
												Báo cáo
											</Button>
										</Box>
									</Card>
								))}
							</Box>
						</Grid>

						{/* Right Sidebar - Empty for now */}
						<Grid size={{ xs: 12, md: 4 }}>
							{/* Additional course info or related courses can go here */}
						</Grid>
					</Grid>
				</Container>
			</Box>
		</Box>
	);
};

export default CourseDetail;
