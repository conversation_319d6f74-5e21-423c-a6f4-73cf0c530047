# BeE STEM Solutions

A comprehensive e-commerce and point-of-sale (POS) system for coffee shops, built with Django REST Framework and React.

![BeE STEM Solutions](https://via.placeholder.com/800x400?text=Bee+Coffee+Shop)

## 📋 Overview

BeE STEM Solutions is a full-stack web application that provides both an online shopping platform and an in-store point-of-sale system for STEM. The application includes product management, inventory tracking, shopping cart functionality, order processing, blog management, and administrative dashboard.

## 🚀 Features

### Customer-Facing Features
- **Product Browsing**: Browse products by category with detailed product information
- **Shopping Cart**: Add products to cart, apply coupons, and checkout
- **User Authentication**: Register, login, and manage profile
- **Google Authentication**: Sign in with Google account
- **Blog**: Read blog posts about coffee and related topics
- **Contact Form**: Get in touch with the shop via contact form

### Admin/Staff Features
- **Point of Sale (POS)**: Process in-store sales with barcode scanning
- **Inventory Management**: Track product inventory and stock levels
- **Order Management**: Process and track customer orders
- **Membership System**: Manage customer membership and loyalty points
- **Promotion Management**: Create and manage promotions and coupons
- **Blog Management**: Create and publish blog posts
- **Dashboard**: View sales analytics and business metrics

### Technical Features
- **RESTful API**: Well-structured API endpoints for all functionality
- **JWT Authentication**: Secure authentication with JWT tokens
- **Google OAuth**: Integration with Google for authentication
- **QR Code Payment**: Generate QR codes for payment
- **Responsive Design**: Works on desktop and mobile devices
- **Comprehensive Testing**: Unit and integration tests for backend

## 🛠️ Technology Stack

### Backend
- **Django**: Web framework
- **Django REST Framework**: API development
- **PostgreSQL**: Database (Production)
- **SQLite**: Database (Development)
- **JWT**: Authentication
- **Google OAuth**: Social authentication
- **reCAPTCHA**: Form protection

### Frontend
- **React**: UI library
- **Vite**: Build tool
- **Material-UI**: Component library
- **Axios**: HTTP client
- **Framer Motion**: Animations
- **React Router**: Routing

## 🏗️ Project Structure

The project consists of two main parts:

### Backend (Django)
- **auth_api**: User authentication and profile management
- **product_api**: Product and inventory management
- **shopping_api**: Cart, checkout, and order processing
- **admin_api**: Admin dashboard and analytics
- **blog_api**: Blog management

### Frontend (React)
- **Authentication**: Login, register, and profile components
- **Product**: Product listing and detail components
- **Shopping**: Cart and checkout components
- **Admin**: Admin dashboard and management components
- **POS**: Point of sale interface
- **Blog**: Blog components

## 🔧 Setup and Installation

### Prerequisites
- Python 3.9+
- Node.js 18+
- npm or yarn
- PostgreSQL (for production)

### Backend Setup
1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/bee-stem-solutions.git
   cd bee-stem-solutions
   ```

2. Create and activate a virtual environment
   ```bash
   cd backend
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. Install dependencies
   ```bash
   pip install -r requirements.txt
   ```

4. Create a `config.json` file in the `config` directory with your settings
   ```json
   {
     "env": "dev",
     "SECRET_KEY": "your-secret-key",
     "DEBUG": true,
     "ALLOWED_HOSTS": ["localhost", "127.0.0.1"],
     "CSRF_TRUSTED_ORIGINS": ["http://localhost:5173"],
     "CORS_ALLOWED_ORIGINS": ["http://localhost:5173"],
     "CORS_ALLOWED_ORIGIN_REGEXES": [],
     "STATIC_ROOT": "",
     "ADMIN_ROOT": "",
     "DATABASES": {},
     "email": {
       "EMAIL_BACKEND": "django.core.mail.backends.console.EmailBackend",
       "EMAIL_HOST": "",
       "EMAIL_USE_TLS": true,
       "EMAIL_USE_SSL": false,
       "EMAIL_PORT": 587,
       "EMAIL_HOST_USER": "",
       "EMAIL_HOST_PASSWORD": ""
     },
     "GOOGLE_AUTH": {
       "CLIENT_ID": "your-google-client-id",
       "SECRET_ID": "your-google-client-secret"
     },
     "CAPTCHA_SECRET_KEY": "your-recaptcha-secret-key",
     "REMOTE_API_KEY": ""
   }
   ```

5. Run migrations
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

6. Create a superuser
   ```bash
   python manage.py createsuperuser
   ```

7. Run the development server
   ```bash
   python manage.py runserver
   ```

### Frontend Setup
1. Navigate to the frontend directory
   ```bash
   cd ../frontend
   ```

2. Install dependencies
   ```bash
   npm install
   # or
   yarn install
   ```

3. Create a `.env` file with your settings
   ```
   VITE_API_URL=http://localhost:8000
   ```

4. Run the development server
   ```bash
   npm run dev
   # or
   yarn dev
   ```

## 🧪 Testing

The project includes comprehensive tests for the backend. To run the tests:

```bash
cd backend
python manage.py test
```

For more detailed information about testing, see [README_TESTS.md](backend/README_TESTS.md).

## 🚢 Deployment

The project is configured for deployment with CircleCI and Ansible. The deployment process includes:

1. Pulling the latest code from the repository
2. Installing dependencies
3. Running migrations
4. Restarting the Gunicorn service

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👥 Contributors

- [Your Name](https://github.com/yourusername)

## 🙏 Acknowledgements

- [Django](https://www.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [React](https://reactjs.org/)
- [Material-UI](https://mui.com/)
- [Vite](https://vitejs.dev/)
