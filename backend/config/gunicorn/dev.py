"""Gunicorn *development* config file"""

# Django WSGI application path in pattern MODULE_NAME:VARIABLE_NAME
wsgi_app = "bee_stem.wsgi:application"
# The granularity of Error log outputs
loglevel = "debug"
# The number of worker processes for handling requests
workers = 2
# The socket to bind
bind = "0.0.0.0:8000"
# Restart workers when code changes (development only!)
reload = True
# Write access and error info to /var/log
accesslog = "/root/workspace/log/gunicorn/access_beeshop.log"
errorlog = "/root/workspace/log/gunicorn/error_beeshop.log"
# Redirect stdout/stderr to log file
capture_output = True
# PID file so you can easily fetch process ID
pidfile = "/root/workspace/log/gunicorn/dev_beeshop.pid"
# Daemonize the Gunicorn process (detach & enter background)
daemon = True
