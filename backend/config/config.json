{"env": "dev", "SECRET_KEY": "django-insecure-&76e(g^0*kvi59vwgg0c6xkv*(se7d70m@^qb@7ew1)s*!&f-=", "DEBUG": true, "ALLOWED_HOSTS": ["*"], "CORS_ALLOWED_ORIGINS": ["http://localhost:4000", "http://************:4000"], "CSRF_TRUSTED_ORIGINS": ["http://localhost:4000", "http://************:4000", "https://beeblock.vn", "http://beeblock.vn"], "CORS_ALLOWED_ORIGIN_REGEXES": ["^http://localhost:4000$", "^http://************:4000$"], "DATABASES": {"ENGINE": "django.db.backends.sqlite3", "NAME": "BASE_DIR / 'db.sqlite3"}, "STATIC_ROOT": "", "ADMIN_ROOT": "", "email": {"EMAIL_BACKEND": "django.core.mail.backends.smtp.EmailBackend", "EMAIL_HOST": "smtp.gmail.com", "EMAIL_USE_TLS": true, "EMAIL_USE_SSL": false, "EMAIL_PORT": 587, "EMAIL_HOST_USER": "<EMAIL>", "EMAIL_HOST_PASSWORD": "dyfo qrgx hqrd dhjr"}, "CAPTCHA_SECRET_KEY": "6Ld14NcqAAAAAP4YD28JCA7lStI4TUQElKl9b0Kf", "GOOGLE_AUTH": {"CLIENT_ID": "743536928805-kircflddgpu653pdiu4eedte086vo7rq.apps.googleusercontent.com", "SECRET_ID": "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"}, "REMOTE_API_KEY": "ALKSJDLKA-kjlsKAJLKJQ-JN123OQW"}