from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    Subject, Grade, Course, Enrollment, Lesson, LessonProgress,
    Quiz, Question, QuestionBank, QuizAttempt, QuizAnswer, Assignment, AssignmentSubmission,
    CourseReview, Payment, Certificate
)


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user information"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)

    class Meta:
        model = User
        fields = ['id', 'username', 'email',
                  'first_name', 'last_name', 'full_name']


class SubjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subject
        fields = '__all__'


class GradeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Grade
        fields = '__all__'


class CourseListSerializer(serializers.ModelSerializer):
    """Serializer for course list view"""
    teacher = UserBasicSerializer(read_only=True)
    subject = SubjectSerializer(read_only=True)
    grade = GradeSerializer(read_only=True)
    enrolled_count = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()
    is_free = serializers.ReadOnlyField()
    total_lessons = serializers.ReadOnlyField()
    total_quizzes = serializers.ReadOnlyField()
    total_assignments = serializers.ReadOnlyField()
    average_rating = serializers.ReadOnlyField()
    total_reviews = serializers.ReadOnlyField()

    class Meta:
        model = Course
        fields = [
            'id', 'title', 'description', 'thumbnail', 'subject', 'grade',
            'difficulty', 'teacher', 'price', 'original_price', 'duration',
            'estimated_time', 'language', 'has_certificate', 'status', 'is_published',
            'max_students', 'enrolled_count', 'discount_percentage', 'is_free',
            'total_lessons', 'total_quizzes', 'total_assignments', 'average_rating',
            'total_reviews', 'created_at', 'updated_at', 'objectives',
            'prerequisites', 'features', 'syllabus'
        ]


class CourseDetailSerializer(serializers.ModelSerializer):
    """Serializer for course detail view"""
    teacher = UserBasicSerializer(read_only=True)
    subject = SubjectSerializer(read_only=True)
    grade = GradeSerializer(read_only=True)
    enrolled_count = serializers.ReadOnlyField()
    discount_percentage = serializers.ReadOnlyField()
    is_free = serializers.ReadOnlyField()
    total_lessons = serializers.ReadOnlyField()
    total_quizzes = serializers.ReadOnlyField()
    total_assignments = serializers.ReadOnlyField()
    average_rating = serializers.ReadOnlyField()
    total_reviews = serializers.ReadOnlyField()

    class Meta:
        model = Course
        fields = '__all__'


class CourseCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating courses"""
    teacher = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = Course
        fields = [
            'title', 'description', 'thumbnail', 'subject', 'grade', 'difficulty',
            'teacher', 'price', 'original_price', 'duration', 'estimated_time',
            'language', 'has_certificate', 'objectives', 'prerequisites', 'features',
            'syllabus', 'max_students'
        ]

    # def validate(self, data):
    #     # Validate pricing
    #     if data.get('original_price', 0) < data.get('price', 0):
    #         raise serializers.ValidationError(
    #             "Original price cannot be less than current price")
    #     return data

    def create(self, validated_data):
        # Handle JSON fields that come as strings from FormData
        if 'objectives' in validated_data and isinstance(validated_data['objectives'], str):
            try:
                import json
                validated_data['objectives'] = json.loads(
                    validated_data['objectives'])
            except (json.JSONDecodeError, TypeError):
                validated_data['objectives'] = []

        if 'prerequisites' in validated_data and isinstance(validated_data['prerequisites'], str):
            try:
                import json
                validated_data['prerequisites'] = json.loads(
                    validated_data['prerequisites'])
            except (json.JSONDecodeError, TypeError):
                validated_data['prerequisites'] = []

        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Handle JSON fields that come as strings from FormData
        if 'objectives' in validated_data and isinstance(validated_data['objectives'], str):
            try:
                import json
                validated_data['objectives'] = json.loads(
                    validated_data['objectives'])
            except (json.JSONDecodeError, TypeError):
                validated_data['objectives'] = []

        if 'prerequisites' in validated_data and isinstance(validated_data['prerequisites'], str):
            try:
                import json
                validated_data['prerequisites'] = json.loads(
                    validated_data['prerequisites'])
            except (json.JSONDecodeError, TypeError):
                validated_data['prerequisites'] = []

        return super().update(instance, validated_data)


class LessonSerializer(serializers.ModelSerializer):
    class Meta:
        model = Lesson
        fields = '__all__'


class LessonCreateUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Lesson
        fields = [
            'title', 'description', 'content_type', 'content', 'video_url',
            'file', 'duration', 'order', 'is_preview', 'is_required'
        ]


class LessonProgressSerializer(serializers.ModelSerializer):
    lesson = LessonSerializer(read_only=True)

    class Meta:
        model = LessonProgress
        fields = '__all__'


class EnrollmentSerializer(serializers.ModelSerializer):
    student = UserBasicSerializer(read_only=True)
    course = CourseListSerializer(read_only=True)
    actual_progress_percentage = serializers.SerializerMethodField()
    actual_completed_lessons = serializers.SerializerMethodField()
    total_lessons = serializers.SerializerMethodField()

    class Meta:
        model = Enrollment
        fields = '__all__'

    def get_actual_progress_percentage(self, obj):
        """Get actual progress percentage based on completed lessons"""
        progress_percentage, _ = obj.calculate_progress()
        return round(progress_percentage, 1)

    def get_actual_completed_lessons(self, obj):
        """Get actual number of completed lessons"""
        _, completed_lessons = obj.calculate_progress()
        return completed_lessons

    def get_total_lessons(self, obj):
        """Get total number of required lessons in the course"""
        return obj.course.lessons.filter(is_required=True).count()


class EnrollmentCreateSerializer(serializers.ModelSerializer):
    student = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = Enrollment
        fields = ['student', 'course', 'paid_amount', 'payment_method']


class QuestionBankSerializer(serializers.ModelSerializer):
    subject_name = serializers.CharField(source='subject.name', read_only=True)
    grade_name = serializers.CharField(source='grade.name', read_only=True)
    teacher_name = serializers.CharField(
        source='teacher.get_full_name', read_only=True)

    class Meta:
        model = QuestionBank
        fields = '__all__'
        read_only_fields = ('teacher', 'id')


class QuestionBankCreateUpdateSerializer(serializers.ModelSerializer):
    # Allow subject and grade to be optional
    subject = serializers.PrimaryKeyRelatedField(
        queryset=Subject.objects.all(),
        required=False,
        allow_null=True
    )
    grade = serializers.PrimaryKeyRelatedField(
        queryset=Grade.objects.all(),
        required=False,
        allow_null=True
    )

    class Meta:
        model = QuestionBank
        fields = [
            'subject', 'grade', 'question_type', 'question_text',
            'explanation', 'points', 'difficulty', 'tags',
            'options', 'correct_answers'
        ]

    def validate(self, data):
        # Validate multiple choice questions
        if data.get('question_type') == 'multiple_choice':
            options = data.get('options', [])
            correct_answers = data.get('correct_answers', [])

            if len(options) < 2:
                raise serializers.ValidationError(
                    "Câu hỏi trắc nghiệm phải có ít nhất 2 lựa chọn"
                )

            if not correct_answers:
                raise serializers.ValidationError(
                    "Phải có ít nhất một đáp án đúng"
                )

            # Check if correct answer indices are valid
            for answer_idx in correct_answers:
                if not isinstance(answer_idx, int) or answer_idx < 0 or answer_idx >= len(options):
                    raise serializers.ValidationError(
                        f"Chỉ số đáp án đúng {answer_idx} không hợp lệ. Phải là số nguyên từ 0 đến {len(options)-1}"
                    )

        # Validate true/false questions
        elif data.get('question_type') == 'true_false':
            correct_answers = data.get('correct_answers', [])
            if not correct_answers or len(correct_answers) != 1:
                raise serializers.ValidationError(
                    "Câu hỏi Đúng/Sai phải có đúng một đáp án đúng (0 hoặc 1)"
                )
            if correct_answers[0] not in [0, 1]:
                raise serializers.ValidationError(
                    "Đáp án đúng cho câu hỏi Đúng/Sai phải là 0 (Đúng) hoặc 1 (Sai)"
                )

        return data


class QuestionSerializer(serializers.ModelSerializer):
    question_bank_data = QuestionBankSerializer(
        source='question_bank', read_only=True)

    class Meta:
        model = Question
        fields = '__all__'


class QuestionCreateUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Question
        fields = [
            'question_type', 'question_text', 'explanation', 'points',
            'order', 'options', 'correct_answers'
        ]


class QuizSerializer(serializers.ModelSerializer):
    questions = QuestionSerializer(many=True, read_only=True)
    total_questions = serializers.ReadOnlyField()

    class Meta:
        model = Quiz
        fields = '__all__'


class QuizCreateUpdateSerializer(serializers.ModelSerializer):
    questions = QuestionCreateUpdateSerializer(many=True, required=False)
    question_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="List of QuestionBank IDs to include in this quiz"
    )

    class Meta:
        model = Quiz
        fields = [
            'title', 'description', 'duration', 'max_attempts',
            'passing_score', 'shuffle_questions', 'show_results_immediately',
            'allow_review', 'is_required', 'order', 'questions', 'question_ids'
        ]

    def create(self, validated_data):
        questions_data = validated_data.pop('questions', [])
        question_ids = validated_data.pop('question_ids', [])
        quiz = Quiz.objects.create(**validated_data)

        # Create questions from questions_data (full question objects)
        for question_data in questions_data:
            Question.objects.create(quiz=quiz, **question_data)

        # Create questions from question_ids (references to QuestionBank)
        if question_ids:
            from .models import QuestionBank
            for idx, question_bank_id in enumerate(question_ids):
                try:
                    question_bank = QuestionBank.objects.get(
                        id=question_bank_id)
                    Question.objects.create(
                        quiz=quiz,
                        question_bank=question_bank,
                        order=idx + 1
                    )
                except QuestionBank.DoesNotExist:
                    continue

        return quiz

    def update(self, instance, validated_data):
        questions_data = validated_data.pop('questions', [])
        question_ids = validated_data.pop('question_ids', [])

        # Update quiz fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update questions if provided
        if questions_data or question_ids:
            # Delete existing questions
            instance.questions.all().delete()

            # Create new questions from questions_data
            for question_data in questions_data:
                Question.objects.create(quiz=instance, **question_data)

            # Create new questions from question_ids
            if question_ids:
                from .models import QuestionBank
                for idx, question_bank_id in enumerate(question_ids):
                    try:
                        question_bank = QuestionBank.objects.get(
                            id=question_bank_id)
                        Question.objects.create(
                            quiz=instance,
                            question_bank=question_bank,
                            order=idx + 1
                        )
                    except QuestionBank.DoesNotExist:
                        continue

        return instance


class QuizAttemptSerializer(serializers.ModelSerializer):
    student = UserBasicSerializer(read_only=True)
    quiz = QuizSerializer(read_only=True)
    is_passed = serializers.ReadOnlyField()
    time_remaining = serializers.ReadOnlyField()

    class Meta:
        model = QuizAttempt
        fields = '__all__'


class QuizAnswerSerializer(serializers.ModelSerializer):
    question = QuestionSerializer(read_only=True)

    class Meta:
        model = QuizAnswer
        fields = '__all__'


class AssignmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assignment
        fields = '__all__'


class AssignmentCreateUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assignment
        fields = [
            'title', 'description', 'assignment_type', 'required_features',
            'grading_criteria', 'starter_project_url', 'max_score',
            'due_date', 'allow_late_submission', 'max_attempts', 'order'
        ]

    def create(self, validated_data):
        return Assignment.objects.create(**validated_data)

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class AssignmentCreateUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assignment
        fields = [
            'title', 'description', 'assignment_type', 'required_features',
            'grading_criteria', 'starter_project_url', 'reference_files',
            'max_score', 'due_date', 'allow_late_submission', 'max_attempts', 'order'
        ]


class AssignmentSubmissionSerializer(serializers.ModelSerializer):
    student = UserBasicSerializer(read_only=True)
    assignment = AssignmentSerializer(read_only=True)
    graded_by = UserBasicSerializer(read_only=True)
    is_late = serializers.ReadOnlyField()

    class Meta:
        model = AssignmentSubmission
        fields = '__all__'


class AssignmentSubmissionCreateUpdateSerializer(serializers.ModelSerializer):
    student = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = AssignmentSubmission
        fields = [
            'student', 'project_url', 'description', 'submission_files'
        ]


class CourseReviewSerializer(serializers.ModelSerializer):
    student = UserBasicSerializer(read_only=True)

    class Meta:
        model = CourseReview
        fields = '__all__'


class CourseReviewCreateUpdateSerializer(serializers.ModelSerializer):
    student = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = CourseReview
        fields = ['student', 'rating', 'title', 'comment']


class PaymentSerializer(serializers.ModelSerializer):
    student = UserBasicSerializer(read_only=True)
    course = CourseListSerializer(read_only=True)
    discount_percentage = serializers.ReadOnlyField()

    class Meta:
        model = Payment
        fields = '__all__'


class PaymentCreateSerializer(serializers.ModelSerializer):
    student = serializers.HiddenField(default=serializers.CurrentUserDefault())

    class Meta:
        model = Payment
        fields = [
            'student', 'course', 'amount', 'original_amount', 'discount_amount',
            'payment_method', 'external_transaction_id', 'payment_gateway'
        ]


class CertificateSerializer(serializers.ModelSerializer):
    student = UserBasicSerializer(read_only=True)
    course = CourseListSerializer(read_only=True)

    class Meta:
        model = Certificate
        fields = '__all__'


# Dashboard and Analytics Serializers
class TeacherDashboardSerializer(serializers.Serializer):
    """Teacher dashboard statistics"""
    total_courses = serializers.IntegerField()
    published_courses = serializers.IntegerField()
    draft_courses = serializers.IntegerField()
    total_students = serializers.IntegerField()
    total_enrollments = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=15, decimal_places=0)
    avg_course_rating = serializers.FloatField()
    recent_enrollments = EnrollmentSerializer(many=True)
    popular_courses = CourseListSerializer(many=True)


class StudentDashboardSerializer(serializers.Serializer):
    """Student dashboard data"""
    enrolled_courses = serializers.IntegerField()
    completed_courses = serializers.IntegerField()
    in_progress_courses = serializers.IntegerField()
    total_certificates = serializers.IntegerField()
    total_time_spent = serializers.IntegerField()  # minutes
    avg_score = serializers.FloatField()
    recent_activities = serializers.ListField()
    recommended_courses = CourseListSerializer(many=True)


class CourseAnalyticsSerializer(serializers.Serializer):
    """Course analytics data"""
    total_enrollments = serializers.IntegerField()
    active_students = serializers.IntegerField()
    completion_rate = serializers.FloatField()
    avg_progress = serializers.FloatField()
    avg_rating = serializers.FloatField()
    total_revenue = serializers.DecimalField(max_digits=15, decimal_places=0)
    lesson_completion_stats = serializers.DictField()
    quiz_performance_stats = serializers.DictField()
    student_engagement_stats = serializers.DictField()


class CourseContentSerializer(serializers.Serializer):
    """Combined course content (lessons, quizzes, assignments)"""
    lessons = LessonSerializer(many=True)
    quizzes = QuizSerializer(many=True)
    assignments = AssignmentSerializer(many=True)


class StudentProgressSerializer(serializers.Serializer):
    """Student progress in a course"""
    enrollment = EnrollmentSerializer()
    lesson_progress = LessonProgressSerializer(many=True)
    quiz_attempts = QuizAttemptSerializer(many=True)
    assignment_submissions = AssignmentSubmissionSerializer(many=True)
    overall_progress = serializers.FloatField()
    time_spent = serializers.IntegerField()
    next_lesson = LessonSerializer(allow_null=True)
    certificates = CertificateSerializer(many=True)
