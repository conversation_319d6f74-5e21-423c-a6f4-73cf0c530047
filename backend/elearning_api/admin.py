from django.contrib import admin
from .models import (
    Subject, Grade, Course, Enrollment, Lesson, LessonProgress,
    Quiz, Question, QuizAttempt, QuizAnswer, Assignment, AssignmentSubmission,
    CourseReview, Payment, Certificate
)


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'color', 'icon', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(Grade)
class GradeAdmin(admin.ModelAdmin):
    list_display = ['name', 'order', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'description']
    ordering = ['order']


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ['title', 'subject', 'grade', 'teacher',
                    'difficulty', 'price', 'status', 'is_published', 'created_at']
    list_filter = ['subject', 'grade', 'difficulty',
                   'status', 'is_published', 'created_at']
    search_fields = ['title', 'description', 'teacher__username',
                     'teacher__first_name', 'teacher__last_name']
    readonly_fields = ['created_at', 'updated_at', 'published_at']
    ordering = ['-created_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'thumbnail', 'subject', 'grade', 'difficulty')
        }),
        ('Teacher & Pricing', {
            'fields': ('teacher', 'price', 'original_price')
        }),
        ('Course Details', {
            'fields': ('duration', 'estimated_time', 'language', 'has_certificate', 'max_students')
        }),
        ('Content', {
            'fields': ('objectives', 'prerequisites', 'features', 'syllabus'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('status', 'is_published', 'published_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Enrollment)
class EnrollmentAdmin(admin.ModelAdmin):
    list_display = ['student', 'course', 'status',
                    'progress_percentage', 'paid_amount', 'enrolled_at']
    list_filter = ['status', 'course__subject', 'enrolled_at']
    search_fields = ['student__username', 'student__first_name',
                     'student__last_name', 'course__title']
    readonly_fields = ['enrolled_at', 'completed_at', 'last_accessed']
    ordering = ['-enrolled_at']


@admin.register(Lesson)
class LessonAdmin(admin.ModelAdmin):
    list_display = ['title', 'course', 'content_type',
                    'duration', 'order', 'is_preview', 'is_required']
    list_filter = ['content_type', 'is_preview',
                   'is_required', 'course__subject']
    search_fields = ['title', 'description', 'course__title']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['course', 'order']


@admin.register(LessonProgress)
class LessonProgressAdmin(admin.ModelAdmin):
    list_display = ['student', 'lesson', 'is_completed',
                    'completion_percentage', 'time_spent']
    list_filter = ['is_completed', 'lesson__course__subject']
    search_fields = ['student__username',
                     'lesson__title', 'lesson__course__title']
    readonly_fields = ['started_at', 'completed_at', 'last_accessed']


@admin.register(Quiz)
class QuizAdmin(admin.ModelAdmin):
    list_display = ['title', 'course', 'duration',
                    'max_attempts', 'passing_score', 'order']
    list_filter = ['course__subject', 'is_required']
    search_fields = ['title', 'description', 'course__title']
    ordering = ['course', 'order']


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ['quiz', 'question_type', 'points', 'order']
    list_filter = ['question_type', 'quiz__course__subject']
    search_fields = ['question_text', 'quiz__title']
    ordering = ['quiz', 'order']


@admin.register(QuizAttempt)
class QuizAttemptAdmin(admin.ModelAdmin):
    list_display = ['student', 'quiz', 'attempt_number',
                    'status', 'score', 'started_at']
    list_filter = ['status', 'quiz__course__subject']
    search_fields = ['student__username', 'quiz__title']
    readonly_fields = ['started_at', 'submitted_at', 'expires_at']


@admin.register(Assignment)
class AssignmentAdmin(admin.ModelAdmin):
    list_display = ['title', 'course', 'assignment_type',
                    'max_score', 'due_date', 'order']
    list_filter = ['assignment_type',
                   'course__subject', 'allow_late_submission']
    search_fields = ['title', 'description', 'course__title']
    ordering = ['course', 'order']


@admin.register(AssignmentSubmission)
class AssignmentSubmissionAdmin(admin.ModelAdmin):
    list_display = ['student', 'assignment',
                    'status', 'score', 'submitted_at', 'is_late']
    list_filter = ['status', 'assignment__course__subject']
    search_fields = ['student__username', 'assignment__title']
    readonly_fields = ['created_at', 'submitted_at', 'graded_at', 'is_late']


@admin.register(CourseReview)
class CourseReviewAdmin(admin.ModelAdmin):
    list_display = ['student', 'course', 'rating',
                    'is_verified', 'is_featured', 'created_at']
    list_filter = ['rating', 'is_verified', 'is_featured', 'course__subject']
    search_fields = ['student__username', 'course__title', 'title', 'comment']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['payment_id', 'student', 'course',
                    'amount', 'payment_method', 'status', 'created_at']
    list_filter = ['payment_method', 'status', 'created_at']
    search_fields = ['payment_id', 'student__username',
                     'course__title', 'external_transaction_id']
    readonly_fields = ['payment_id', 'created_at',
                       'processed_at', 'completed_at']


@admin.register(Certificate)
class CertificateAdmin(admin.ModelAdmin):
    list_display = ['certificate_number', 'student', 'course',
                    'final_score', 'completion_date', 'is_verified']
    list_filter = ['is_verified', 'course__subject', 'issued_at']
    search_fields = ['certificate_number',
                     'student__username', 'course__title']
    readonly_fields = ['certificate_id', 'certificate_number', 'issued_at']
