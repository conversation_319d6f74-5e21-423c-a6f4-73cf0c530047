# Generated by Django 4.2.6 on 2025-08-27 11:47

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('elearning_api', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='QuestionBank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Trắc nghiệm'), ('true_false', 'Đúng/Sai'), ('short_answer', 'Trả lời ngắn'), ('essay', 'Tự luận')], default='multiple_choice', max_length=20)),
                ('question_text', models.TextField()),
                ('explanation', models.TextField(blank=True)),
                ('points', models.FloatField(default=1.0, validators=[django.core.validators.MinValueValidator(0)])),
                ('difficulty', models.CharField(choices=[('easy', 'Dễ'), ('medium', 'Trung bình'), ('hard', 'Khó')], default='medium', max_length=10)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('options', models.JSONField(blank=True, default=list)),
                ('correct_answers', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grade', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='elearning_api.grade')),
                ('subject', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='elearning_api.subject')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_bank', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='question',
            name='question_bank',
            field=models.ForeignKey(blank=True, help_text='Reference to question in question bank', null=True, on_delete=django.db.models.deletion.CASCADE, to='elearning_api.questionbank'),
        ),
    ]
