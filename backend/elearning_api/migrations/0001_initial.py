# Generated by Django 4.2.6 on 2025-08-26 16:27

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import elearning_api.models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to=elearning_api.models.upload_course_thumbnail)),
                ('difficulty', models.CharField(choices=[('beginner', 'Cơ bản'), ('intermediate', 'Trung bình'), ('advanced', 'Nâng cao'), ('expert', '<PERSON><PERSON><PERSON><PERSON> sâu')], default='beginner', max_length=20)),
                ('price', models.DecimalField(decimal_places=0, default=0, max_digits=10)),
                ('original_price', models.DecimalField(decimal_places=0, default=0, max_digits=10)),
                ('duration', models.CharField(max_length=100)),
                ('estimated_time', models.IntegerField(default=0)),
                ('language', models.CharField(default='Tiếng Việt', max_length=50)),
                ('has_certificate', models.BooleanField(default=True)),
                ('objectives', models.JSONField(blank=True, default=list)),
                ('prerequisites', models.JSONField(blank=True, default=list)),
                ('features', models.JSONField(blank=True, default=list)),
                ('syllabus', models.JSONField(blank=True, default=list)),
                ('status', models.CharField(choices=[('draft', 'Bản nháp'), ('published', 'Đã xuất bản'), ('archived', 'Đã lưu trữ')], default='draft', max_length=20)),
                ('is_published', models.BooleanField(default=False)),
                ('max_students', models.IntegerField(default=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('active', 'Đang học'), ('completed', 'Hoàn thành'), ('dropped', 'Đã bỏ học'), ('suspended', 'Tạm dừng')], default='active', max_length=20)),
                ('paid_amount', models.DecimalField(decimal_places=0, default=0, max_digits=10)),
                ('payment_method', models.CharField(blank=True, max_length=50)),
                ('payment_date', models.DateTimeField(blank=True, null=True)),
                ('progress_percentage', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('completed_lessons', models.IntegerField(default=0)),
                ('total_time_spent', models.IntegerField(default=0)),
                ('enrolled_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='elearning_api.course')),
                ('student', models.ForeignKey(limit_choices_to={'profile__is_student': True}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-enrolled_at'],
                'unique_together': {('student', 'course')},
            },
        ),
        migrations.CreateModel(
            name='Grade',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('order', models.IntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Lesson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('content_type', models.CharField(choices=[('text', 'Nội dung văn bản'), ('video', 'Video'), ('file', 'Tài liệu')], default='text', max_length=20)),
                ('content', models.TextField(blank=True)),
                ('video_url', models.URLField(blank=True)),
                ('file', models.FileField(blank=True, null=True, upload_to=elearning_api.models.upload_lesson_content)),
                ('duration', models.IntegerField(default=0)),
                ('order', models.IntegerField(default=0)),
                ('is_preview', models.BooleanField(default=False)),
                ('is_required', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lessons', to='elearning_api.course')),
            ],
            options={
                'ordering': ['course', 'order'],
                'unique_together': {('course', 'order')},
            },
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('duration', models.IntegerField(default=30)),
                ('max_attempts', models.IntegerField(default=3)),
                ('passing_score', models.FloatField(default=70.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('order', models.IntegerField(default=0)),
                ('shuffle_questions', models.BooleanField(default=True)),
                ('show_results_immediately', models.BooleanField(default=True)),
                ('allow_review', models.BooleanField(default=True)),
                ('is_required', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='elearning_api.course')),
            ],
            options={
                'ordering': ['course', 'order'],
                'unique_together': {('course', 'order')},
            },
        ),
        migrations.CreateModel(
            name='Subject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('color', models.CharField(default='#1976d2', max_length=7)),
                ('icon', models.CharField(blank=True, max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='QuizAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attempt_number', models.IntegerField(default=1)),
                ('status', models.CharField(choices=[('in_progress', 'Đang làm'), ('submitted', 'Đã nộp'), ('graded', 'Đã chấm'), ('expired', 'Hết giờ')], default='in_progress', max_length=20)),
                ('score', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('total_points', models.FloatField(default=0.0)),
                ('earned_points', models.FloatField(default=0.0)),
                ('time_limit', models.IntegerField()),
                ('time_spent', models.IntegerField(default=0)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField()),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.enrollment')),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='elearning_api.quiz')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
                'unique_together': {('student', 'quiz', 'attempt_number')},
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Trắc nghiệm'), ('true_false', 'Đúng/Sai'), ('short_answer', 'Trả lời ngắn'), ('essay', 'Tự luận')], default='multiple_choice', max_length=20)),
                ('question_text', models.TextField()),
                ('explanation', models.TextField(blank=True)),
                ('points', models.FloatField(default=1.0, validators=[django.core.validators.MinValueValidator(0)])),
                ('order', models.IntegerField(default=0)),
                ('options', models.JSONField(blank=True, default=list)),
                ('correct_answers', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='elearning_api.quiz')),
            ],
            options={
                'ordering': ['quiz', 'order'],
                'unique_together': {('quiz', 'order')},
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('amount', models.DecimalField(decimal_places=0, max_digits=10)),
                ('original_amount', models.DecimalField(decimal_places=0, default=0, max_digits=10)),
                ('discount_amount', models.DecimalField(decimal_places=0, default=0, max_digits=10)),
                ('payment_method', models.CharField(choices=[('credit_card', 'Thẻ tín dụng'), ('debit_card', 'Thẻ ghi nợ'), ('e_wallet', 'Ví điện tử'), ('bank_transfer', 'Chuyển khoản'), ('cash', 'Tiền mặt')], max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Chờ xử lý'), ('processing', 'Đang xử lý'), ('completed', 'Hoàn thành'), ('failed', 'Thất bại'), ('cancelled', 'Đã hủy'), ('refunded', 'Đã hoàn tiền')], default='pending', max_length=20)),
                ('external_transaction_id', models.CharField(blank=True, max_length=255)),
                ('payment_gateway', models.CharField(blank=True, max_length=50)),
                ('gateway_response', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.course')),
                ('enrollment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='elearning_api.enrollment')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='course',
            name='grade',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.grade'),
        ),
        migrations.AddField(
            model_name='course',
            name='subject',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.subject'),
        ),
        migrations.AddField(
            model_name='course',
            name='teacher',
            field=models.ForeignKey(limit_choices_to={'profile__is_teacher': True}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Assignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('assignment_type', models.CharField(choices=[('turbowarp', 'TurboWarp Project'), ('scratch', 'Scratch Project'), ('coding', 'Coding Assignment')], default='turbowarp', max_length=20)),
                ('required_features', models.JSONField(blank=True, default=list)),
                ('grading_criteria', models.JSONField(blank=True, default=list)),
                ('starter_project_url', models.URLField(blank=True)),
                ('reference_files', models.FileField(blank=True, null=True, upload_to=elearning_api.models.upload_assignment_file)),
                ('max_score', models.FloatField(default=100.0)),
                ('due_date', models.DateTimeField(blank=True, null=True)),
                ('allow_late_submission', models.BooleanField(default=True)),
                ('max_attempts', models.IntegerField(default=5)),
                ('order', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='elearning_api.course')),
            ],
            options={
                'ordering': ['course', 'order'],
                'unique_together': {('course', 'order')},
            },
        ),
        migrations.CreateModel(
            name='QuizAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('answer_data', models.JSONField(default=dict)),
                ('is_correct', models.BooleanField(blank=True, null=True)),
                ('points_earned', models.FloatField(default=0.0)),
                ('answered_at', models.DateTimeField(auto_now=True)),
                ('attempt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='elearning_api.quizattempt')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.question')),
            ],
            options={
                'unique_together': {('attempt', 'question')},
            },
        ),
        migrations.CreateModel(
            name='LessonProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_completed', models.BooleanField(default=False)),
                ('time_spent', models.IntegerField(default=0)),
                ('completion_percentage', models.FloatField(default=0.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.enrollment')),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.lesson')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_accessed'],
                'unique_together': {('student', 'lesson')},
            },
        ),
        migrations.CreateModel(
            name='CourseReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('title', models.CharField(blank=True, max_length=255)),
                ('comment', models.TextField(blank=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('is_featured', models.BooleanField(default=False)),
                ('helpful_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='elearning_api.course')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.enrollment')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('student', 'course')},
            },
        ),
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('certificate_id', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('certificate_number', models.CharField(max_length=50, unique=True)),
                ('final_score', models.FloatField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('completion_date', models.DateTimeField()),
                ('is_verified', models.BooleanField(default=True)),
                ('verification_url', models.URLField(blank=True)),
                ('certificate_file', models.FileField(blank=True, null=True, upload_to='certificates/')),
                ('issued_at', models.DateTimeField(auto_now_add=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.course')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.enrollment')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-issued_at'],
                'unique_together': {('student', 'course')},
            },
        ),
        migrations.CreateModel(
            name='AssignmentSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_url', models.URLField()),
                ('description', models.TextField(blank=True)),
                ('submission_files', models.FileField(blank=True, null=True, upload_to=elearning_api.models.upload_submission_file)),
                ('status', models.CharField(choices=[('draft', 'Bản nháp'), ('submitted', 'Đã nộp'), ('graded', 'Đã chấm'), ('returned', 'Trả lại')], default='draft', max_length=20)),
                ('score', models.FloatField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('feedback', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='elearning_api.assignment')),
                ('enrollment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='elearning_api.enrollment')),
                ('graded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='graded_submissions', to=settings.AUTH_USER_MODEL)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-submitted_at'],
                'unique_together': {('student', 'assignment')},
            },
        ),
    ]
