# Hướng dẫn chạy Tests cho Bee STEM Solutions

Dự án BeE STEM Solutions sử dụng Django's testing framework để kiểm thử các thành phần khác nhau của ứng dụng. Dưới đây là hướng dẫn để chạy các tests.

## Cài đặt môi trường

Đảm bảo bạn đã cài đặt tất cả các dependencies cần thiết:

```bash
pip3 install -r requirements.txt
```

## Cấu trúc Tests

Tests được tổ chức trong các file tests.py của mỗi ứng dụng:

-   `product_api/tests.py`: Tests cho Product API

    -   Tests cho models (Category, Tag, Unit, Inventory)
    -   Tests cho views và API endpoints (CategoryView, ProductView)

-   `shopping_api/tests.py`: Tests cho Shopping API

    -   Tests cho models (Cart, CartDetail)
    -   Tests cho views và API endpoints (HomeView, AddToCart)

-   `blog_api/tests.py`: Tests cho Blog API

    -   Tests cho models (BlogCategory, BlogTag, BlogPost)
    -   Tests cho views và API endpoints (BlogCategoryView)

-   `auth_api/tests.py`: Tests cho Auth API
    -   Tests cho views và API endpoints (ProfileView, RegisterUser)

## Chạy tất cả tests

Để chạy tất cả các tests, sử dụng lệnh sau:

```bash
python3 manage.py test
```

## Chạy tests cho một ứng dụng cụ thể

Để chạy tests cho một ứng dụng cụ thể, sử dụng lệnh sau:

```bash
python3 manage.py test product_api
python3 manage.py test shopping_api
python3 manage.py test blog_api
python3 manage.py test auth_api
```

## Chạy một test cụ thể

Để chạy một test class cụ thể, sử dụng lệnh sau:

```bash
python3 manage.py test product_api.tests.InventoryModelTest
```

Để chạy một test method cụ thể, sử dụng lệnh sau:

```bash
python3 manage.py test product_api.tests.InventoryModelTest.test_inventory_creation_with_manual_sku
```

## Xem báo cáo coverage

Để xem báo cáo coverage, bạn cần cài đặt package `coverage`:

```bash
python3 -m pip install coverage
```

Sau đó, chạy tests với coverage:

```bash
coverage run --source='.' manage.py test
coverage report
```

Để tạo báo cáo HTML chi tiết:

```bash
coverage html
```

Sau đó, mở file `htmlcov/index.html` trong trình duyệt để xem báo cáo.

## Lưu ý

-   Một số tests có thể yêu cầu môi trường cụ thể hoặc dữ liệu mẫu. Đảm bảo bạn đã thiết lập đúng môi trường trước khi chạy tests.
-   Một số tests có thể sử dụng mocking để giả lập các dịch vụ bên ngoài như reCAPTCHA. Đảm bảo bạn đã cài đặt các dependencies cần thiết.
-   Nếu bạn gặp lỗi khi chạy tests, hãy kiểm tra logs và đảm bảo rằng database settings của bạn đã được cấu hình đúng cho testing.
-   Nếu bạn gặp lỗi `ImportError: 'tests' module incorrectly imported`, hãy đảm bảo rằng bạn đã xóa các thư mục `tests` và sử dụng các file `tests.py` trong thư mục gốc của mỗi ứng dụng.

## Viết thêm tests

Khi thêm tính năng mới vào dự án, hãy đảm bảo viết tests cho các tính năng đó. Bạn có thể sử dụng các tests hiện có làm mẫu.

Để biết thêm thông tin về cách viết tests trong Django, hãy tham khảo [Django Testing Documentation](https://docs.djangoproject.com/en/4.2/topics/testing/).
