version: 2.1

commands:
    install_ansible:
        description: Install Ansible
        steps:
            - run:
                  name: Install Ansible
                  command: |
                      sudo apt-get update
                      sudo apt-get install -y python3-pip
                      pip3 install ansible

jobs:
    # test-backend:
    #   docker:
    #     - image: cimg/python:3.9.11
    #   steps:
    #     - checkout
    #     - run:
    #         name: add config.json to test
    #         command: |
    #           echo '{"env": "dev",' >> BeeBot_IDE/config/config.json
    #           echo '"DEBUG": true,' >> BeeBot_IDE/config/config.json
    #           echo '"ALLOWED_HOSTS": ["localhost"],' >> BeeBot_IDE/config/config.json
    #           echo '"CSRF_TRUSTED_ORIGINS": ["https://beeblock.vn","http://beeblock.vn"],' >> BeeBot_IDE/config/config.json
    #           echo '"DATABASES": {"ENGINE": "django.db.backends.postgresql_psycopg2","NAME": "beeblock_test1","USER": "lqdung","PASSWORD": "LqDung0914478982d","HOST": "**************","PORT": "5432", "TEST": {"NAME": "beeblock_test1"}},' >> BeeBot_IDE/config/config.json
    #           echo '"STATIC_ROOT": "",' >> BeeBot_IDE/config/config.json
    #           echo '"ADMIN_ROOT": "",' >> BeeBot_IDE/config/config.json
    #           echo '"email": {' >> BeeBot_IDE/config/config.json
    #           echo '"EMAIL_BACKEND": "",' >> BeeBot_IDE/config/config.json
    #           echo '"EMAIL_HOST": "",' >> BeeBot_IDE/config/config.json
    #           echo '"EMAIL_USE_TLS": true,' >> BeeBot_IDE/config/config.json
    #           echo '"EMAIL_USE_SSL": false,' >> BeeBot_IDE/config/config.json
    #           echo '"EMAIL_PORT": 587,' >> BeeBot_IDE/config/config.json
    #           echo '"EMAIL_HOST_USER": "",' >> BeeBot_IDE/config/config.json
    #           echo '"EMAIL_HOST_PASSWORD": ""}' >> BeeBot_IDE/config/config.json
    #           echo '}' >> BeeBot_IDE/config/config.json
    #           pwd
    #           ls BeeBot_IDE/config
    #           cat BeeBot_IDE/config/config.json
    #     - run:
    #         name: install dependencies and migrate db
    #         command: |
    #           python -m pip install -r requirements.txt
    #           cd BeeBot_IDE
    #           python manage.py makemigrations
    #           python manage.py migrate
    #           python manage.py test --keepdb --verbosity=2

    deploy-vps:
        docker:
            - image: cimg/base:stable
        steps:
            - checkout
            - install_ansible
            - add_ssh_keys:
                  fingerprints:
                      ["SHA256:vzJX20phP69PLkFKzNpS43QTtcHUKbOiHao+p/RTLqw"]
            - run:
                  name: Configure server
                  command: |
                      cd .circleci/ansible
                      cat vps-ip.txt
                      ansible-playbook -i vps-ip.txt ssh-vps.yml

workflows:
    BeE_CI:
        jobs:
            # - test-backend
            - deploy-vps:
                  # requires: [test-backend]
                  filters:
                      branches:
                          only: [develop]
